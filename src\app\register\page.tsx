"use client"

import { RegisterForm } from "@/components/register-form"
import { ThemeSwitcher } from "@/components/theme-switcher"
import PublicRoute from "@/components/auth/PublicRoute"

export default function RegisterPage() {
  return (
    <PublicRoute>
      <div className="bg-muted flex min-h-svh flex-col items-center justify-center p-6 md:p-10 relative">
        <div className="absolute top-4 right-4">
          <ThemeSwitcher />
        </div>
        <div className="w-full max-w-sm md:max-w-md">
          <RegisterForm />
        </div>
      </div>
    </PublicRoute>
  )
}
