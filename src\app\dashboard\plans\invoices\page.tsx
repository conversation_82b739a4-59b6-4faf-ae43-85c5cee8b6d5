"use client";

import React, { useEffect, useState, useRef } from "react";
import { AppSidebar } from "@/components/app-sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AlertTriangle, Download, ExternalLink, Loader2, Info } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";
import { Toaster } from "@/components/ui/sonner";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

// Define types based on the API response
interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  price: number;
}

interface InvoiceTotal {
  type: string;
  description: string;
  amount: number;
}

interface Invoice {
  id: string;
  userId: string;
  planId: string;
  items: InvoiceItem[];
  totals: InvoiceTotal[];
  paidDate: string | null;
  metadata: any;
  status: string;
  invoiceCode: string;
  invoiceLink: string | null;
  paymentToken: string | null;
  createdAt: string;
  updatedAt: string;
}

interface PaginatedResponse<T> {
  data: T[];
  totalItems: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
}

// Declare Midtrans Snap as a global variable
declare global {
  interface Window {
    snap?: {
      pay: (token: string, options: any) => void;
    };
  }
}

export default function InvoicesPage() {
  const { accessToken } = useAuth();
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const midtransScriptLoaded = useRef(false);

  // Fetch user's invoices
  const fetchInvoices = async () => {
    if (!accessToken) {
      setIsLoading(false);
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/plans/invoices?page=${currentPage}&limit=${itemsPerPage}`,
        {
          headers: { Authorization: `Bearer ${accessToken}` },
        }
      );
      
      if (!response.ok) {
        throw new Error("Gagal mengambil data riwayat pembayaran.");
      }
      
      const data: PaginatedResponse<Invoice> = await response.json();
      setInvoices(data.data);
      setTotalPages(data.totalPages);
      setCurrentPage(data.currentPage);
    } catch (err: any) {
      console.error("Error fetching invoices:", err);
      setError(err.message);
      setInvoices([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Format price to IDR
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
      minimumFractionDigits: 0,
    }).format(price);
  };

  // Format date to readable format
  const formatDate = (dateString: string | null) => {
    if (!dateString) return "-";
    return new Date(dateString).toLocaleDateString("id-ID", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  // Validate payment with backend
  const validatePayment = async (paymentResult: any, invoiceId: string) => {
    if (!accessToken) return;
    
    try {
      // Extract transaction_id and order_id from Midtrans result
      const transactionId = paymentResult.transaction_id;
      const orderId = paymentResult.order_id;
      
      if (!transactionId || !orderId) {
        console.error("Missing transaction_id or order_id in payment result");
        return;
      }
      
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/plans/invoices/validate`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${accessToken}`
          },
          body: JSON.stringify({
            invoiceId: invoiceId,
            transactionId: transactionId,
            orderId: orderId
          })
        }
      );
      
      if (!response.ok) {
        throw new Error("Gagal memvalidasi pembayaran.");
      }
      
      console.log("Payment validation sent successfully");
      return await response.json();
    } catch (err) {
      console.error("Error validating payment:", err);
      // Don't show error to user since payment was already successful
      return null;
    }
  };

  // Process payment with Midtrans Snap
  const processPayment = (paymentToken: string, invoiceId: string) => {
    if (!window.snap || !midtransScriptLoaded.current) {
      toast.error("Payment gateway tidak tersedia. Silakan coba lagi nanti.");
      return;
    }

    setIsProcessingPayment(true);
    
    try {
      window.snap.pay(paymentToken, {
        onSuccess: function(result: any) {
          // Validate payment with backend
          validatePayment(result, invoiceId).then(response => {
            if (response) {
              toast.success("Pembayaran berhasil divalidasi!");
            } else {
              toast.success("Pembayaran berhasil! Validasi sedang diproses.");
            }
            fetchInvoices(); // Refresh invoices after successful payment
          });
        },
        onPending: function(result: any) {
          toast.info("Pembayaran dalam proses");
          fetchInvoices(); // Refresh to get updated status
        },
        onError: function(result: any) {
          toast.error("Pembayaran gagal. Silakan coba lagi.");
        },
        onClose: function() {
          setIsProcessingPayment(false);
        }
      });
    } catch (error) {
      console.error("Error processing payment:", error);
      toast.error("Terjadi kesalahan saat memproses pembayaran.");
      setIsProcessingPayment(false);
    }
  };

  // Get status badge color
  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case "paid":
        return <Badge className="bg-green-500">Lunas</Badge>;
      case "pending":
        return <Badge className="bg-yellow-500">Menunggu Pembayaran</Badge>;
      case "cancelled":
        return <Badge className="bg-red-500">Dibatalkan</Badge>;
      case "refunded":
        return <Badge className="bg-blue-500">Dikembalikan</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  // Load Midtrans Snap script
  useEffect(() => {
    if (!midtransScriptLoaded.current) {
      const script = document.createElement('script');
      script.src = process.env.NEXT_PUBLIC_MIDTRANS_SNAP_URL || 'https://app.sandbox.midtrans.com/snap/snap.js';
      script.setAttribute('data-client-key', process.env.NEXT_PUBLIC_MIDTRANS_CLIENT_KEY || ''); // Client key from env
      script.async = true;
      script.onload = () => {
        midtransScriptLoaded.current = true;
      };
      document.body.appendChild(script);

      return () => {
        document.body.removeChild(script);
      };
    }
  }, []);

  useEffect(() => {
    fetchInvoices();
  }, [accessToken, currentPage, itemsPerPage]);

  return (
    <SidebarProvider>
      <Toaster />
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b">
          <div className="flex items-center gap-2 px-4 w-full overflow-x-auto scrollbar-hide">
            <SidebarTrigger className="-ml-1 flex-shrink-0" />
            <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4 flex-shrink-0" />
            <Breadcrumb className="overflow-x-auto scrollbar-hide">
              <BreadcrumbList>
                <BreadcrumbItem className="flex-shrink-0">
                  <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="flex-shrink-0" />
                <BreadcrumbItem className="flex-shrink-0">
                  <BreadcrumbLink href="/dashboard/plans">Paket Layanan</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="flex-shrink-0" />
                <BreadcrumbItem className="flex-shrink-0">
                  <BreadcrumbPage>Riwayat Pembayaran</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>

        <main className="flex-1 p-4 md:p-6 space-y-6">
          <div className="flex flex-col gap-2">
            <h1 className="text-3xl font-bold tracking-tight">Riwayat Pembayaran</h1>
            <p className="text-muted-foreground">
              Lihat riwayat pembayaran dan unduh invoice Anda
            </p>
          </div>

          {/* Payment Method Info */}
          <Card className="border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-950/20">
            <CardHeader className="flex flex-row items-center space-x-3 pb-2">
              <Info className="h-5 w-5 text-amber-600 dark:text-amber-400" />
              <CardTitle className="text-amber-800 dark:text-amber-200">Informasi Metode Pembayaran</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-amber-700 dark:text-amber-300 text-sm leading-relaxed">
                <strong>Ingin mengganti metode pembayaran?</strong> Jika Anda sudah memilih metode pembayaran sebelumnya dan ingin menggantinya,
                Anda dapat membuat invoice baru dengan kembali ke halaman{" "}
                <a href="/dashboard/plans" className="underline font-medium hover:text-amber-900 dark:hover:text-amber-100">
                  Paket Layanan
                </a>{" "}
                dan memilih paket yang diinginkan. Invoice baru akan dibuat dan Anda dapat memilih metode pembayaran yang berbeda.
              </p>
            </CardContent>
          </Card>

          {/* Error State */}
          {error && (
            <Card className="border-destructive bg-destructive/10">
              <CardHeader className="flex flex-row items-center space-x-3 pb-2">
                <AlertTriangle className="h-6 w-6 text-destructive" />
                <CardTitle className="text-destructive">Gagal Memuat Data</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-destructive/80">Terjadi kesalahan: {error}</p>
                <Button variant="outline" size="sm" onClick={fetchInvoices} className="mt-4">
                  Coba Lagi
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Invoices Table */}
          <Card>
            <CardHeader>
              <CardTitle>Invoice</CardTitle>
              <CardDescription>
                Daftar semua transaksi pembayaran Anda
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-4">
                  <Skeleton className="h-10 w-full" />
                  {Array.from({ length: 5 }).map((_, index) => (
                    <Skeleton key={index} className="h-16 w-full" />
                  ))}
                </div>
              ) : invoices.length === 0 ? (
                <div className="text-center py-6">
                  <p className="text-muted-foreground">Belum ada riwayat pembayaran</p>
                </div>
              ) : (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>No. Invoice</TableHead>
                        <TableHead>Tanggal</TableHead>
                        <TableHead>Deskripsi</TableHead>
                        <TableHead>Total</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Aksi</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {invoices.map((invoice) => {
                        // Get the first item description as the main description
                        const mainDescription = invoice.items[0]?.description || "Langganan";
                        // Get the total amount
                        const totalAmount = invoice.totals.find(t => t.type === "total")?.amount || 0;
                        
                        return (
                          <TableRow key={invoice.id}>
                            <TableCell className="font-medium">{invoice.invoiceCode}</TableCell>
                            <TableCell>{formatDate(invoice.createdAt)}</TableCell>
                            <TableCell>{mainDescription}</TableCell>
                            <TableCell>{formatPrice(totalAmount)}</TableCell>
                            <TableCell>{getStatusBadge(invoice.status)}</TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-end gap-2">
                                {invoice.invoiceLink && (
                                  <Button 
                                    variant="outline" 
                                    size="sm" 
                                    onClick={() => invoice.invoiceLink && window.open(invoice.invoiceLink, "_blank")}
                                  >
                                    <Download className="h-4 w-4 mr-1" />
                                    Invoice
                                  </Button>
                                )}
                                {invoice.paymentToken && invoice.status.toLowerCase() === "pending" && (
                                  <Button 
                                    variant="default" 
                                    size="sm" 
                                    onClick={() => invoice.paymentToken && processPayment(invoice.paymentToken, invoice.id)}
                                    disabled={isProcessingPayment}
                                  >
                                    {isProcessingPayment ? (
                                      <>
                                        <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                                        Memproses...
                                      </>
                                    ) : (
                                      <>
                                        <ExternalLink className="h-4 w-4 mr-1" />
                                        Bayar
                                      </>
                                    )}
                                  </Button>
                                )}
                              </div>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
            {totalPages > 1 && (
              <CardFooter className="flex justify-center">
                <Pagination>
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious 
                        href="#" 
                        onClick={(e) => {
                          e.preventDefault();
                          if (currentPage > 1) setCurrentPage(currentPage - 1);
                        }}
                        className={currentPage <= 1 ? "pointer-events-none opacity-50" : ""}
                      />
                    </PaginationItem>
                    
                    {Array.from({ length: totalPages }, (_, i) => i + 1)
                      .filter(page => {
                        // Show fewer pages on mobile
                        const isMobile = window.innerWidth < 640;
                        if (isMobile) {
                          return page === 1 || 
                                page === totalPages || 
                                page === currentPage;
                        }
                        // Show more pages on desktop
                        return page === 1 || 
                              page === totalPages || 
                              (page >= currentPage - 1 && page <= currentPage + 1);
                      })
                      .map((page, index, array) => {
                        // Add ellipsis
                        const showEllipsisBefore = index > 0 && array[index - 1] !== page - 1;
                        const showEllipsisAfter = index < array.length - 1 && array[index + 1] !== page + 1;
                        
                        return (
                          <React.Fragment key={page}>
                            {showEllipsisBefore && (
                              <PaginationItem className="hidden sm:flex">
                                <span className="px-4 py-2">...</span>
                              </PaginationItem>
                            )}
                            
                            <PaginationItem>
                              <PaginationLink 
                                href="#" 
                                onClick={(e) => {
                                  e.preventDefault();
                                  setCurrentPage(page);
                                }}
                                isActive={page === currentPage}
                              >
                                {page}
                              </PaginationLink>
                            </PaginationItem>
                            
                            {showEllipsisAfter && (
                              <PaginationItem className="hidden sm:flex">
                                <span className="px-4 py-2">...</span>
                              </PaginationItem>
                            )}
                          </React.Fragment>
                        );
                      })}
                    
                    <PaginationItem>
                      <PaginationNext 
                        href="#" 
                        onClick={(e) => {
                          e.preventDefault();
                          if (currentPage < totalPages) setCurrentPage(currentPage + 1);
                        }}
                        className={currentPage >= totalPages ? "pointer-events-none opacity-50" : ""}
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </CardFooter>
            )}
          </Card>
        </main>
      </SidebarInset>
    </SidebarProvider>
  );
}
