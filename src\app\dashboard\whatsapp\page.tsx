"use client";

import React, { useEffect, useState, useCallback } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { AppSidebar } from '@/components/app-sidebar';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Separator } from '@/components/ui/separator';
import { SidebarInset, SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar';
import { PlusCircle, Trash2, Edit, Settings, AlertTriangle, Loader2, <PERSON>, Plus, MessageSquare, History } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Toaster } from "@/components/ui/sonner";
import { toast } from "sonner";

interface WhatsAppInstance {
  id: string;
  name: string;
  phoneNumber: string;
  status: 'DISCONNECTED' | 'CONNECTED' | 'INITIALIZING' | 'QR_PENDING' | 'ERROR' | string; // string for other potential statuses
  lastConnected?: string;
}

export default function WhatsAppInstancesPage() {
  const { accessToken } = useAuth();
  const [instances, setInstances] = useState<WhatsAppInstance[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [instanceToDelete, setInstanceToDelete] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const fetchInstances = useCallback(async () => {
    if (!accessToken) {
      setIsLoading(false);
      setError("Autentikasi diperlukan.");
      return;
    }
    setIsLoading(true);
    setError(null);
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/whatsapp`, {
        headers: { Authorization: `Bearer ${accessToken}` },
      });
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Gagal mengambil data WhatsApp.' }));
        throw new Error(errorData.message || 'Gagal mengambil data WhatsApp.');
      }
      const data: WhatsAppInstance[] = await response.json();
      setInstances(data);
    } catch (err: any) {
      setError(err.message);
      setInstances([]);
    } finally {
      setIsLoading(false);
    }
  }, [accessToken]);

  // Check for toast messages in sessionStorage
  useEffect(() => {
    // Check if there's a toast message stored in sessionStorage
    const storedToast = sessionStorage.getItem('whatsappToast');
    if (storedToast) {
      // Display the toast message
      toast.success(storedToast);
      // Remove the message from sessionStorage to prevent showing it again on refresh
      sessionStorage.removeItem('whatsappToast');
    }
  }, []);

  useEffect(() => {
    fetchInstances();
  }, [fetchInstances]);

  const handleDeleteInstance = async (instanceId: string) => {
    // Open the confirmation dialog
    setInstanceToDelete(instanceId);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!instanceToDelete || !accessToken) return;
    
    setIsDeleting(true);
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/whatsapp/${instanceToDelete}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Gagal menghapus WhatsApp.' }));
        throw new Error(errorData.message || `Gagal menghapus WhatsApp: ${response.status}`);
      }
      
      // Remove the deleted instance from state
      setInstances(prev => prev.filter(inst => inst.id !== instanceToDelete));
      toast.success("WhatsApp berhasil dihapus.");
    } catch (err: any) {
      toast.error(`Error: ${err.message}`);
    } finally {
      setDeleteDialogOpen(false);
      setInstanceToDelete(null);
      setIsDeleting(false);
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status.toUpperCase()) {
      case 'CONNECTED':
        return 'success';
      case 'DISCONNECTED':
        return 'destructive';
      case 'INITIALIZING':
      case 'QR_PENDING':
        return 'warning';
      default:
        return 'secondary';
    }
  };

  return (
    <SidebarProvider>
      <AppSidebar />
      <Toaster />
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Hapus WhatsApp</AlertDialogTitle>
            <AlertDialogDescription>
              Apakah Anda yakin ingin menghapus WhatsApp ini? Tindakan ini tidak dapat dibatalkan.
              Semua percakapan dan data terkait WhatsApp ini akan dihapus secara permanen.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Batal</AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                confirmDelete();
              }}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Menghapus...
                </>
              ) : (
                "Hapus"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>Pengaturan WhatsApp</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>

        <main className="flex-1 p-4 md:p-6 space-y-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-2xl font-semibold">WhatsApp</h1>
              <p className="text-muted-foreground">
                Kelola semua WhatsApp Anda di sini.
              </p>
            </div>
            <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3">
              <Link href="/dashboard/whatsapp/chats" passHref>
                <Button variant="outline" className="w-full sm:w-auto">
                  <History className="mr-2 h-4 w-4" /> Histori Percakapan
                </Button>
              </Link>
              <Link href="/dashboard/whatsapp/new" passHref>
                <Button variant="default" className="w-full sm:w-auto">
                  <PlusCircle className="mr-2 h-4 w-4" /> Tambah WhatsApp Baru
                </Button>
              </Link>
            </div>
          </div>

          {isLoading && (
            <div className="flex flex-col items-center justify-center py-20">
              <div className="relative">
                <div className="w-16 h-16 rounded-full bg-primary animate-pulse"></div>
                <MessageSquare className="absolute inset-0 m-auto h-8 w-8 text-primary-foreground animate-bounce" />
              </div>
              <p className="mt-6 text-lg font-medium text-muted-foreground">Memuat WhatsApp...</p>
            </div>
          )}

          {error && !isLoading && (
            <div className="flex flex-col items-center justify-center py-20">
              <div className="p-4 rounded-full bg-destructive/10 mb-4">
                <AlertTriangle className="h-8 w-8 text-destructive" />
              </div>
              <h3 className="text-lg font-semibold text-destructive mb-2">Terjadi Kesalahan</h3>
              <p className="text-muted-foreground text-center mb-6">{error}</p>
              <Button onClick={fetchInstances} variant="outline">
                Coba Lagi
              </Button>
            </div>
          )}

          {!isLoading && !error && instances.length === 0 && (
            <div className="flex flex-col items-center justify-center py-20">
              <div className="relative mb-8">
                <div className="w-24 h-24 rounded-3xl bg-primary/10 dark:bg-primary/20 flex items-center justify-center">
                  <MessageSquare className="h-12 w-12 text-primary" />
                </div>
                <div className="absolute -top-2 -right-2 w-8 h-8 rounded-full bg-primary flex items-center justify-center">
                  <Plus className="h-4 w-4 text-primary-foreground" />
                </div>
              </div>
              <h3 className="text-2xl font-bold mb-3">Belum Ada WhatsApp</h3>
              <p className="text-muted-foreground text-center max-w-md mb-8">
                Mulai dengan menghubungkan WhatsApp pertama Anda. Hubungkan nomor WhatsApp dan mulai berinteraksi dengan pengguna!
              </p>
              <Button asChild size="lg" className="bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg">
                <Link href="/dashboard/whatsapp/new">
                  <Plus className="mr-2 h-5 w-5" />
                  Tambahkan WhatsApp Pertama
                </Link>
              </Button>
            </div>
          )}

          {!isLoading && !error && instances.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Daftar WhatsApp</CardTitle>
                <CardDescription>Total {instances.length} WhatsApp ditemukan.</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nama</TableHead>
                      <TableHead>Nomor Telepon</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Terakhir Terhubung</TableHead>
                      <TableHead className="text-right">Aksi</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {instances.map((instance) => (
                      <TableRow key={instance.id}>
                        <TableCell className="font-medium">{instance.name}</TableCell>
                        <TableCell>{instance.phoneNumber}</TableCell>
                        <TableCell>
                          <Badge variant={getStatusBadgeVariant(instance.status) as any}>
                            {instance.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {instance.lastConnected ? new Date(instance.lastConnected).toLocaleString() : '-'}
                        </TableCell>
                        <TableCell className="text-right space-x-2">
                          <Link href={`/dashboard/whatsapp/${instance.id}`} passHref>
                            <Button variant="outline" size="sm">
                              <Settings className="mr-1 h-3 w-3" /> Kelola
                            </Button>
                          </Link>
                          <Button variant="destructive" size="sm" onClick={() => handleDeleteInstance(instance.id)}>
                            <Trash2 className="mr-1 h-3 w-3" /> Hapus
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )}
        </main>
      </SidebarInset>
    </SidebarProvider>
  );
}
