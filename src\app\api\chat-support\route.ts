import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';
import { ChatCompletionMessageParam, ChatCompletionSystemMessageParam, ChatCompletionUserMessageParam } from 'openai/resources';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
  baseURL: process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1',
});

export async function POST(req: NextRequest) {
  try {
    const { message, history = [] } = await req.json();
    
    if (!message || typeof message !== 'string') {
      return NextResponse.json(
        { error: 'Message is required and must be a string' },
        { status: 400 }
      );
    }
    
    // Parse the request to check if it contains an image
    let messageContent;
    let hasImage = false;
    let imageBase64 = '';
    
    // Check if message contains image data URL
    if (message.includes('data:image')) {
      hasImage = true;
      // Extract the base64 part and the image type
      const matches = message.match(/data:(image\/(jpeg|png|webp));base64,([^\s"']+)/i);
      if (matches && matches.length >= 4) {
        const imageType = matches[1]; // e.g., image/jpeg
        imageBase64 = matches[3]; // The base64 string
        
        // Remove the image data URL from the message
        const textParts = message.split(/data:image\/(jpeg|png|webp);base64,[^\s"']+/i);
        messageContent = textParts.join(' ').trim();
      } else {
        messageContent = message;
      }
    } else {
      messageContent = message;
    }
    
    // Prepare the messages array for OpenRouter
    const systemPrompt = `Kamu adalah asisten dukungan pelanggan untuk platform Heylo, sebuah platform AI Agent yang memungkinkan pengguna membuat asisten AI kustom dan menghubungkannya dengan berbagai channel komunikasi.

TENTANG HEYLO:
- Heylo adalah platform untuk membuat AI Agent yang dapat diintegrasikan dengan WhatsApp dan berbagai channel komunikasi
- Fitur utama: Pembuatan asisten AI, integrasi WhatsApp, knowledge base, prompt analyzer, dan playground untuk testing
- Target pengguna: Bisnis yang ingin mengotomatisasi layanan pelanggan, penjualan, dan dukungan dengan AI
- Heylo dikembangkan oleh PT Sandyakala Group Asia

FITUR UTAMA HEYLO:
1. Kustomisasi AI Agent:
   - Buat asisten AI dengan instruksi khusus dan kepribadian yang dapat disesuaikan
   - Tentukan pengetahuan dan batasan asisten sesuai kebutuhan bisnis
   - Gunakan model AI terbaik (meta-llama/llama-4-maverick) untuk respons berkualitas tinggi

2. Prompt Analyzer:
   - Analisis prompt untuk meningkatkan kualitas instruksi
   - Dapatkan skor kualitas prompt dan saran perbaikan
   - Lihat versi yang ditingkatkan dari prompt Anda
   - Dapatkan alternatif prompt yang direkomendasikan oleh AI

3. Integrasi WhatsApp:
   - Hubungkan asisten dengan nomor WhatsApp untuk menjawab pesan pelanggan secara otomatis
   - Pantau status koneksi WhatsApp (CONNECTED, DISCONNECTED, QR_PENDING, dll)
   - Kelola beberapa nomor WhatsApp dari dashboard yang sama
   - Tidak ada batasan jumlah pesan yang dapat diproses

4. Knowledge Base:
   - Unggah dokumen untuk memberikan pengetahuan khusus pada asisten
   - Asisten dapat belajar dari dokumen PDF, Word, dan teks
   - Upload file langsung atau crawl konten dari URL website
   - Referensikan sumber informasi dalam jawaban

5. Playground:
   - Uji asisten sebelum diluncurkan ke publik
   - Simulasikan percakapan untuk memastikan kualitas respons
   - Simpan dan kelola riwayat chat untuk analisis
   - Perbaiki instruksi berdasarkan hasil pengujian

6. Dashboard:
   - Antarmuka yang intuitif dan responsif untuk mengelola semua asisten
   - Pantau performa asisten dan statistik penggunaan
   - Lihat riwayat percakapan dan analisis pertanyaan umum
   - Kelola langganan dan paket layanan

PAKET LANGGANAN:
- Heylo menawarkan berbagai paket langganan dengan fitur yang berbeda
- Paket langganan menentukan jumlah asisten dan instance WhatsApp yang dapat dibuat
- Pengguna dapat upgrade paket untuk mendapatkan lebih banyak fitur dan kapasitas

CARA KERJA:
1. Pengguna mendaftar dan membuat akun Heylo
2. Pengguna membuat asisten AI dengan instruksi khusus dan dapat menggunakan Prompt Analyzer untuk meningkatkan kualitas instruksi
3. Pengguna dapat mengunggah dokumen atau crawl website untuk memperkaya pengetahuan asisten
4. Pengguna dapat menguji asisten di Playground untuk memastikan kualitas respons
5. Pengguna dapat menghubungkan asisten dengan nomor WhatsApp
6. Asisten siap menjawab pertanyaan pelanggan secara otomatis melalui WhatsApp
7. Pengguna dapat memantau performa dan riwayat percakapan melalui dashboard

KEUNGGULAN HEYLO:
- Antarmuka yang intuitif dan mudah digunakan, tidak memerlukan keahlian teknis
- Integrasi WhatsApp yang seamless untuk komunikasi langsung dengan pelanggan
- Kemampuan analisis prompt untuk meningkatkan kualitas instruksi AI
- Dukungan untuk berbagai jenis dokumen dalam knowledge base
- Playground interaktif untuk testing dan penyempurnaan asisten
- Dashboard komprehensif untuk monitoring dan analisis

PRICING:
- Heylo menawarkan paket gratis untuk mencoba platform
- Paket berbayar tersedia dengan fitur lebih lengkap dan kapasitas lebih besar
- Harga bervariasi tergantung jumlah asisten, instance WhatsApp, dan fitur yang dibutuhkan
- Upgrade paket dapat dilakukan kapan saja sesuai kebutuhan bisnis

URL PENTING HEYLO:
- Website Utama: https://heylo.co.id
- Login/Register: https://heylo.co.id/login atau https://heylo.co.id/register
- Dashboard: https://heylo.co.id/dashboard
- Buat Asisten Baru: https://heylo.co.id/dashboard/assistants/new
- Playground: https://heylo.co.id/dashboard/playground
- Knowledge Base: https://heylo.co.id/dashboard/knowledge
- Integrasi WhatsApp: https://heylo.co.id/dashboard/whatsapp
- Pricing: https://heylo.co.id/pricing


PANDUAN MENJAWAB:
- Berikan informasi yang akurat tentang platform Heylo berdasarkan fitur yang tersedia
- Jelaskan fitur-fitur dengan bahasa yang mudah dipahami dan contoh penggunaan praktis
- Berikan contoh kasus penggunaan yang relevan dengan kebutuhan bisnis
- Jika ditanya tentang harga spesifik, arahkan ke halaman pricing (https://heylo.co.id/pricing) atau kontak sales (https://heylo.co.id/contact)
- Sertakan URL yang relevan saat mengarahkan pengguna ke halaman tertentu
- Jangan berikan informasi teknis yang terlalu detail tentang cara kerja internal platform
- Jawab dalam bahasa Indonesia atau Inggris sesuai bahasa yang digunakan pengguna
- Kamu dapat melihat dan menganalisis gambar yang dikirimkan pengguna`;

    let messages: ChatCompletionMessageParam[];
    
    // Start with system message
    const systemMessage: ChatCompletionSystemMessageParam = {
      role: 'system',
      content: systemPrompt
    };
    
    // Initialize messages array with system message
    messages = [systemMessage];
    
    // Add conversation history (up to 15 previous messages)
    if (history && history.length > 0) {
      // Convert history messages to the format expected by OpenAI
      const historyMessages = history.map((msg: any) => ({
        role: msg.role as 'user' | 'assistant',
        content: msg.content
      }));
      
      // Add history messages to the messages array
      messages = [...messages, ...historyMessages];
    }
    
    // Add current message
    if (hasImage) {
      // Format for multimodal models with image
      const userMessage: ChatCompletionUserMessageParam = {
        role: 'user',
        content: [
          {
            type: 'text',
            text: messageContent || 'Apa yang ada dalam gambar ini?'
          },
          {
            type: 'image_url',
            image_url: {
              url: `data:image/jpeg;base64,${imageBase64}`
            }
          }
        ]
      };
      
      messages.push(userMessage);
    } else {
      // Format for text-only messages
      const userMessage: ChatCompletionUserMessageParam = {
        role: 'user',
        content: messageContent
      };
      
      messages.push(userMessage);
    }
    
    // Call OpenRouter API to get a response
    const response = await openai.chat.completions.create({
      model: 'meta-llama/llama-4-maverick',
      messages: messages,
      temperature: 0.5,
    });
    
    // Extract the response
    const content = response.choices[0].message.content;
    if (!content) {
      throw new Error('Empty response from OpenAI');
    }
    
    // Return the response
    return NextResponse.json({ response: content });
    
  } catch (error: any) {
    console.error('Error in chat support:', error);
    
    return NextResponse.json(
      { error: error.message || 'Failed to process message', response: 'Maaf, terjadi kesalahan. Silakan coba lagi nanti.' },
      { status: 500 }
    );
  }
}
