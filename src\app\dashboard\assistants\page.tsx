"use client";

import { AppSidebar } from "@/components/app-sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Bot, Plus, Edit, Trash2, AlertCircle, Sparkles, Calendar, MessageSquare, Loader2 } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import React, { useEffect, useState } from "react";
import Link from 'next/link';
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Toaster } from "@/components/ui/sonner";

interface Assistant {
  id: string;
  name: string;
  description: string;
  instructions: string;
  createdAt: string;
  updatedAt: string;
}

export default function AssistantsPage() {
  const [assistants, setAssistants] = useState<Assistant[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { accessToken } = useAuth();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [assistantToDelete, setAssistantToDelete] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Check for toast messages in sessionStorage
  useEffect(() => {
    // Check if there's a toast message stored in sessionStorage
    const storedToast = sessionStorage.getItem('assistantToast');
    if (storedToast) {
      // Display the toast message
      toast.success(storedToast);
      // Remove the message from sessionStorage to prevent showing it again on refresh
      sessionStorage.removeItem('assistantToast');
    }
  }, []);

  useEffect(() => {
    const fetchAssistants = async () => {
      if (!accessToken) {
        setIsLoading(false);
        // setError("Authentication token not found. Please log in."); // Or rely on ProtectedRoute
        return;
      }
      setIsLoading(true);
      setError(null);
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/assistants`, {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
          },
        });
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || `Failed to fetch assistants: ${response.status}`);
        }
        const data: Assistant[] = await response.json();
        setAssistants(data);
      } catch (err: any) {
        setError(err.message || "An unexpected error occurred.");
      }
      setIsLoading(false);
    };

    fetchAssistants();
  }, [accessToken]);

  const handleDeleteAssistant = async (assistantId: string) => {
    if (!accessToken) {
      toast.error("Authentication token not found.");
      return;
    }
    
    // Open the confirmation dialog
    setAssistantToDelete(assistantId);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!assistantToDelete || !accessToken) return;
    
    setIsDeleting(true);
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/assistants/${assistantToDelete}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to delete assistant: ${response.status}`);
      }
      // Refresh the list after deletion
      setAssistants(prevAssistants => prevAssistants.filter(assistant => assistant.id !== assistantToDelete));
      toast.success("Asisten berhasil dihapus.");
    } catch (err: any) {
      toast.error(`Error: ${err.message}`);
    } finally {
      setDeleteDialogOpen(false);
      setAssistantToDelete(null);
      setIsDeleting(false);
    }
  };

  return (
    <SidebarProvider>
      <AppSidebar />
      <Toaster />
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Hapus Asisten</AlertDialogTitle>
            <AlertDialogDescription>
              Apakah Anda yakin ingin menghapus asisten ini? Tindakan ini tidak dapat dibatalkan.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Batal</AlertDialogCancel>
            <AlertDialogAction 
              onClick={(e) => {
                e.preventDefault();
                confirmDelete();
              }} 
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Menghapus...
                </>
              ) : (
                "Hapus"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>Asisten AI</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>
        
        <main className="flex-1 p-4 md:p-6 space-y-6">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h2 className="text-2xl font-bold tracking-tight">Asisten AI Anda</h2>
              <p className="text-muted-foreground">
                Kelola dan kustomisasi asisten AI untuk berbagai kebutuhan
              </p>
            </div>
            <Button asChild className="bg-primary hover:bg-primary/90 text-primary-foreground">
              <Link href="/dashboard/assistants/new">
                <Plus className="mr-2 h-4 w-4" />
                Buat Asisten Baru
              </Link>
            </Button>
          </div>


          {/* Content */}
          {isLoading && (
            <div className="flex flex-col items-center justify-center py-20">
              <div className="relative">
                <div className="w-16 h-16 rounded-full bg-primary animate-pulse"></div>
                <Bot className="absolute inset-0 m-auto h-8 w-8 text-primary-foreground animate-bounce" />
              </div>
              <p className="mt-6 text-lg font-medium text-muted-foreground">Memuat asisten...</p>
            </div>
          )}
          
          {error && (
            <div className="flex flex-col items-center justify-center py-20">
              <div className="p-4 rounded-full bg-destructive/10 mb-4">
                <AlertCircle className="h-8 w-8 text-destructive" />
              </div>
              <h3 className="text-lg font-semibold text-destructive mb-2">Terjadi Kesalahan</h3>
              <p className="text-muted-foreground text-center mb-6">{error}</p>
              <Button onClick={() => window.location.reload()} variant="outline">
                Coba Lagi
              </Button>
            </div>
          )}
          
          {!isLoading && !error && assistants.length === 0 && (
            <div className="flex flex-col items-center justify-center py-20">
              <div className="relative mb-8">
                <div className="w-24 h-24 rounded-3xl bg-primary/10 dark:bg-primary/20 flex items-center justify-center">
                  <Bot className="h-12 w-12 text-primary" />
                </div>
                <div className="absolute -top-2 -right-2 w-8 h-8 rounded-full bg-primary flex items-center justify-center">
                  <Plus className="h-4 w-4 text-primary-foreground" />
                </div>
              </div>
              <h3 className="text-2xl font-bold mb-3">Belum Ada Asisten</h3>
              <p className="text-muted-foreground text-center max-w-md mb-8">
                Mulai dengan membuat asisten AI pertama Anda. Kustomisasi sesuai kebutuhan dan mulai berinteraksi!
              </p>
              <Button asChild size="lg" className="bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg">
                <Link href="/dashboard/assistants/new">
                  <Plus className="mr-2 h-5 w-5" />
                  Buat Asisten Pertama
                </Link>
              </Button>
            </div>
          )}
          
          {!isLoading && !error && assistants.length > 0 && (
            <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
              {assistants.map((assistant) => (
                <Card key={assistant.id}>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">{assistant.name}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-xs text-muted-foreground mb-2">
                      {assistant.description || "Tidak ada deskripsi tersedia."}
                    </p>
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        <span>{new Date(assistant.updatedAt).toLocaleDateString('id-ID')}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <MessageSquare className="h-3 w-3" />
                        <span>Aktif</span>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="pt-0 flex justify-end gap-2">
                    <Link 
                      href={`/dashboard/assistants/${assistant.id}/edit`}
                      className="p-1 rounded hover:bg-muted transition-colors"
                    >
                      <Edit className="h-4 w-4" />
                    </Link>
                    <button
                      onClick={() => handleDeleteAssistant(assistant.id)}
                      className="p-1 rounded text-destructive hover:bg-destructive/10 transition-colors"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </main>
      </SidebarInset>
    </SidebarProvider>
  )
}
