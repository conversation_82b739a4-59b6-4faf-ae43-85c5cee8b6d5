"use client";

import { AppSidebar } from "@/components/app-sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Globe, ArrowLeft, Loader2, <PERSON><PERSON>, AlertCircle, Upload, Image as ImageIcon } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import React, { useEffect, useState } from "react";
import Link from 'next/link';
import { useRouter, useParams } from 'next/navigation';
import { toast } from "sonner";
import { Toaster } from "@/components/ui/sonner";

interface Assistant {
  id: string;
  name: string;
  description: string;
}

interface Webchat {
  id: string;
  name: string;
  title: string;
  greetingMessage: string;
  position: string;
  color: string;
  siteUrl: string;
  assistantId: string;
  snippet: string;
  isEnabled: boolean;
  iconUrl?: string;
  avatarUrl?: string;
}

export default function EditWebchatPage() {
  const [assistants, setAssistants] = useState<Assistant[]>([]);
  const [webchat, setWebchat] = useState<Webchat | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { accessToken } = useAuth();
  const router = useRouter();
  const params = useParams();
  const webchatId = params.id as string;

  const [formData, setFormData] = useState({
    name: '',
    title: '',
    greetingMessage: '',
    position: 'bottom-right',
    color: '#02C1B0',
    siteUrl: '',
    assistantId: '',
    isEnabled: true,
    iconUrl: '',
    avatarUrl: '',
  });

  const [errors, setErrors] = useState<any>({});

  useEffect(() => {
    const fetchData = async () => {
      if (!accessToken || !webchatId) {
        setIsLoading(false);
        return;
      }

      try {
        // Fetch webchat data
        const webchatResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/webchat/${webchatId}`, {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
          },
        });
        
        if (!webchatResponse.ok) {
          throw new Error('Webchat tidak ditemukan');
        }
        
        const webchatData: Webchat = await webchatResponse.json();
        setWebchat(webchatData);
        setFormData({
          name: webchatData.name,
          title: webchatData.title,
          greetingMessage: webchatData.greetingMessage,
          position: webchatData.position,
          color: webchatData.color,
          siteUrl: webchatData.siteUrl,
          assistantId: webchatData.assistantId,
          isEnabled: webchatData.isEnabled,
          iconUrl: webchatData.iconUrl || '',
          avatarUrl: webchatData.avatarUrl || '',
        });

        // Fetch assistants
        const assistantsResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/assistants`, {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
          },
        });
        
        if (assistantsResponse.ok) {
          const assistantsData: Assistant[] = await assistantsResponse.json();
          setAssistants(assistantsData);
        }
      } catch (err: any) {
        toast.error(err.message || "Gagal memuat data webchat");
        router.push('/dashboard/webchat');
      }
      setIsLoading(false);
    };

    fetchData();
  }, [accessToken, webchatId, router]);

  const validateForm = (): boolean => {
    const newErrors: any = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Nama webchat harus diisi';
    }

    if (!formData.title.trim()) {
      newErrors.title = 'Judul widget harus diisi';
    }

    if (!formData.greetingMessage.trim()) {
      newErrors.greetingMessage = 'Pesan sambutan harus diisi';
    }

    if (!formData.siteUrl.trim()) {
      newErrors.siteUrl = 'URL website harus diisi';
    } else if (!formData.siteUrl.startsWith('https://')) {
      newErrors.siteUrl = 'URL harus menggunakan format https://domain.com';
    }

    if (!formData.assistantId) {
      newErrors.assistantId = 'Pilih asisten yang akan digunakan';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleIconUpload = async (file: File) => {
    if (!accessToken || !webchatId) {
      toast.error("Authentication token not found");
      return null;
    }

    const formData = new FormData();
    formData.append('image', file);

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/webchat/${webchatId}/upload-icon`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to upload icon');
      }

      const data = await response.json();
      return data.url;
    } catch (err: any) {
      toast.error(`Error uploading icon: ${err.message}`);
      return null;
    }
  };

  const handleAvatarUpload = async (file: File) => {
    if (!accessToken || !webchatId) {
      toast.error("Authentication token not found");
      return null;
    }

    const formData = new FormData();
    formData.append('image', file);

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/webchat/${webchatId}/upload-avatar`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to upload avatar');
      }

      const data = await response.json();
      return data.url;
    } catch (err: any) {
      toast.error(`Error uploading avatar: ${err.message}`);
      return null;
    }
  };

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>, type: 'icon' | 'avatar') => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Check file type
    if (!file.type.includes('image/')) {
      toast.error('File harus berupa gambar');
      return;
    }

    // Check file size (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
      toast.error('Ukuran file maksimal 1MB');
      return;
    }

    // Show loading toast
    const loadingToast = toast.loading(`Mengupload ${type === 'icon' ? 'ikon' : 'avatar'}...`);

    try {
      let url;
      if (type === 'icon') {
        url = await handleIconUpload(file);
      } else {
        url = await handleAvatarUpload(file);
      }

      if (url) {
        setFormData(prev => ({
          ...prev,
          [type === 'icon' ? 'iconUrl' : 'avatarUrl']: url
        }));
        toast.success(`${type === 'icon' ? 'Ikon' : 'Avatar'} berhasil diupload`);
      }
    } finally {
      toast.dismiss(loadingToast);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    if (!accessToken || !webchatId) {
      toast.error("Authentication token not found");
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/webchat/${webchatId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`,
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update webchat');
      }

      // Fetch updated data to get new snippet
      const updatedResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/webchat/${webchatId}`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      });

      if (updatedResponse.ok) {
        const updatedData: Webchat = await updatedResponse.json();
        setWebchat(updatedData);
      }

      toast.success('Webchat berhasil diperbarui!');
    } catch (err: any) {
      toast.error(`Error: ${err.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors((prev: any) => ({ ...prev, [field]: undefined }));
    }
  };

  const copySnippet = () => {
    if (webchat?.snippet) {
      navigator.clipboard.writeText(webchat.snippet);
      toast.success("Snippet berhasil disalin!");
    }
  };

  const positions = [
    { value: 'bottom-right', label: 'Kanan Bawah' },
    { value: 'bottom-left', label: 'Kiri Bawah' },
    { value: 'top-right', label: 'Kanan Atas' },
    { value: 'top-left', label: 'Kiri Atas' },
  ];

  if (isLoading) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <div className="flex items-center justify-center h-screen">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p>Memuat data webchat...</p>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <Toaster />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbLink href="/dashboard/webchat">Webchat</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>Edit</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>
        
        <main className="flex-1 p-4 md:p-6 space-y-6">
          <div className="mb-6">
            <Link href="/dashboard/webchat" passHref>
              <Button variant="outline" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" /> Kembali ke Daftar Webchat
              </Button>
            </Link>
          </div>

          <div className="mb-6">
            <h2 className="text-2xl font-bold tracking-tight">Edit Webchat Widget</h2>
            <p className="text-muted-foreground">
              Perbarui konfigurasi widget chat Anda
            </p>
          </div>

            <div className="grid gap-6 lg:grid-cols-3">
              {/* Form */}
              <div className="lg:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Globe className="h-5 w-5" />
                      Konfigurasi Widget
                    </CardTitle>
                    <CardDescription>
                      Atur tampilan dan perilaku widget chat
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <form onSubmit={handleSubmit} className="space-y-4">
                      {/* Basic Info */}
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="name">Nama Webchat *</Label>
                          <Input
                            id="name"
                            value={formData.name}
                            onChange={(e) => handleInputChange('name', e.target.value)}
                            placeholder="Contoh: Widget Customer Support"
                            className={errors.name ? 'border-destructive' : ''}
                          />
                          {errors.name && (
                            <p className="text-sm text-destructive mt-1">{errors.name}</p>
                          )}
                        </div>

                        <div>
                          <Label htmlFor="title">Judul Widget *</Label>
                          <Input
                            id="title"
                            value={formData.title}
                            onChange={(e) => handleInputChange('title', e.target.value)}
                            placeholder="Contoh: Customer Support"
                            className={errors.title ? 'border-destructive' : ''}
                          />
                          {errors.title && (
                            <p className="text-sm text-destructive mt-1">{errors.title}</p>
                          )}
                        </div>

                        <div>
                          <Label htmlFor="greetingMessage">Pesan Sambutan *</Label>
                          <Textarea
                            id="greetingMessage"
                            value={formData.greetingMessage}
                            onChange={(e) => handleInputChange('greetingMessage', e.target.value)}
                            placeholder="Pesan yang akan ditampilkan saat widget dibuka"
                            className={errors.greetingMessage ? 'border-destructive' : ''}
                          />
                          {errors.greetingMessage && (
                            <p className="text-sm text-destructive mt-1">{errors.greetingMessage}</p>
                          )}
                        </div>

                        <div>
                          <Label htmlFor="siteUrl">URL Website *</Label>
                          <Input
                            id="siteUrl"
                            value={formData.siteUrl}
                            onChange={(e) => handleInputChange('siteUrl', e.target.value)}
                            placeholder="https://domain.com"
                            className={errors.siteUrl ? 'border-destructive' : ''}
                          />
                          {errors.siteUrl && (
                            <p className="text-sm text-destructive mt-1">{errors.siteUrl}</p>
                          )}
                        </div>

                        <div>
                          <Label htmlFor="assistantId">Pilih Asisten *</Label>
                          <Select
                            value={formData.assistantId}
                            onValueChange={(value) => handleInputChange('assistantId', value)}
                          >
                            <SelectTrigger className={errors.assistantId ? 'border-destructive' : ''}>
                              <SelectValue placeholder="Pilih asisten yang akan digunakan" />
                            </SelectTrigger>
                            <SelectContent>
                              {assistants.map((assistant) => (
                                <SelectItem key={assistant.id} value={assistant.id}>
                                  <div>
                                    <div className="font-medium">{assistant.name}</div>
                                    {assistant.description && (
                                      <div className="text-xs text-muted-foreground">
                                        {assistant.description}
                                      </div>
                                    )}
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          {errors.assistantId && (
                            <p className="text-sm text-destructive mt-1">{errors.assistantId}</p>
                          )}
                        </div>
                      </div>

                      {/* Appearance */}
                      <div className="space-y-4 pt-4 border-t">
                        <h3 className="font-medium">Tampilan Widget</h3>
                        
                        <div>
                          <Label htmlFor="position">Posisi Widget</Label>
                          <Select
                            value={formData.position}
                            onValueChange={(value) => handleInputChange('position', value)}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {positions.map((position) => (
                                <SelectItem key={position.value} value={position.value}>
                                  {position.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div>
                          <Label htmlFor="color">Warna Utama</Label>
                          <div className="flex items-center gap-3">
                            <Input
                              id="color"
                              type="color"
                              value={formData.color}
                              onChange={(e) => handleInputChange('color', e.target.value)}
                              className="w-16 h-10 p-1 border rounded"
                            />
                            <Input
                              value={formData.color}
                              onChange={(e) => handleInputChange('color', e.target.value)}
                              placeholder="#02C1B0"
                              className="flex-1"
                            />
                          </div>
                        </div>

                        <div>
                          <Label>Ikon Chat</Label>
                          <div className="flex flex-col space-y-2">
                            <div className="flex items-center gap-3">
                              <div className="h-16 w-16 rounded-md border flex items-center justify-center bg-muted">
                                {formData.iconUrl ? (
                                  <img 
                                    src={formData.iconUrl} 
                                    alt="Chat Icon" 
                                    className="h-full w-full object-contain p-1" 
                                  />
                                ) : (
                                  <ImageIcon className="h-8 w-8 text-muted-foreground" />
                                )}
                              </div>
                              <div className="flex-1">
                                <Label htmlFor="icon-upload" className="cursor-pointer">
                                  <div className="flex items-center gap-2 border rounded-md px-4 py-2 hover:bg-muted transition-colors">
                                    <Upload className="h-4 w-4" />
                                    <span>Upload Ikon</span>
                                  </div>
                                </Label>
                                <Input 
                                  id="icon-upload" 
                                  type="file" 
                                  accept="image/*"
                                  className="hidden" 
                                  onChange={(e) => handleFileChange(e, 'icon')}
                                />
                                <p className="text-xs text-muted-foreground mt-1">Format PNG, dimensi 1:1 (persegi), max 1MB</p>
                              </div>
                            </div>
                            {formData.iconUrl && (
                              <Button 
                                type="button" 
                                variant="outline" 
                                size="sm"
                                onClick={() => handleInputChange('iconUrl', '')}
                              >
                                Hapus Ikon
                              </Button>
                            )}
                          </div>
                        </div>

                        <div>
                          <Label>Avatar Chat</Label>
                          <div className="flex flex-col space-y-2">
                            <div className="flex items-center gap-3">
                              <div className="h-16 w-16 rounded-full border flex items-center justify-center bg-muted overflow-hidden">
                                {formData.avatarUrl ? (
                                  <img 
                                    src={formData.avatarUrl} 
                                    alt="Chat Avatar" 
                                    className="h-full w-full object-cover" 
                                  />
                                ) : (
                                  <ImageIcon className="h-8 w-8 text-muted-foreground" />
                                )}
                              </div>
                              <div className="flex-1">
                                <Label htmlFor="avatar-upload" className="cursor-pointer">
                                  <div className="flex items-center gap-2 border rounded-md px-4 py-2 hover:bg-muted transition-colors">
                                    <Upload className="h-4 w-4" />
                                    <span>Upload Avatar</span>
                                  </div>
                                </Label>
                                <Input 
                                  id="avatar-upload" 
                                  type="file" 
                                  accept="image/*"
                                  className="hidden" 
                                  onChange={(e) => handleFileChange(e, 'avatar')}
                                />
                                <p className="text-xs text-muted-foreground mt-1">Format PNG, dimensi 1:1 (persegi), max 1MB</p>
                              </div>
                            </div>
                            {formData.avatarUrl && (
                              <Button 
                                type="button" 
                                variant="outline" 
                                size="sm"
                                onClick={() => handleInputChange('avatarUrl', '')}
                              >
                                Hapus Avatar
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Settings */}
                      <div className="space-y-4 pt-4 border-t">
                        <h3 className="font-medium">Pengaturan</h3>

                        <div className="flex items-center justify-between">
                          <div>
                            <Label htmlFor="isEnabled">Aktifkan Widget</Label>
                            <p className="text-sm text-muted-foreground">
                              Widget akan berfungsi di website
                            </p>
                          </div>
                          <Switch
                            id="isEnabled"
                            checked={formData.isEnabled}
                            onCheckedChange={(checked) => handleInputChange('isEnabled', checked)}
                          />
                        </div>
                      </div>

                      <div className="flex gap-3 pt-6">
                        <Button
                          type="submit"
                          disabled={isSubmitting}
                          className="flex-1"
                        >
                          {isSubmitting ? (
                            <>
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                              Menyimpan...
                            </>
                          ) : (
                            'Simpan Perubahan'
                          )}
                        </Button>
                      </div>
                    </form>
                  </CardContent>
                </Card>
              </div>

              {/* Snippet Code */}
              <div>
                <Card>
                  <CardHeader>
                    <CardTitle>Kode Embed</CardTitle>
                    <CardDescription>
                      Salin kode ini ke website Anda
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {webchat?.snippet ? (
                      <div className="space-y-3">
                        <div className="bg-muted p-3 rounded-lg">
                          <code className="text-sm break-all">
                            {webchat.snippet}
                          </code>
                        </div>
                        <Button
                          onClick={copySnippet}
                          variant="outline"
                          size="sm"
                          className="w-full"
                        >
                          <Copy className="h-4 w-4 mr-2" />
                          Salin Kode
                        </Button>
                        <div className="text-xs text-muted-foreground">
                          <div className="flex items-start gap-2">
                            <AlertCircle className="h-4 w-4 mt-0.5 flex-shrink-0" />
                            <div>
                              <p className="font-medium">Cara Pasang:</p>
                              <p>Tempel kode di atas sebelum tag &lt;/body&gt; di website Anda</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center text-muted-foreground py-8">
                        <Globe className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p>Kode embed akan muncul setelah disimpan</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>

        </main>
      </SidebarInset>
    </SidebarProvider>
  )
}
