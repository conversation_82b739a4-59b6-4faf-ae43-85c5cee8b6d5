"use client";

import { AppSidebar } from "@/components/app-sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Globe, Plus, Edit, Trash2, AlertCircle, Calendar, Copy, Loader2, ExternalLink, Settings, History } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import React, { useEffect, useState } from "react";
import Link from 'next/link';
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Toaster } from "@/components/ui/sonner";

interface Webchat {
  id: string;
  name: string;
  title: string;
  greetingMessage: string;
  position: string;
  color: string;
  siteUrl: string;
  userId: string;
  assistantId: string;
  snippet: string;
  isEnabled: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function WebchatPage() {
  const [webchats, setWebchats] = useState<Webchat[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { accessToken } = useAuth();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [webchatToDelete, setWebchatToDelete] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Check for toast messages in sessionStorage
  useEffect(() => {
    const storedToast = sessionStorage.getItem('webchatToast');
    if (storedToast) {
      toast.success(storedToast);
      sessionStorage.removeItem('webchatToast');
    }
  }, []);

  useEffect(() => {
    const fetchWebchats = async () => {
      if (!accessToken) {
        setIsLoading(false);
        return;
      }
      setIsLoading(true);
      setError(null);
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/webchat`, {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
          },
        });
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || `Failed to fetch webchats: ${response.status}`);
        }
        const data: Webchat[] = await response.json();
        setWebchats(data);
      } catch (err: any) {
        setError(err.message || "An unexpected error occurred.");
      }
      setIsLoading(false);
    };

    fetchWebchats();
  }, [accessToken]);

  const handleDeleteWebchat = async (webchatId: string) => {
    if (!accessToken) {
      toast.error("Authentication token not found.");
      return;
    }
    
    setWebchatToDelete(webchatId);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!webchatToDelete || !accessToken) return;
    
    setIsDeleting(true);
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/webchat/${webchatToDelete}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      });
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Gagal menghapus webchat.' }));
        throw new Error(errorData.message || `Gagal menghapus webchat: ${response.status}`);
      }
      setWebchats(prevWebchats => prevWebchats.filter(webchat => webchat.id !== webchatToDelete));
      toast.success("Webchat berhasil dihapus.");
    } catch (err: any) {
      toast.error(`Error: ${err.message}`);
    } finally {
      setDeleteDialogOpen(false);
      setWebchatToDelete(null);
      setIsDeleting(false);
    }
  };

  const copySnippet = (snippet: string) => {
    navigator.clipboard.writeText(snippet);
    toast.success("Snippet berhasil disalin!");
  };

  const getPositionLabel = (position: string) => {
    const positions: { [key: string]: string } = {
      'top-left': 'Kiri Atas',
      'top-right': 'Kanan Atas',
      'bottom-left': 'Kiri Bawah',
      'bottom-right': 'Kanan Bawah'
    };
    return positions[position] || position;
  };

  return (
    <SidebarProvider>
      <AppSidebar />
      <Toaster />
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Hapus Webchat</AlertDialogTitle>
            <AlertDialogDescription>
              Apakah Anda yakin ingin menghapus webchat ini? Tindakan ini tidak dapat dibatalkan dan widget akan berhenti berfungsi di website Anda.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Batal</AlertDialogCancel>
            <AlertDialogAction 
              onClick={(e) => {
                e.preventDefault();
                confirmDelete();
              }} 
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Menghapus...
                </>
              ) : (
                "Hapus"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>Webchat</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>
        
        <main className="flex-1 p-4 md:p-6 space-y-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
            <div>
              <h2 className="text-2xl font-bold tracking-tight">Webchat Widget</h2>
              <p className="text-muted-foreground">
                Integrasikan asisten AI ke website Anda dengan widget chat yang mudah dipasang
              </p>
            </div>
            <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3">
              <Link href="/dashboard/webchat/chats" passHref>
                <Button variant="outline" className="w-full sm:w-auto">
                  <History className="mr-2 h-4 w-4" /> Histori Percakapan
                </Button>
              </Link>
              <Button asChild className="bg-primary hover:bg-primary/90 text-primary-foreground w-full sm:w-auto">
                <Link href="/dashboard/webchat/new">
                  <Plus className="mr-2 h-4 w-4" />
                  Buat Widget Baru
                </Link>
              </Button>
            </div>
          </div>

          {/* Content */}
          {isLoading && (
            <div className="flex flex-col items-center justify-center py-20">
              <div className="relative">
                <div className="w-16 h-16 rounded-full bg-primary animate-pulse"></div>
                <Globe className="absolute inset-0 m-auto h-8 w-8 text-primary-foreground animate-bounce" />
              </div>
              <p className="mt-6 text-lg font-medium text-muted-foreground">Memuat webchat...</p>
            </div>
          )}
          
          {error && (
            <div className="flex flex-col items-center justify-center py-20">
              <div className="p-4 rounded-full bg-destructive/10 mb-4">
                <AlertCircle className="h-8 w-8 text-destructive" />
              </div>
              <h3 className="text-lg font-semibold text-destructive mb-2">Terjadi Kesalahan</h3>
              <p className="text-muted-foreground text-center mb-6">{error}</p>
              <Button onClick={() => window.location.reload()} variant="outline">
                Coba Lagi
              </Button>
            </div>
          )}
          
          {!isLoading && !error && webchats.length === 0 && (
            <div className="flex flex-col items-center justify-center py-20">
              <div className="relative mb-8">
                <div className="w-24 h-24 rounded-3xl bg-primary/10 dark:bg-primary/20 flex items-center justify-center">
                  <Globe className="h-12 w-12 text-primary" />
                </div>
                <div className="absolute -top-2 -right-2 w-8 h-8 rounded-full bg-primary flex items-center justify-center">
                  <Plus className="h-4 w-4 text-primary-foreground" />
                </div>
              </div>
              <h3 className="text-2xl font-bold mb-3">Belum Ada Widget</h3>
              <p className="text-muted-foreground text-center max-w-md mb-8">
                Mulai dengan membuat widget webchat pertama Anda. Integrasikan asisten AI ke website dengan mudah!
              </p>
              <Button asChild size="lg" className="bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg">
                <Link href="/dashboard/webchat/new">
                  <Plus className="mr-2 h-5 w-5" />
                  Buat Widget Pertama
                </Link>
              </Button>
            </div>
          )}
          
          {!isLoading && !error && webchats.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Daftar Webchat Widget</CardTitle>
                <CardDescription>Total {webchats.length} widget ditemukan.</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nama</TableHead>
                      <TableHead>Website</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Posisi</TableHead>
                      <TableHead>Terakhir Diperbarui</TableHead>
                      <TableHead className="text-right">Aksi</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {webchats.map((webchat) => (
                      <TableRow key={webchat.id}>
                        <TableCell className="font-medium">
                          <div>
                            <div className="font-semibold">{webchat.name}</div>
                            <div className="text-sm text-muted-foreground">{webchat.title}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <a
                            href={webchat.siteUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-primary hover:underline flex items-center gap-1 max-w-[200px] truncate"
                          >
                            {webchat.siteUrl.replace('https://', '')}
                            <ExternalLink className="h-3 w-3" />
                          </a>
                        </TableCell>
                        <TableCell>
                          <Badge variant={webchat.isEnabled ? "default" : "secondary"}>
                            {webchat.isEnabled ? "Aktif" : "Nonaktif"}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <span>{getPositionLabel(webchat.position)}</span>
                            <div
                              className="w-4 h-4 rounded border border-border"
                              style={{ backgroundColor: webchat.color }}
                            ></div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {new Date(webchat.updatedAt).toLocaleDateString('id-ID')}
                        </TableCell>
                        <TableCell className="text-right space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => copySnippet(webchat.snippet)}
                            title="Copy Snippet Code"
                          >
                            <Copy className="mr-1 h-3 w-3" /> Copy Snippet
                          </Button>
                          <Link href={`/dashboard/webchat/${webchat.id}/edit`} passHref>
                            <Button variant="outline" size="sm">
                              <Edit className="mr-1 h-3 w-3" /> Edit
                            </Button>
                          </Link>
                          <Button variant="destructive" size="sm" onClick={() => handleDeleteWebchat(webchat.id)}>
                            <Trash2 className="mr-1 h-3 w-3" /> Hapus
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )}
        </main>
      </SidebarInset>
    </SidebarProvider>
  )
}
