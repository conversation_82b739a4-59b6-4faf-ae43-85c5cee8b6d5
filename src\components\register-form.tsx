"use client"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useState, useEffect } from "react"
import Image from "next/image"
import { useTheme } from "next-themes"
import { useAuth } from "@/contexts/AuthContext";

// Declare gtag function for TypeScript
declare global {
  interface Window {
    gtag: (command: string, action: string, params?: any) => void;
    dataLayer: any[];
  }
}

export function RegisterForm({
  className,
  ...props
}: React.ComponentProps<"div">) {
  const [isOtpSent, setIsOtpSent] = useState(false);
  const [email, setEmail] = useState("");
  const [name, setName] = useState("");
  const [otp, setOtp] = useState("");
  const { theme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [message, setMessage] = useState<string | null>(null);
  const auth = useAuth();

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleSendOtp = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email || !name) {
      setError("Name and Email are required.");
      return;
    }
    setIsLoading(true);
    setError(null);
    setMessage(null);
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/auth/signup`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ name, email, provider: "email" }),
      });
      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.message || "Failed to send OTP during signup.");
      }
      setMessage(data.message || "OTP sent successfully. Please check your email.");
      setIsOtpSent(true);
    } catch (err: any) {
      setError(err.message || "An unexpected error occurred during signup.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!otp) {
      setError("OTP is required.");
      return;
    }
    setIsLoading(true);
    setError(null);
    setMessage(null);
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/auth/verify-otp`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, provider: "email", code: otp }),
      });
      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.message || "Failed to verify OTP.");
      }
      if (data.accessToken) {
        // Track successful registration in Google Analytics
        if (typeof window !== 'undefined' && window.gtag) {
          window.gtag('event', 'sign_up_success', {
            'method': 'email',
            'user_type': 'new_user'
          });
        }
        await auth.login(data.accessToken);
      } else {
        throw new Error("Access token not received.");
      }
    } catch (err: any) {
      setError(err.message || "An unexpected error occurred during registration.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <Card className="overflow-hidden p-6 md:p-8">
        <CardContent className="grid p-0">
          <div className="flex justify-center mb-6">
            <div className="relative w-40 h-16">
              {mounted ? (
                <Image 
                  src={theme === "light" ? "/logoblack.svg" : "/logowhite.svg"}
                  alt="Heylo Logo"
                  fill
                  className="object-contain"
                />
              ) : (
                <div className="w-full h-full bg-transparent" /> // Placeholder to maintain layout
              )}
            </div>
          </div>
          <form onSubmit={isOtpSent ? handleRegister : handleSendOtp}>
            <div className="flex flex-col gap-6">
              <div className="flex flex-col items-center text-center">
                <h1 className="text-2xl font-bold">Daftar Akun</h1>
                <p className="text-muted-foreground text-balance">
                  Buat akun Heylo baru
                </p>
              </div>
              
              {!isOtpSent && (
                <>
                  <div className="grid gap-3">
                    <Label htmlFor="name">Nama</Label>
                    <Input
                      id="name"
                      type="text"
                      placeholder="Nama Lengkap"
                      required
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      disabled={isOtpSent} 
                    />
                  </div>
                  <div className="grid gap-3">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      required
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      disabled={isOtpSent} 
                    />
                  </div>
                </>
              )}
              
              {message && <p className={`text-sm ${error ? 'text-red-600' : 'text-green-600'}`}>{message}</p>}
              {error && !message && <p className="text-sm text-red-600">{error}</p>}
              
              {isOtpSent && (
                <div className="grid gap-3">
                  <div className="flex items-center">
                    <Label htmlFor="otp">Kode OTP</Label>
                    <a
                      href="#"
                      className="ml-auto text-sm underline-offset-2 hover:underline"
                      onClick={(e) => {
                        e.preventDefault();
                        alert("OTP baru telah dikirim ke email Anda");
                      }}
                    >
                      Kirim ulang OTP?
                    </a>
                  </div>
                  <Input
                    id="otp"
                    type="text"
                    placeholder="Enter 4-6 digit OTP"
                    required
                    value={otp}
                    onChange={(e) => setOtp(e.target.value)}
                    maxLength={6}
                    className="h-12 text-center text-xl tracking-[0.5em]"
                  />
                </div>
              )}
              
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? "Processing..." : (isOtpSent ? "Daftar" : "Kirim Kode OTP")}
              </Button>
              
              <div className="text-center text-sm">
                Sudah memiliki akun?{" "}
                <a href="/login" className="underline underline-offset-4">
                  Masuk
                </a>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>
      <div className="text-muted-foreground *:[a]:hover:text-primary text-center text-xs text-balance *:[a]:underline *:[a]:underline-offset-4">
        Dengan melanjutkan, Anda menyetujui <a href="/terms">Ketentuan Layanan</a>{" "}
        dan <a href="/privacy">Kebijakan Privasi</a> kami.
      </div>
    </div>
  )
}
