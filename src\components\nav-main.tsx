"use client"

import { ChevronRight, type LucideIcon } from "lucide-react"
import Link from "next/link"

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar"

export function NavMain({
  items,
}: {
  items: {
    title: string
    url: string
    icon: LucideIcon
    isActive?: boolean
    items?: {
      title: string
      url: string
      icon?: LucideIcon
      disabled?: boolean
      isActive?: boolean
    }[]
  }[]
}) {
  return (
    <SidebarGroup>
      <SidebarGroupLabel>Menu</SidebarGroupLabel>
      <SidebarMenu>
        {items.map((item) => (
          <Collapsible key={item.title} asChild defaultOpen={item.title === "Channel" ? true : item.isActive}>
            <SidebarMenuItem>
              <SidebarMenuButton 
                asChild 
                tooltip={item.title}
                className={`group relative hover:bg-accent/50 rounded-md transition-colors ${item.isActive ? 'bg-primary/10' : ''}`}
              >
                <Link href={item.url} className="flex items-center">
                  <item.icon className={`transition-colors ${item.isActive ? 'text-primary' : 'text-muted-foreground hover:text-primary'}`} />
                  <span className={`font-medium transition-colors ${item.isActive ? 'text-primary' : 'hover:text-primary'}`}>{item.title}</span>
                </Link>
              </SidebarMenuButton>
              {item.items?.length ? (
                <>
                  <CollapsibleTrigger asChild>
                    <SidebarMenuAction className="data-[state=open]:rotate-90 text-muted-foreground hover:text-foreground transition-colors">
                      <ChevronRight />
                      <span className="sr-only">Toggle</span>
                    </SidebarMenuAction>
                  </CollapsibleTrigger>
                  <CollapsibleContent>
                    <SidebarMenuSub>
                      {item.items?.map((subItem) => (
                        <SidebarMenuSubItem key={subItem.title}>
                          <SidebarMenuSubButton 
                            asChild 
                            className={`flex items-center text-muted-foreground hover:text-foreground hover:bg-accent/30 rounded transition-colors pl-8 ${subItem.disabled ? 'opacity-50 cursor-not-allowed' : ''} ${subItem.isActive ? 'bg-primary/10 text-primary' : ''}`}
                          >
                            <Link href={subItem.disabled ? "#" : subItem.url} 
                                  className={`flex items-center ${subItem.disabled ? 'pointer-events-none' : ''}`}
                                  onClick={subItem.disabled ? (e) => e.preventDefault() : undefined}
                            >
                              {subItem.icon && <subItem.icon className="mr-2 h-4 w-4" />}
                              <span>{subItem.title}</span>
                            </Link>
                          </SidebarMenuSubButton>
                        </SidebarMenuSubItem>
                      ))}
                    </SidebarMenuSub>
                  </CollapsibleContent>
                </>
              ) : null}
            </SidebarMenuItem>
          </Collapsible>
        ))}
      </SidebarMenu>
    </SidebarGroup>
  )
}
