"use client"

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';

import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Wand2, ArrowRight, ArrowLeft, Copy, Check, Loader2, Sparkles, MessageSquare } from 'lucide-react';
import { toast } from 'sonner';

interface AIPromptGeneratorProps {
  onPromptGenerated: (prompt: string) => void;
  disabled?: boolean;
}

interface WizardAnswers {
  business: string;
  audience: string;
  knowledge: string;
  additional: string;
}

const WIZARD_QUESTIONS = [
  {
    id: 'business',
    title: 'Assistant untuk apa? PT/Brand apa?',
    description: '<PERSON><PERSON><PERSON> secara singkat assistant ini untuk bisnis/brand apa dan fungsi utamanya.',
    placeholder: 'Contoh: "Assistant customer service untuk PT ABC, perusahaan kursus online. Fungsi utama: membantu pendaftaran, menjawab FAQ, dan memberikan info jadwal kelas."',
    type: 'textarea' as const
  },
  {
    id: 'audience',
    title: 'Untuk user external atau internal?',
    description: 'Siapa yang akan menggunakan assistant ini? Customer/client atau tim internal?',
    placeholder: 'Contoh: "User external - calon siswa dan orang tua yang ingin mendaftar kursus. Mereka biasanya bertanya tentang harga, jadwal, dan cara pendaftaran."',
    type: 'textarea' as const
  },
  {
    id: 'knowledge',
    title: 'Akan ada dokumen knowledge? Nama file apa?',
    description: 'Dokumen apa saja yang akan di-upload? Sebutkan nama file dan isinya secara singkat.',
    placeholder: 'Contoh: "1) katalog-kursus-2024.pdf (daftar mata pelajaran dan harga), 2) faq-umum.docx (pertanyaan yang sering ditanyakan), 3) panduan-pendaftaran.pdf (step-by-step cara daftar)"',
    type: 'textarea' as const
  },
  {
    id: 'additional',
    title: 'Hal lain yang AI perlu tau?',
    description: 'Info tambahan seperti jam operasional, gaya komunikasi, kebijakan, atau batasan tertentu.',
    placeholder: 'Contoh: "Jam operasional: Senin-Jumat 08:00-17:00. Gaya komunikasi: ramah dan santai. Jangan berikan diskon di luar kebijakan. Jika ada komplain serius, minta user untuk hubungi WhatsApp admin."',
    type: 'textarea' as const
  }
];

export function AIPromptGenerator({ onPromptGenerated, disabled = false }: AIPromptGeneratorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [answers, setAnswers] = useState<WizardAnswers>({
    business: '',
    audience: '',
    knowledge: '',
    additional: ''
  });
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedPrompt, setGeneratedPrompt] = useState<string | null>(null);
  const [copied, setCopied] = useState(false);

  const currentQuestion = WIZARD_QUESTIONS[currentStep];
  const isLastStep = currentStep === WIZARD_QUESTIONS.length - 1;
  const canProceed = answers[currentQuestion.id as keyof WizardAnswers].trim().length > 0;

  const handleNext = () => {
    if (isLastStep) {
      generatePrompt();
    } else {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => prev - 1);
  };

  const handleAnswerChange = (value: string) => {
    setAnswers(prev => ({
      ...prev,
      [currentQuestion.id]: value
    }));
  };

  const generatePrompt = async () => {
    setIsGenerating(true);

    // Combine all answers into a message
    const message = `
Assistant untuk apa? PT/Brand apa?: ${answers.business}

Untuk user external atau internal?: ${answers.audience}

Akan ada dokumen knowledge? Nama file apa?: ${answers.knowledge}

Hal lain yang AI perlu tau?: ${answers.additional}
    `.trim();

    try {
      // Call our internal API route instead of direct webhook
      const response = await fetch('/api/generate-prompt', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message }),
      });

      if (!response.ok) {
        throw new Error('Gagal menghubungi AI prompter');
      }

      const data = await response.json();

      if (data.output) {
        setGeneratedPrompt(data.output);
        toast.success('Prompt berhasil di-generate!');
      } else {
        throw new Error('Response tidak valid dari AI prompter');
      }
    } catch (error) {
      console.error('Error generating prompt:', error);
      toast.error('Gagal generate prompt', {
        description: 'Terjadi kesalahan saat menghubungi AI prompter. Silakan coba lagi.'
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const copyPrompt = async () => {
    if (!generatedPrompt) return;
    
    try {
      await navigator.clipboard.writeText(generatedPrompt);
      setCopied(true);
      toast.success('Prompt berhasil disalin!');
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast.error('Gagal menyalin prompt');
    }
  };

  const applyPrompt = () => {
    if (generatedPrompt) {
      onPromptGenerated(generatedPrompt);
      setIsOpen(false);
      resetWizard();
      toast.success('Prompt berhasil diterapkan!');
    }
  };

  const resetWizard = () => {
    setCurrentStep(0);
    setAnswers({
      business: '',
      audience: '',
      knowledge: '',
      additional: ''
    });
    setGeneratedPrompt(null);
    setCopied(false);
  };

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    if (!open) {
      resetWizard();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <Button
          type="button"
          variant="outline"
          size="sm"
          disabled={disabled}
          className="gap-2 bg-primary/5 text-primary border-primary/20 hover:bg-primary/10 hover:border-primary/30"
        >
          <Sparkles className="h-4 w-4" />
          Minta AI Bikinkan Prompt
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Wand2 className="h-5 w-5 text-primary" />
            AI Prompt Generator
          </DialogTitle>
          <DialogDescription>
            Jawab 4 pertanyaan singkat untuk membantu AI membuat prompt yang tepat untuk assistant Anda.
            Berikan jawaban yang jelas dan spesifik!
          </DialogDescription>
        </DialogHeader>

        {!generatedPrompt ? (
          <div className="space-y-6">
            {/* Progress */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">
                  Langkah {currentStep + 1} dari {WIZARD_QUESTIONS.length}
                </span>
                <span className="text-muted-foreground">
                  {Math.round(((currentStep + 1) / WIZARD_QUESTIONS.length) * 100)}%
                </span>
              </div>
              <Progress value={((currentStep + 1) / WIZARD_QUESTIONS.length) * 100} className="h-2" />
            </div>

            {/* Question Card */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <MessageSquare className="h-5 w-5 text-primary" />
                  {currentQuestion.title}
                </CardTitle>
                <CardDescription>
                  {currentQuestion.description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Label htmlFor="answer">Jawaban Anda:</Label>
                  <Textarea
                    id="answer"
                    value={answers[currentQuestion.id as keyof WizardAnswers]}
                    onChange={(e) => handleAnswerChange(e.target.value)}
                    placeholder={currentQuestion.placeholder}
                    className="min-h-[120px] resize-none"
                    rows={5}
                  />
                  <p className="text-xs text-muted-foreground">
                    💡 <strong>Tips:</strong> Jawab dengan singkat tapi jelas. Berikan contoh konkret jika memungkinkan.
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Navigation */}
            <div className="flex justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={handlePrevious}
                disabled={currentStep === 0}
                className="gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Sebelumnya
              </Button>
              
              <Button
                type="button"
                onClick={handleNext}
                disabled={!canProceed || isGenerating}
                className="gap-2"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    Generating...
                  </>
                ) : isLastStep ? (
                  <>
                    <Wand2 className="h-4 w-4" />
                    Generate Prompt
                  </>
                ) : (
                  <>
                    Selanjutnya
                    <ArrowRight className="h-4 w-4" />
                  </>
                )}
              </Button>
            </div>
          </div>
        ) : (
          /* Generated Prompt Result */
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Check className="h-5 w-5 text-green-600" />
                  Prompt Berhasil Di-generate!
                </CardTitle>
                <CardDescription>
                  Berikut adalah prompt yang dibuat AI berdasarkan jawaban Anda
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="bg-muted/50 rounded-lg p-4 border max-h-80 overflow-y-auto">
                    <pre className="whitespace-pre-wrap text-sm font-mono">
                      {generatedPrompt}
                    </pre>
                  </div>
                  
                  <div className="flex gap-2 justify-end">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={copyPrompt}
                      className="gap-2"
                    >
                      {copied ? (
                        <>
                          <Check className="h-4 w-4 text-green-600" />
                          Tersalin!
                        </>
                      ) : (
                        <>
                          <Copy className="h-4 w-4" />
                          Salin
                        </>
                      )}
                    </Button>
                    
                    <Button
                      type="button"
                      onClick={applyPrompt}
                      className="gap-2"
                    >
                      <Check className="h-4 w-4" />
                      Gunakan Prompt Ini
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <div className="flex justify-center">
              <Button
                type="button"
                variant="ghost"
                onClick={resetWizard}
                className="text-muted-foreground"
              >
                Buat Prompt Baru
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
