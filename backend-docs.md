# API Documentation

base url di env frontend = NEXT_API_URL=https://dianabe.pradi.cloud

## Table of Contents

- [Auth Module](#auth-module)
  - [Sign In](#1-sign-in)
  - [Sign Up](#2-sign-up)
  - [Verify OTP](#3-verify-otp)
  - [Get Profile](#4-get-profile)
- [Assistant Module](#assistant-module)
  - [Assistant Management](#assistant-management)
    - [Create Assistant](#1-create-assistant)
    - [Get All Assistants](#2-get-all-assistants)
    - [Get Assistant by ID](#3-get-assistant-by-id)
    - [Update Assistant](#4-update-assistant)
    - [Delete Assistant](#5-delete-assistant)
  - [Chat Management](#chat-management)
    - [Get All Chats](#1-get-all-chats)
    - [Get Chat by ID](#2-get-chat-by-id)
    - [Start New Chat](#3-start-new-chat)
    - [Send Message to Existing Chat](#4-send-message-to-existing-chat)
    - [Get Chat Messages](#5-get-chat-messages)
    - [Delete Chat](#6-delete-chat)
  - [File Management](#file-management)
    - [Upload Files](#1-upload-files)
    - [Get All Files](#2-get-all-files)
    - [Delete File](#3-delete-file)
- [WhatsApp Module](#whatsapp-module)
  - [WhatsApp Instance Management](#whatsapp-instance-management)
    - [Create WhatsApp Instance](#1-create-whatsapp-instance)
    - [Get All WhatsApp Instances](#2-get-all-whatsapp-instances)
    - [Get WhatsApp Instance](#3-get-whatsapp-instance)
    - [Update WhatsApp Instance](#4-update-whatsapp-instance)
    - [Delete WhatsApp Instance](#5-delete-whatsapp-instance)
    - [Initialize WhatsApp Client](#6-initialize-whatsapp-client)
    - [Get QR Code](#7-get-qr-code)
    - [Disconnect WhatsApp](#8-disconnect-whatsapp)
    - [Logout WhatsApp](#9-logout-whatsapp)
    - [Assign Assistant](#10-assign-assistant)
    - [Enable WhatsApp](#11-enable-whatsapp)
    - [Disable WhatsApp](#12-disable-whatsapp)
  - [WhatsApp Chat Management](#whatsapp-chat-management)
    - [Get All Chats](#1-get-all-chats)
    - [Get Chat Messages](#2-get-chat-messages)
    - [Update Chat](#3-update-chat)

## Auth Module

The Auth module provides endpoints for user authentication and management.

### Endpoints

#### 1. Sign In

Endpoint for user login using email or phone number. The system will send an OTP (One-Time Password) to the provided email or phone number.

- **URL**: `/auth/signin`
- **Method**: `POST`
- **Authentication**: Public
- **Request Body**:
  ```json
  {
    "provider": "email | whatsapp | sms",
    "email": "<EMAIL>",  // Required if provider is "email"
    "phoneNumber": "+628123456789"  // Required if provider is "whatsapp" or "sms"
  }
  ```
- **Response**:
  ```json
  {
    "message": "OTP sent successfully"
  }
  ```

#### 2. Sign Up

Endpoint for registering a new user.

- **URL**: `/auth/signup`
- **Method**: `POST`
- **Authentication**: Public
- **Request Body**:
  ```json
  {
    "provider": "email | whatsapp | sms",
    "name": "John Doe",
    "email": "<EMAIL>",  // Required if phoneNumber is not provided
    "phoneNumber": "+628123456789",  // Required if email is not provided
    "imageUrl": "https://example.com/profile.jpg"  // Optional
  }
  ```
- **Response**:
  ```json
  {
    "message": "User registered successfully, OTP sent"
  }
  ```

#### 3. Verify OTP

Endpoint to verify the OTP sent to the user's email or phone number. After successful verification, a JWT token will be provided.

- **URL**: `/auth/verify-otp`
- **Method**: `POST`
- **Authentication**: Public
- **Request Body**:
  ```json
  {
    "provider": "email | whatsapp | sms",
    "email": "<EMAIL>",  // Required if provider is "email"
    "phoneNumber": "+628123456789",  // Required if provider is "whatsapp" or "sms"
    "code": "123456"  // OTP code received
  }
  ```
- **Response**:
  ```json
  {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiredAt": 1621526400000,  // Timestamp in milliseconds
    "issuedAt": 1621440000000   // Timestamp in milliseconds
  }
  ```

#### 4. Get Profile

Endpoint to get the profile information of the currently logged-in user.

- **URL**: `/auth/profile`
- **Method**: `GET`
- **Authentication**: Required (JWT Token)
- **Headers**:
  ```
  Authorization: Bearer <access_token>
  ```
- **Response**:
  ```json
  {
    "id": "60d21b4667d0d8992e610c85",
    "name": "John Doe",
    "email": "<EMAIL>",
    "phoneNumber": "+628123456789",
    "imageUrl": "https://example.com/profile.jpg",
    "status": "active"
  }
  ```

### Authentication Flow

1. **Login/Register**:
   - User logs in with email/phone number or registers as a new user.
   - System sends an OTP to the user's email or phone number.

2. **OTP Verification**:
   - User enters the received OTP.
   - If the OTP is valid, the system provides a JWT token.

3. **API Authentication**:
   - For endpoints that require authentication, the JWT token must be included in the Authorization header.
   - Format: `Authorization: Bearer <token>`.

### Important Notes

- OTPs have a limited validity period, typically 5-10 minutes.
- JWT tokens also have an expiration time, typically 24 hours.
- All endpoints except those marked with `@IsPublic()` require JWT authentication.

## Assistant Module

The Assistant module provides endpoints for creating and managing AI assistants, as well as their chats and files.

### Assistant Management

#### 1. Create Assistant

Endpoint to create a new AI assistant.

- **URL**: `/assistants`
- **Method**: `POST`
- **Authentication**: Required (JWT Token)
- **Request Body**:
  ```json
  {
    "name": "Customer Support Assistant",
    "description": "An assistant that helps with customer inquiries",
    "instructions": "You are a helpful customer support assistant..."
  }
  ```
- **Response**:
  ```json
  {
    "id": "60d21b4667d0d8992e610c85",
    "name": "Customer Support Assistant",
    "description": "An assistant that helps with customer inquiries",
    "instructions": "You are a helpful customer support assistant...",
    "createdAt": "2025-05-21T05:42:22.000Z",
    "updatedAt": "2025-05-21T05:42:22.000Z"
  }
  ```

#### 2. Get All Assistants

Endpoint to retrieve all assistants belonging to the authenticated user.

- **URL**: `/assistants`
- **Method**: `GET`
- **Authentication**: Required (JWT Token)
- **Response**:
  ```json
  [
    {
      "id": "60d21b4667d0d8992e610c85",
      "name": "Customer Support Assistant",
      "description": "An assistant that helps with customer inquiries",
      "instructions": "You are a helpful customer support assistant...",
      "createdAt": "2025-05-21T05:42:22.000Z",
      "updatedAt": "2025-05-21T05:42:22.000Z"
    },
    {
      "id": "60d21b4667d0d8992e610c86",
      "name": "Sales Assistant",
      "description": "An assistant that helps with sales inquiries",
      "instructions": "You are a helpful sales assistant...",
      "createdAt": "2025-05-21T05:45:30.000Z",
      "updatedAt": "2025-05-21T05:45:30.000Z"
    }
  ]
  ```

#### 3. Get Assistant by ID

Endpoint to retrieve a specific assistant by its ID.

- **URL**: `/assistants/:assistantId`
- **Method**: `GET`
- **Authentication**: Required (JWT Token)
- **Response**:
  ```json
  {
    "id": "60d21b4667d0d8992e610c85",
    "name": "Customer Support Assistant",
    "description": "An assistant that helps with customer inquiries",
    "instructions": "You are a helpful customer support assistant...",
    "createdAt": "2025-05-21T05:42:22.000Z",
    "updatedAt": "2025-05-21T05:42:22.000Z"
  }
  ```

#### 4. Update Assistant

Endpoint to update an existing assistant.

- **URL**: `/assistants/:assistantId`
- **Method**: `PUT`
- **Authentication**: Required (JWT Token)
- **Request Body**:
  ```json
  {
    "name": "Updated Assistant Name",
    "description": "Updated description",
    "instructions": "Updated instructions..."
  }
  ```
- **Response**:
  ```json
  {
    "id": "60d21b4667d0d8992e610c85",
    "name": "Updated Assistant Name",
    "description": "Updated description",
    "instructions": "Updated instructions...",
    "createdAt": "2025-05-21T05:42:22.000Z",
    "updatedAt": "2025-05-21T05:50:15.000Z"
  }
  ```

#### 5. Delete Assistant

Endpoint to delete an assistant.

- **URL**: `/assistants/:assistantId`
- **Method**: `DELETE`
- **Authentication**: Required (JWT Token)
- **Response**:
  ```json
  {
    "message": "Assistant 60d21b4667d0d8992e610c85 deleted successfully"
  }
  ```

### Chat Management

This section provides endpoints for creating and managing conversations with AI assistants.

#### Endpoints

#### 1. Get All Chats

Endpoint to retrieve all chats for a specific assistant with pagination.

- **URL**: `/assistants/:assistantId/chats`
- **Method**: `GET`
- **Authentication**: Required (JWT Token)
- **Query Parameters**:
  - `page`: Page number (default: 1)
  - `limit`: Items per page (default: 10)
- **Response**:
  ```json
  {
    "data": [
      {
        "id": "60d21b4667d0d8992e610c90",
        "assistantId": "60d21b4667d0d8992e610c85",
        "title": "Help with customer inquiry",
        "lastMessageAt": "2025-05-21T06:30:22.000Z",
        "messageCount": 5,
        "createdAt": "2025-05-21T06:15:22.000Z",
        "updatedAt": "2025-05-21T06:30:22.000Z"
      },
      {
        "id": "60d21b4667d0d8992e610c91",
        "assistantId": "60d21b4667d0d8992e610c85",
        "title": "Product information request",
        "lastMessageAt": "2025-05-21T07:10:45.000Z",
        "messageCount": 3,
        "createdAt": "2025-05-21T07:05:30.000Z",
        "updatedAt": "2025-05-21T07:10:45.000Z"
      }
    ],
    "totalItems": 15,
    "itemsPerPage": 10,
    "totalPages": 2,
    "currentPage": 1
  }
  ```

#### 2. Get Chat by ID

Endpoint to retrieve a specific chat with its messages.

- **URL**: `/assistants/:assistantId/chats/:chatId`
- **Method**: `GET`
- **Authentication**: Required (JWT Token)
- **Response**:
  ```json
  {
    "id": "60d21b4667d0d8992e610c90",
    "assistantId": "60d21b4667d0d8992e610c85",
    "title": "Help with customer inquiry",
    "lastMessageAt": "2025-05-21T06:30:22.000Z",
    "messageCount": 5,
    "createdAt": "2025-05-21T06:15:22.000Z",
    "updatedAt": "2025-05-21T06:30:22.000Z",
    "messages": [
      {
        "id": "60d21b4667d0d8992e610c92",
        "chatId": "60d21b4667d0d8992e610c90",
        "role": "user",
        "content": "How can I reset my password?",
        "createdAt": "2025-05-21T06:15:22.000Z",
        "updatedAt": "2025-05-21T06:15:22.000Z"
      },
      {
        "id": "60d21b4667d0d8992e610c93",
        "chatId": "60d21b4667d0d8992e610c90",
        "role": "assistant",
        "content": "You can reset your password by clicking on the 'Forgot Password' link on the login page...",
        "createdAt": "2025-05-21T06:15:30.000Z",
        "updatedAt": "2025-05-21T06:15:30.000Z"
      }
    ]
  }
  ```

#### 3. Start New Chat

Endpoint to start a new conversation with an assistant.

- **URL**: `/assistants/:assistantId/chats`
- **Method**: `POST`
- **Authentication**: Required (JWT Token)
- **Request Body**:
  ```json
  {
    "message": "How can I reset my password?"
  }
  ```
- **Response**:
  ```json
  {
    "chat": {
      "id": "60d21b4667d0d8992e610c90",
      "assistantId": "60d21b4667d0d8992e610c85",
      "title": "How can I reset my password?",
      "lastMessageAt": "2025-05-21T06:15:30.000Z",
      "messageCount": 2,
      "createdAt": "2025-05-21T06:15:22.000Z",
      "updatedAt": "2025-05-21T06:15:30.000Z"
    },
    "userMessage": {
      "id": "60d21b4667d0d8992e610c92",
      "chatId": "60d21b4667d0d8992e610c90",
      "role": "user",
      "content": "How can I reset my password?",
      "createdAt": "2025-05-21T06:15:22.000Z",
      "updatedAt": "2025-05-21T06:15:22.000Z"
    },
    "assistantMessage": {
      "id": "60d21b4667d0d8992e610c93",
      "chatId": "60d21b4667d0d8992e610c90",
      "role": "assistant",
      "content": "You can reset your password by clicking on the 'Forgot Password' link on the login page...",
      "createdAt": "2025-05-21T06:15:30.000Z",
      "updatedAt": "2025-05-21T06:15:30.000Z"
    }
  }
  ```

#### 4. Send Message to Existing Chat

Endpoint to send a message to an existing chat.

- **URL**: `/assistants/:assistantId/chats/:chatId/messages`
- **Method**: `POST`
- **Authentication**: Required (JWT Token)
- **Request Body**:
  ```json
  {
    "message": "What about changing my email address?"
  }
  ```
- **Response**:
  ```json
  {
    "userMessage": {
      "id": "60d21b4667d0d8992e610c94",
      "chatId": "60d21b4667d0d8992e610c90",
      "role": "user",
      "content": "What about changing my email address?",
      "createdAt": "2025-05-21T06:20:22.000Z",
      "updatedAt": "2025-05-21T06:20:22.000Z"
    },
    "assistantMessage": {
      "id": "60d21b4667d0d8992e610c95",
      "chatId": "60d21b4667d0d8992e610c90",
      "role": "assistant",
      "content": "To change your email address, you need to go to your account settings...",
      "createdAt": "2025-05-21T06:20:30.000Z",
      "updatedAt": "2025-05-21T06:20:30.000Z"
    }
  }
  ```

#### 5. Get Chat Messages

Endpoint to retrieve all messages for a specific chat.

- **URL**: `/assistants/:assistantId/chats/:chatId/messages`
- **Method**: `GET`
- **Authentication**: Required (JWT Token)
- **Response**:
  ```json
  [
    {
      "id": "60d21b4667d0d8992e610c92",
      "chatId": "60d21b4667d0d8992e610c90",
      "role": "user",
      "content": "How can I reset my password?",
      "createdAt": "2025-05-21T06:15:22.000Z",
      "updatedAt": "2025-05-21T06:15:22.000Z"
    },
    {
      "id": "60d21b4667d0d8992e610c93",
      "chatId": "60d21b4667d0d8992e610c90",
      "role": "assistant",
      "content": "You can reset your password by clicking on the 'Forgot Password' link on the login page...",
      "createdAt": "2025-05-21T06:15:30.000Z",
      "updatedAt": "2025-05-21T06:15:30.000Z"
    },
    {
      "id": "60d21b4667d0d8992e610c94",
      "chatId": "60d21b4667d0d8992e610c90",
      "role": "user",
      "content": "What about changing my email address?",
      "createdAt": "2025-05-21T06:20:22.000Z",
      "updatedAt": "2025-05-21T06:20:22.000Z"
    },
    {
      "id": "60d21b4667d0d8992e610c95",
      "chatId": "60d21b4667d0d8992e610c90",
      "role": "assistant",
      "content": "To change your email address, you need to go to your account settings...",
      "createdAt": "2025-05-21T06:20:30.000Z",
      "updatedAt": "2025-05-21T06:20:30.000Z"
    }
  ]
  ```

#### 6. Delete Chat

Endpoint to delete a chat.

- **URL**: `/assistants/:assistantId/chats/:chatId`
- **Method**: `DELETE`
- **Authentication**: Required (JWT Token)
- **Response**:
  ```json
  {
    "message": "Chat 60d21b4667d0d8992e610c90 deleted successfully"
  }
  ```

### Important Notes

- All chat operations require authentication and ownership of the associated assistant.
- When starting a new chat, the system automatically generates a title based on the first message.
- The assistant's response is generated using OpenAI's API and may take a moment to process.
- Messages are ordered chronologically in the responses.

### File Management

This section provides endpoints for managing files associated with assistants.

#### Endpoints

#### 1. Upload Files

Endpoint to upload files for a specific assistant (maximum 5 files, 50MB per file).

- **URL**: `/assistants/:assistantId/files`
- **Method**: `POST`
- **Authentication**: Required (JWT Token)
- **Content-Type**: `multipart/form-data`
- **Request Body**:
  ```
  files: [file1, file2, ...] // Form data with files
  ```
- **Supported File Types**:
  - Text documents: .txt, .md, .pdf, .doc, .docx, .rtf, .odt
  - Spreadsheets: .csv, .xls, .xlsx, .ods
  - Presentations: .ppt, .pptx, .odp
  - Structured data: .json, .xml, .yaml, .yml
- **Response**:
  ```json
  [
    {
      "id": "60d21b4667d0d8992e610c87",
      "name": "document.pdf",
      "size": 1024567,
      "createdAt": "2025-05-21T05:55:22.000Z",
      "updatedAt": "2025-05-21T05:55:22.000Z"
    },
    {
      "id": "60d21b4667d0d8992e610c88",
      "name": "data.csv",
      "size": 52340,
      "createdAt": "2025-05-21T05:55:22.000Z",
      "updatedAt": "2025-05-21T05:55:22.000Z"
    }
  ]
  ```

#### 2. Get All Files

Endpoint to retrieve all files associated with an assistant.

- **URL**: `/assistants/:assistantId/files`
- **Method**: `GET`
- **Authentication**: Required (JWT Token)
- **Response**:
  ```json
  [
    {
      "id": "60d21b4667d0d8992e610c87",
      "name": "document.pdf",
      "size": 1024567,
      "createdAt": "2025-05-21T05:55:22.000Z",
      "updatedAt": "2025-05-21T05:55:22.000Z"
    },
    {
      "id": "60d21b4667d0d8992e610c88",
      "name": "data.csv",
      "size": 52340,
      "createdAt": "2025-05-21T05:55:22.000Z",
      "updatedAt": "2025-05-21T05:55:22.000Z"
    }
  ]
  ```

#### 3. Delete File

Endpoint to delete a file associated with an assistant.

- **URL**: `/assistants/:assistantId/files/:fileId`
- **Method**: `DELETE`
- **Authentication**: Required (JWT Token)
- **Response**:
  ```json
  {
    "message": "File 60d21b4667d0d8992e610c87 deleted successfully"
  }
  ```

### Important Notes

- All file operations require authentication and ownership of the associated assistant.
- Files are automatically processed and made available for the assistant to use in conversations.
- The maximum file size is 50MB per file.
- A maximum of 5 files can be uploaded in a single request.

## WhatsApp Module

The WhatsApp module provides endpoints for creating and managing WhatsApp instances, as well as their chats and messages.

### WhatsApp Instance Management

#### 1. Create WhatsApp Instance

Endpoint to create a new WhatsApp instance.

- **URL**: `/whatsapp`
- **Method**: `POST`
- **Authentication**: Required (JWT Token)
- **Request Body**:
  ```json
  {
    "name": "My WhatsApp",
    "phoneNumber": "+628123456789"
  }
  ```
- **Response**:
  ```json
  {
    "id": "60d21b4667d0d8992e610c85",
    "name": "My WhatsApp",
    "phoneNumber": "+628123456789",
    "userId": "60d21b4667d0d8992e610c80",
    "status": "DISCONNECTED",
    "assistantId": null,
    "enabled": false,
    "createdAt": "2025-05-21T05:42:22.000Z",
    "updatedAt": "2025-05-21T05:42:22.000Z"
  }
  ```

#### 2. Get All WhatsApp Instances

Endpoint to retrieve all WhatsApp instances for the authenticated user.

- **URL**: `/whatsapp`
- **Method**: `GET`
- **Authentication**: Required (JWT Token)
- **Response**:
  ```json
  [
    {
      "id": "60d21b4667d0d8992e610c85",
      "name": "Personal WhatsApp",
      "phoneNumber": "+628123456789",
      "status": "DISCONNECTED",
      "lastConnected": "2025-05-21T05:42:22.000Z"
    },
    {
      "id": "60d21b4667d0d8992e610c86",
      "name": "Business WhatsApp",
      "phoneNumber": "+628987654321",
      "status": "CONNECTED",
      "lastConnected": "2025-05-21T06:15:22.000Z"
    }
  ]
  ```
#### 3. Get WhatsApp Instance

Endpoint to retrieve a specific WhatsApp instance.

- **URL**: `/whatsapp/:whatsappId`
- **Method**: `GET`
- **Authentication**: Required (JWT Token)
- **Response**:
  ```json
  {
    "id": "60d21b4667d0d8992e610c85",
    "phoneNumber": "+628123456789",
    "status": "DISCONNECTED",
    "lastConnected": "2025-05-21T05:42:22.000Z"
  }
  ```

#### 4. Update WhatsApp Instance

Endpoint to update a WhatsApp instance.

- **URL**: `/whatsapp/:whatsappId`
- **Method**: `PUT`
- **Authentication**: Required (JWT Token)
- **Request Body**:
  ```json
  {
    "name": "Updated WhatsApp Name",
    "phoneNumber": "+628123456789"
  }
  ```
- **Response**:
  ```json
  {
    "id": "60d21b4667d0d8992e610c85",
    "phoneNumber": "+628123456789",
    "status": "DISCONNECTED",
    "lastConnected": "2025-05-21T05:42:22.000Z"
  }
  ```

#### 5. Delete WhatsApp Instance

Endpoint to delete a WhatsApp instance.

- **URL**: `/whatsapp/:whatsappId`
- **Method**: `DELETE`
- **Authentication**: Required (JWT Token)
- **Response**:
  ```json
  {
    "message": "WhatsApp instance with ID 60d21b4667d0d8992e610c85 removed"
  }
  ```

#### 6. Initialize WhatsApp Client

Endpoint to initialize a WhatsApp client.

- **URL**: `/whatsapp/:whatsappId/initialize`
- **Method**: `POST`
- **Authentication**: Required (JWT Token)
- **Response**:
  ```json
  {
    "message": "WhatsApp instance with ID 60d21b4667d0d8992e610c85 initialized"
  }
  ```

#### 7. Get QR Code

Endpoint to get the QR code for WhatsApp authentication.

- **URL**: `/whatsapp/:whatsappId/qr`
- **Method**: `GET`
- **Authentication**: Public
- **Query Parameters**:
  - `format`: Format of the QR code response (json, html, image). Default: image
- **Response**:
  - When format is `json`:
    ```json
    {
      "qrCode": "1@HGgstCVZc8..."
    }
    ```
  - When format is `html`: HTML page with QR code styled like WhatsApp Web
  - When format is `image`: PNG image of the QR code

#### 8. Disconnect WhatsApp

Endpoint to disconnect a WhatsApp client.

- **URL**: `/whatsapp/:whatsappId/disconnect`
- **Method**: `POST`
- **Authentication**: Required (JWT Token)
- **Response**:
  ```json
  {
    "message": "WhatsApp instance with ID 60d21b4667d0d8992e610c85 disconnected"
  }
  ```

#### 9. Logout WhatsApp

Endpoint to logout from a WhatsApp client.

- **URL**: `/whatsapp/:whatsappId/logout`
- **Method**: `POST`
- **Authentication**: Required (JWT Token)
- **Response**:
  ```json
  {
    "message": "WhatsApp instance with ID 60d21b4667d0d8992e610c85 logged out"
  }
  ```

#### 10. Assign Assistant

Endpoint to assign an AI assistant to a WhatsApp instance.

- **URL**: `/whatsapp/:whatsappId/assistant`
- **Method**: `POST`
- **Authentication**: Required (JWT Token)
- **Request Body**:
  ```json
  {
    "whatsappId": "60d21b4667d0d8992e610c85",
    "assistantId": "60d21b4667d0d8992e610c90"
  }
  ```
- **Response**:
  ```json
  {
    "message": "Assistant 60d21b4667d0d8992e610c90 assigned to whatsapp 60d21b4667d0d8992e610c85"
  }
  ```

#### 11. Enable WhatsApp

Endpoint to enable a WhatsApp instance for receiving messages.

- **URL**: `/whatsapp/:whatsappId/enable`
- **Method**: `POST`
- **Authentication**: Required (JWT Token)
- **Request Body**:
  ```json
  {
    "whatsappId": "60d21b4667d0d8992e610c85"
  }
  ```
- **Response**:
  ```json
  {
    "message": "Whatsapp 60d21b4667d0d8992e610c85 enabled"
  }
  ```

#### 12. Disable WhatsApp

Endpoint to disable a WhatsApp instance from receiving messages.

- **URL**: `/whatsapp/:whatsappId/disable`
- **Method**: `POST`
- **Authentication**: Required (JWT Token)
- **Request Body**:
  ```json
  {
    "whatsappId": "60d21b4667d0d8992e610c85"
  }
  ```
- **Response**:
  ```json
  {
    "message": "Whatsapp 60d21b4667d0d8992e610c85 disabled"
  }
  ```

### WhatsApp Chat Management

#### 1. Get All Chats

Endpoint to retrieve all chats for a specific WhatsApp instance with pagination.

- **URL**: `/whatsapp/:whatsappId/chats`
- **Method**: `GET`
- **Authentication**: Required (JWT Token)
- **Query Parameters**:
  - `page`: Page number for pagination (default: 1)
  - `limit`: Number of items per page (default: 10)
- **Response**:
  ```json
  {
    "data": [
      {
        "id": "60d21b4667d0d8992e610c85",
        "whatsappId": "60d21b4667d0d8992e610c90",
        "autoReplyEnabled": true,
        "createdAt": "2025-05-21T05:42:22.000Z",
        "messageCount": 24,
        "latestMessage": {
          "userId": "123456789",
          "role": "user",
          "content": "Hello, I need help with my order...",
          "createdAt": "2025-05-21T06:15:22.000Z"
        }
      },
      {
        "id": "60d21b4667d0d8992e610c86",
        "whatsappId": "60d21b4667d0d8992e610c90",
        "autoReplyEnabled": false,
        "createdAt": "2025-05-21T05:30:22.000Z",
        "messageCount": 12,
        "latestMessage": {
          "userId": "987654321",
          "role": "assistant",
          "content": "I'll help you track your package right away...",
          "createdAt": "2025-05-21T06:10:22.000Z"
        }
      }
    ],
    "totalItems": 45,
    "itemsPerPage": 10,
    "totalPages": 5,
    "currentPage": 1
  }
  ```

#### 2. Get Chat Messages

Endpoint to retrieve messages for a specific chat with pagination.

- **URL**: `/whatsapp/:whatsappId/chats/:chatId/messages`
- **Method**: `GET`
- **Authentication**: Required (JWT Token)
- **Query Parameters**:
  - `page`: Page number for pagination (default: 1)
  - `limit`: Number of items per page (default: 10)
- **Response**:
  ```json
  {
    "data": [
      {
        "id": "60d21b4667d0d8992e610c87",
        "chatId": "60d21b4667d0d8992e610c85",
        "role": "user",
        "content": "Hello, I need help with my order",
        "createdAt": "2025-05-21T05:42:22.000Z",
        "updatedAt": "2025-05-21T05:42:22.000Z"
      },
      {
        "id": "60d21b4667d0d8992e610c88",
        "chatId": "60d21b4667d0d8992e610c85",
        "role": "assistant",
        "content": "Hi there! I'd be happy to help with your order. Could you please provide your order number?",
        "createdAt": "2025-05-21T05:42:30.000Z",
        "updatedAt": "2025-05-21T05:42:30.000Z"
      }
    ],
    "totalItems": 24,
    "itemsPerPage": 10,
    "totalPages": 3,
    "currentPage": 1
  }
  ```

#### 3. Update Chat

Endpoint to update settings for a specific chat.

- **URL**: `/whatsapp/:whatsappId/chats/:chatId`
- **Method**: `PUT`
- **Authentication**: Required (JWT Token)
- **Request Body**:
  ```json
  {
    "autoReplyEnabled": true
  }
  ```
- **Response**:
  ```json
  {
    "id": "60d21b4667d0d8992e610c85",
    "whatsappId": "60d21b4667d0d8992e610c90",
    "chatId": "<EMAIL>",
    "threadId": "60d21b4667d0d8992e610c91",
    "autoReplyEnabled": true,
    "createdAt": "2025-05-21T05:42:22.000Z",
    "updatedAt": "2025-05-21T06:30:22.000Z"
  }
  ```

### Important Notes

- WhatsApp instances must be initialized before they can be used.
- After initialization, a QR code will be generated which needs to be scanned using the WhatsApp mobile app.
- Only one assistant can be assigned to a WhatsApp instance at a time.
- When a WhatsApp instance is enabled, it will automatically respond to incoming messages using the assigned assistant.
- The WhatsApp module uses the WhatsApp Web API to connect to WhatsApp servers.
- Chat messages are paginated to improve performance when dealing with large conversation histories.
- The `autoReplyEnabled` setting can be toggled per chat to control whether the assistant should automatically respond to messages in that chat.
