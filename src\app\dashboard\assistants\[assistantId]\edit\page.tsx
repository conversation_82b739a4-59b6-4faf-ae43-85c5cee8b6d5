"use client";

import { AppSidebar } from "@/components/app-sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { PromptAnalyzer } from "@/components/prompt-analyzer";
import { Save, AlertCircle, Trash2, FileText, Loader2, Globe, Link2, Calendar, DollarSign, Search, Image, Mail, BarChart3, Video, Zap, Info } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { useRouter, useParams } from 'next/navigation';
import React, { useEffect, useState, useCallback } from "react";
import { toast } from "sonner";
import { Toaster } from "@/components/ui/sonner";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

// Define Assistant type (can be shared if moved to a types file)
interface Assistant {
  id: string;
  name: string;
  description: string;
  instructions: string;
  createdAt: string;
  updatedAt: string;
}

interface AssistantFile {
  id: string;
  name: string;
  size: number;
  createdAt: string;
  updatedAt: string;
}

export default function EditAssistantPage() {
  const router = useRouter();
  const params = useParams();
  const assistantId = params.assistantId as string;

  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [instructions, setInstructions] = useState("");
  const [currentFiles, setCurrentFiles] = useState<AssistantFile[]>([]);
  const [newFiles, setNewFiles] = useState<File[]>([]); // For new uploads
  const [uploadMethod, setUploadMethod] = useState<'file' | 'url'>('file');
  const [url, setUrl] = useState('');
  const [isCrawling, setIsCrawling] = useState(false);
  const [crawlError, setCrawlError] = useState<string | null>(null);
  const [crawlPreview, setCrawlPreview] = useState<{content: string, filename: string} | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  
  const [isLoading, setIsLoading] = useState(true); // Initial loading for assistant data
  const [isSaving, setIsSaving] = useState(false); // For saving assistant details
  const [isUploading, setIsUploading] = useState(false); // For uploading new files
  const [isDeletingFile, setIsDeletingFile] = useState<string | null>(null); // Store ID of file being deleted
  const [error, setError] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [fileToDelete, setFileToDelete] = useState<string | null>(null);
  
  const { accessToken } = useAuth();

  // Fetch Assistant Details
  const fetchAssistantDetails = useCallback(async () => {
    if (!accessToken || !assistantId) return;
    // setIsLoading(true); // Set by initial state or if called independently
    // setError(null); // Reset error before this specific fetch if needed
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/assistants/${assistantId}`, {
        headers: { 'Authorization': `Bearer ${accessToken}` },
      });
      if (!response.ok) throw new Error("Failed to fetch assistant details.");
      const data: Assistant = await response.json();
      setName(data.name);
      setDescription(data.description);
      setInstructions(data.instructions);
    } catch (err: unknown) { 
        setError(prevError => prevError ? `${prevError}\nFetch Details Error: ${(err as Error).message}` : `Fetch Details Error: ${(err as Error).message}`); 
    }
  }, [accessToken, assistantId]);

  // Fetch Assistant Files
  const fetchAssistantFiles = useCallback(async () => {
    if (!accessToken || !assistantId) return;
    // setIsLoading(true); // Set by initial state or if called independently
    // setError(null); // Reset error before this specific fetch if needed
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/assistants/${assistantId}/files`, {
        headers: { 'Authorization': `Bearer ${accessToken}` },
      });
      if (!response.ok) throw new Error("Failed to fetch assistant files.");
      const data: AssistantFile[] = await response.json();
      setCurrentFiles(data);
    } catch (err: unknown) { 
      setError(prevError => prevError ? `${prevError}\nFetch Files Error: ${(err as Error).message}` : `Fetch Files Error: ${(err as Error).message}`);
    }
  }, [accessToken, assistantId]);

  useEffect(() => {
    const loadData = async () => {
      if (accessToken && assistantId) {
        setIsLoading(true);
        setError(null); // Clear errors before fetching
        await fetchAssistantDetails();
        await fetchAssistantFiles();
        setIsLoading(false);
      }
    };
    loadData();
  }, [accessToken, assistantId, fetchAssistantDetails, fetchAssistantFiles]);

  const handleNewFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setNewFiles(Array.from(e.target.files));
      toast.success(`${e.target.files.length} file dipilih`);
    }
  };

  const handleCrawlUrl = useCallback(async () => {
    if (!url) return;
    
    // Validasi format URL
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      setCrawlError('URL harus dimulai dengan http:// atau https://');
      toast.error('Format URL tidak valid', {
        description: 'URL harus dimulai dengan http:// atau https://'
      });
      return;
    }
    
    setIsCrawling(true);
    setCrawlError(null);
    setCrawlPreview(null);
    
    try {
      const response = await fetch('/api/crawl-url', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ url })
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Gagal mengambil konten dari URL');
      }
      
      const data = await response.json();
      
      // Simpan hasil crawling untuk preview
      setCrawlPreview({
        content: data.content,
        filename: data.filename
      });
      
      // Tampilkan preview
      setShowPreview(true);
      
      toast.success('Konten berhasil diambil dari URL', {
        description: `Preview konten dari ${url} tersedia`
      });
    } catch (err: any) {
      setCrawlError(err.message);
      toast.error('Gagal mengambil konten', {
        description: err.message
      });
    } finally {
      setIsCrawling(false);
    }
  }, [url]);
  
  // Fungsi untuk menambahkan hasil crawling ke daftar file
  const addCrawlResultToFiles = useCallback(() => {
    if (!crawlPreview) return;
    
    // Buat file dari konten yang di-crawl
    const file = new File(
      [crawlPreview.content],
      crawlPreview.filename,
      { type: 'text/plain' }
    );
    
    // Tambahkan ke daftar file yang sudah ada
    setNewFiles(prevFiles => [...prevFiles, file]);
    
    toast.success('File berhasil ditambahkan', {
      description: `File ${crawlPreview.filename} telah ditambahkan ke daftar upload`
    });
    
    // Reset preview dan URL
    setCrawlPreview(null);
    setShowPreview(false);
    setUrl('');
  }, [crawlPreview]);
  
  // Fungsi untuk membatalkan hasil crawling
  const cancelCrawlResult = useCallback(() => {
    setCrawlPreview(null);
    setShowPreview(false);
  }, []);

  const handleUpdateAssistant = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!accessToken) { setError("Not authenticated."); return; }
    
    setIsSaving(true);
    setError(null);

    const assistantData = { name, description, instructions };

    try {
      // Update assistant details
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/assistants/${assistantId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`,
        },
        body: JSON.stringify(assistantData),
      });
      if (!response.ok) {
        const errData = await response.json();
        throw new Error(errData.message || "Failed to update assistant.");
      }
      
      let fileUploadSuccess = true;
      // If new files are selected, upload them
      if (newFiles.length > 0) {
        setIsUploading(true);
        const formData = new FormData();
        newFiles.forEach(file => formData.append('files', file));

        const fileUploadResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/assistants/${assistantId}/files`, {
          method: 'POST',
          headers: { 'Authorization': `Bearer ${accessToken}` }, // No Content-Type for FormData
          body: formData,
        });
        setIsUploading(false);
        if (!fileUploadResponse.ok) {
          fileUploadSuccess = false;
          const fileErrData = await fileUploadResponse.json();
          setError(`Assistant details updated, but new file upload failed: ${fileErrData.message || fileUploadResponse.status}. Please try uploading files again.`);
          // Don't throw here, let the main success message indicate partial success
        } else {
          setNewFiles([]); // Clear selection after successful upload
          await fetchAssistantFiles(); // Refresh file list
        }
      }

      if (fileUploadSuccess) {
        // Store success message in sessionStorage to show after redirect
        sessionStorage.setItem('assistantToast', "Asisten berhasil disimpan!");
        
        // Redirect back to the assistants list page
        router.push('/dashboard/assistants');
      } else {
        // Error already set for file upload failure
      }

    } catch (err: unknown) { 
        setError((err as Error).message); 
    }
    finally { 
        setIsSaving(false); 
        setIsUploading(false); 
    }
  };

  const handleDeleteFile = async (fileId: string) => {
    if (!accessToken) {
      toast.error("Authentication token not found.");
      return;
    }
    
    // Open the confirmation dialog
    setFileToDelete(fileId);
    setDeleteDialogOpen(true);
  };

  const confirmDeleteFile = async () => {
    if (!fileToDelete || !accessToken) return;
    
    setIsDeletingFile(fileToDelete);
    setError(null);
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/assistants/${assistantId}/files/${fileToDelete}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${accessToken}` },
      });
      if (!response.ok) {
        const errData = await response.json();
        throw new Error(errData.message || "Failed to delete file.");
      }
      toast.success("File berhasil dihapus!");
      await fetchAssistantFiles(); // Refresh file list
    } catch (err: unknown) {
      console.error("Failed to delete file:", err);
      toast.error(`Gagal menghapus file: ${(err as Error).message}`);
      setError(`Gagal menghapus file: ${(err as Error).message}`);
    } finally {
      setIsDeletingFile(null);
      setFileToDelete(null);
      setDeleteDialogOpen(false);
    }
  };

  if (isLoading) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <div className="flex h-screen flex-col items-center justify-center">
            <Loader2 className="h-12 w-12 animate-spin text-primary" />
            <p className="mt-4 text-muted-foreground">Loading assistant data...</p>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <Toaster />
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Hapus File</AlertDialogTitle>
            <AlertDialogDescription>
              Apakah Anda yakin ingin menghapus file ini? Tindakan ini tidak dapat dibatalkan.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Batal</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDeleteFile} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
              Hapus
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbLink href="/dashboard/assistants">Asisten AI</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>Edit: {name || assistantId.substring(0,6)}</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>
        <div className="flex flex-1 flex-col gap-4 p-4 pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Edit Asisten AI</CardTitle>
              <CardDescription>
                Perbarui nama, deskripsi, instruksi, dan kelola knowledge base asisten Anda.
              </CardDescription>
            </CardHeader>
            <form onSubmit={handleUpdateAssistant}>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="name">Nama Asisten</Label>
                  <Input id="name" value={name} onChange={(e) => setName(e.target.value)} required />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">Deskripsi</Label>
                  <Textarea id="description" value={description} onChange={(e) => setDescription(e.target.value)} className="min-h-[100px]" required />
                </div>
                <div className="space-y-2">
                  <PromptAnalyzer
                    value={instructions}
                    onChange={setInstructions}
                    placeholder="Instruksi detail untuk asisten Anda..."
                    required
                  />
                </div>

                {/* Tools Section */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Label className="text-base">Tools & Integrations</Label>
                    <div className="flex items-center gap-1 text-xs font-medium text-orange-600 bg-orange-100 px-3 py-1 rounded-full border border-orange-200">
                      <Zap className="h-3 w-3" />
                      Coming Soon
                    </div>
                  </div>

                  <div className="rounded-lg border-2 border-dashed border-muted bg-muted/20 p-6">
                    <div className="flex items-start gap-3 mb-4">
                      <Info className="h-5 w-5 text-primary mt-0.5 flex-shrink-0" />
                      <div className="space-y-2">
                        <h3 className="font-medium text-sm">Mengapa Tools Penting?</h3>
                        <p className="text-sm text-muted-foreground">
                          Banyak user menulis instruksi seperti "buatkan jadwal di Google Calendar" atau "kirim email ke customer",
                          padahal assistant perlu di-connect secara eksplisit ke tools ini bukan hanya instruksi teks.
                        </p>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                      {/* Google Calendar */}
                      <div className="bg-background/30 border border-muted rounded-lg p-4 opacity-50 cursor-not-allowed">
                        <div className="flex items-center gap-3 mb-2">
                          <Calendar className="h-6 w-6 text-blue-500/60" />
                          <h4 className="font-medium text-sm text-muted-foreground">Google Calendar</h4>
                        </div>
                        <p className="text-xs text-muted-foreground/80">
                          Scheduling appointments, managing events, checking availability
                        </p>
                      </div>

                      {/* Xero Accounting */}
                      <div className="bg-background/30 border border-muted rounded-lg p-4 opacity-50 cursor-not-allowed">
                        <div className="flex items-center gap-3 mb-2">
                          <DollarSign className="h-6 w-6 text-green-500/60" />
                          <h4 className="font-medium text-sm text-muted-foreground">Xero</h4>
                        </div>
                        <p className="text-xs text-muted-foreground/80">
                          Invoice management, financial reports, payment tracking
                        </p>
                      </div>

                      {/* Web Search */}
                      <div className="bg-background/30 border border-muted rounded-lg p-4 opacity-50 cursor-not-allowed">
                        <div className="flex items-center gap-3 mb-2">
                          <Search className="h-6 w-6 text-purple-500/60" />
                          <h4 className="font-medium text-sm text-muted-foreground">Web Search</h4>
                        </div>
                        <p className="text-xs text-muted-foreground/80">
                          Real-time information, current news, product research
                        </p>
                      </div>

                      {/* Image Generator */}
                      <div className="bg-background/30 border border-muted rounded-lg p-4 opacity-50 cursor-not-allowed">
                        <div className="flex items-center gap-3 mb-2">
                          <Image className="h-6 w-6 text-pink-500/60" />
                          <h4 className="font-medium text-sm text-muted-foreground">Image Generator</h4>
                        </div>
                        <p className="text-xs text-muted-foreground/80">
                          AI-powered image creation, logos, marketing materials
                        </p>
                      </div>

                      {/* Video Generator */}
                      <div className="bg-background/30 border border-muted rounded-lg p-4 opacity-50 cursor-not-allowed">
                        <div className="flex items-center gap-3 mb-2">
                          <Video className="h-6 w-6 text-purple-600/60" />
                          <h4 className="font-medium text-sm text-muted-foreground">Video Generator</h4>
                        </div>
                        <p className="text-xs text-muted-foreground/80">
                          AI-powered video creation, animations, promotional content
                        </p>
                      </div>

                      {/* Email */}
                      <div className="bg-background/30 border border-muted rounded-lg p-4 opacity-50 cursor-not-allowed">
                        <div className="flex items-center gap-3 mb-2">
                          <Mail className="h-6 w-6 text-red-500/60" />
                          <h4 className="font-medium text-sm text-muted-foreground">Email</h4>
                        </div>
                        <p className="text-xs text-muted-foreground/80">
                          Send notifications, follow-ups, automated responses
                        </p>
                      </div>

                      {/* Google Sheets */}
                      <div className="bg-background/30 border border-muted rounded-lg p-4 opacity-50 cursor-not-allowed">
                        <div className="flex items-center gap-3 mb-2">
                          <BarChart3 className="h-6 w-6 text-green-600/60" />
                          <h4 className="font-medium text-sm text-muted-foreground">Google Sheets</h4>
                        </div>
                        <p className="text-xs text-muted-foreground/80">
                          Data management, lead tracking, report generation
                        </p>
                      </div>

                      {/* More Tools Placeholder */}
                      <div className="bg-background/30 border border-muted rounded-lg p-4 opacity-50 cursor-not-allowed">
                        <div className="flex items-center gap-3 mb-2">
                          <Zap className="h-6 w-6 text-orange-500/60" />
                          <h4 className="font-medium text-sm text-muted-foreground">More Tools</h4>
                        </div>
                        <p className="text-xs text-muted-foreground/80">
                          CRM, SMS, WhatsApp, and many more integrations
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Knowledge Base Section */}
                <div className="space-y-4 rounded-md border bg-background p-4 shadow">
                  <h3 className="text-lg font-semibold tracking-tight">Knowledge Base</h3>
                  {/* Display Existing Files */}
                  {currentFiles.length > 0 ? (
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">File Tersimpan:</Label>
                      <ul className="divide-y divide-border rounded-md border">
                        {currentFiles.map(file => (
                          <li key={file.id} className="flex items-center justify-between p-3 text-sm hover:bg-muted/50">
                            <div className="flex items-center gap-3 min-w-0 flex-1 pr-2">
                              <FileText className="h-5 w-5 flex-shrink-0 text-muted-foreground" />
                              <div className="flex flex-col min-w-0 flex-1">
                                <span className="font-medium truncate">{file.name}</span>
                                <span className="text-xs text-muted-foreground truncate">({(file.size / 1024).toFixed(2)} KB) - Ditambahkan: {new Date(file.createdAt).toLocaleDateString()}</span>
                              </div>
                            </div>
                            <Button 
                              type="button"
                              variant="ghost" 
                              size="icon" 
                              className="h-8 w-8"
                              onClick={() => handleDeleteFile(file.id)}
                              disabled={isDeletingFile === file.id}
                              aria-label="Delete file"
                            >
                              {isDeletingFile === file.id ? <Loader2 className="h-4 w-4 animate-spin" /> : <Trash2 className="h-4 w-4 text-destructive" />}
                            </Button>
                          </li>
                        ))}
                      </ul>
                    </div>
                  ) : (
                    <p className="py-3 text-center text-sm text-muted-foreground">Belum ada file di knowledge base.</p>
                  )}

                  {/* Upload New Files */}
                  {/* Tab selector for upload method */}
                  <div className="flex border-b mb-4">
                    <button 
                      type="button"
                      className={`px-4 py-2 flex items-center gap-1 ${uploadMethod === 'file' ? 'border-b-2 border-primary text-primary font-medium' : 'text-muted-foreground'}`}
                      onClick={() => setUploadMethod('file')}
                    >
                      <FileText className="h-4 w-4" />
                      Upload File
                    </button>
                    <button 
                      type="button"
                      className={`px-4 py-2 flex items-center gap-1 ${uploadMethod === 'url' ? 'border-b-2 border-primary text-primary font-medium' : 'text-muted-foreground'}`}
                      onClick={() => setUploadMethod('url')}
                    >
                      <Globe className="h-4 w-4" />
                      Crawl URL
                    </button>
                  </div>
                  
                  <div className="mt-8 mb-6 space-y-4 rounded-lg border-2 border-dashed border-primary/30 bg-primary/5 p-6 shadow-sm">
                    <div className="flex items-center gap-2">
                      {uploadMethod === 'file' ? (
                        <>
                          <FileText className="h-6 w-6 text-primary" />
                          <h3 className="text-lg font-semibold text-primary/90">Upload File Baru</h3>
                        </>
                      ) : (
                        <>
                          <Globe className="h-6 w-6 text-primary" />
                          <h3 className="text-lg font-semibold text-primary/90">Crawl URL</h3>
                        </>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {uploadMethod === 'file' 
                        ? 'Tambahkan file ke knowledge base asisten untuk meningkatkan kemampuannya.'
                        : 'Tambahkan konten dari URL ke knowledge base asisten. Konten akan dikonversi menjadi file teks.'}
                    </p>
                    <div className="mt-2">
                      {uploadMethod === 'file' ? (
                        <>
                          <Label htmlFor="knowledge-upload" className="mb-2 block text-sm font-medium">Pilih File:</Label>
                          <div className="relative">
                            <Button 
                              type="button"
                              variant="outline"
                              className="bg-primary/10 text-primary hover:bg-primary/20 hover:text-primary mr-2"
                              onClick={() => document.getElementById('knowledge-upload')?.click()}
                              disabled={isSaving || isUploading}
                            >
                              <FileText className="mr-2 h-4 w-4" />
                              Pilih File
                            </Button>
                            <span className="text-sm text-muted-foreground">
                              {newFiles.length > 0 
                                ? `${newFiles.length} file dipilih` 
                                : "Belum ada file dipilih"}
                            </span>
                            <Input 
                              id="knowledge-upload" 
                              type="file" 
                              className="sr-only"
                              onChange={handleNewFileChange}
                              multiple
                              accept=".txt,.md,.pdf,.doc,.docx,.rtf,.odt,.csv,.xls,.xlsx,.ods,.ppt,.pptx,.odp,.json,.xml,.yaml,.yml"
                              disabled={isSaving || isUploading}
                            />
                          </div>
                          <p className="mt-1 text-xs text-muted-foreground">Format yang didukung: TXT, PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, dll.</p>
                        </>
                      ) : (
                        <>
                          <Label htmlFor="url-input" className="mb-2 block text-sm font-medium">Masukkan URL: <span className="text-muted-foreground font-normal">(format: https://example.com)</span></Label>
                          <div className="flex gap-2">
                            <div className="relative flex-1">
                              <Input
                                id="url-input"
                                type="url"
                                placeholder="https://example.com/article"
                                value={url}
                                onChange={(e) => setUrl(e.target.value)}
                                disabled={isSaving || isCrawling}
                                className={`pr-8 ${!url.startsWith('http://') && !url.startsWith('https://') && url.length > 0 ? 'border-destructive focus-visible:ring-destructive' : ''}`}
                              />
                              {url && (
                                <button
                                  type="button"
                                  className="absolute right-2 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                                  onClick={() => setUrl('')}
                                  disabled={isSaving || isCrawling}
                                >
                                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                                </button>
                              )}
                            </div>
                            <Button
                              type="button"
                              onClick={handleCrawlUrl}
                              disabled={!url || isSaving || isCrawling || (!url.startsWith('http://') && !url.startsWith('https://') && url.length > 0)}
                              className="bg-primary/10 text-primary hover:bg-primary/20 hover:text-primary"
                            >
                              {isCrawling ? (
                                <>
                                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                  Crawling...
                                </>
                              ) : (
                                <>
                                  <Link2 className="mr-2 h-4 w-4" />
                                  Crawl
                                </>
                              )}
                            </Button>
                          </div>
                          {crawlError && (
                            <div className="mt-2 text-sm text-destructive">
                              <AlertCircle className="inline-block mr-1 h-4 w-4" />
                              {crawlError}
                            </div>
                          )}
                          <p className="mt-1 text-xs text-muted-foreground">
                            Konten dari URL akan dikonversi menjadi file teks. Anda dapat melihat preview sebelum menambahkannya.
                          </p>
                          {url.length > 0 && !url.startsWith('http://') && !url.startsWith('https://') && (
                            <p className="mt-1 text-xs text-destructive flex items-center">
                              <AlertCircle className="h-3 w-3 mr-1" />
                              URL harus dimulai dengan http:// atau https://
                            </p>
                          )}
                          
                          {/* Preview hasil crawling */}
                          {showPreview && crawlPreview && (
                            <div className="mt-4 border rounded-md p-3 sm:p-4 bg-muted/30">
                              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 mb-3">
                                <h4 className="font-medium text-sm sm:text-base break-all">Preview: {crawlPreview.filename}</h4>
                                <div className="flex gap-2 w-full sm:w-auto">
                                  <Button 
                                    type="button" 
                                    variant="outline" 
                                    size="sm"
                                    onClick={cancelCrawlResult}
                                    className="text-destructive border-destructive hover:bg-destructive/10 flex-1 sm:flex-none"
                                  >
                                    Batalkan
                                  </Button>
                                  <Button 
                                    type="button" 
                                    variant="default" 
                                    size="sm"
                                    onClick={addCrawlResultToFiles}
                                    className="flex-1 sm:flex-none"
                                  >
                                    Tambahkan ke File
                                  </Button>
                                </div>
                              </div>
                              <div className="max-h-72 sm:max-h-96 overflow-y-auto border rounded p-2 sm:p-3 bg-card text-sm">
                                <pre className="whitespace-pre-wrap break-words font-mono text-xs sm:text-sm">
                                  {crawlPreview.content}
                                </pre>
                              </div>
                              <p className="text-xs text-muted-foreground mt-2 text-center sm:text-left">
                                {Math.round(crawlPreview.content.length / 1024 * 100) / 100} KB • {crawlPreview.content.split(/\s+/).length} kata
                              </p>
                            </div>
                          )}
                        </>
                      )}
                    </div>
                    {newFiles.length > 0 && (
                      <div className="mt-2 space-y-1">
                        <p className="text-sm font-medium">File akan diupload:</p>
                        <ul className="list-disc pl-5 text-sm text-muted-foreground">
                          {newFiles.map((file, index) => (
                            <li key={index}>{file.name} ({(file.size / 1024).toFixed(2)} KB)</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>

                {error && (
                  <div className="mt-4 flex items-center gap-2 rounded-md border border-red-500 bg-red-50 p-3 text-sm text-red-700">
                    <AlertCircle className="h-5 w-5 flex-shrink-0" />
                    <div className="flex flex-col">
                      {error.split('\n').map((line, i) => <span key={i}>{line}</span>)}
                    </div>
                  </div>
                )}
              </CardContent>
              <CardFooter className="flex items-center justify-between border-t pt-6">
                <Button variant="outline" type="button" onClick={() => router.push('/dashboard/assistants')} disabled={isSaving || isUploading}>
                  Kembali ke Daftar
                </Button>
                <Button type="submit" disabled={isSaving || isUploading || (newFiles.length === 0 && name === "" && description === "" && instructions === "")}>
                  {(isSaving || isUploading) ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {isUploading ? "Mengupload File..." : "Menyimpan Asisten..."}
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Simpan Perubahan
                    </>
                  )}
                </Button>
              </CardFooter>
            </form>
          </Card>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
