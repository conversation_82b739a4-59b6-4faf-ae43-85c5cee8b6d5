"use client";

import React, { useState, useEffect, useCallback, useRef } from 'react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { AppSidebar } from '@/components/app-sidebar';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Separator } from '@/components/ui/separator';
import { SidebarInset, SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { Toaster } from "@/components/ui/sonner";
import {
  ArrowLeft,
  Loader2,
  AlertTriangle,
  Save,
  Trash2,
  PlayCircle,
  StopCircle,
  CheckCircle,
  XCircle,
  Settings2,
  Bot,
  RefreshCw,
  ExternalLink,
  Eye,
  EyeOff
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface TelegramInstance {
  id: string;
  name: string;
  botToken: string;
  username: string;
  status: 'CREATED' | 'CONNECTED' | 'DISCONNECTED' | 'ERROR' | string;
  lastConnected?: string;
  assistant?: {
    id: string;
    name: string;
  };
  assistantId?: string;
}

interface Assistant {
  id: string;
  name: string;
}

export default function TelegramInstancePage() {
  const { accessToken } = useAuth();
  const params = useParams();
  const router = useRouter();
  const telegramId = params.telegramId as string;
  
  const [instance, setInstance] = useState<TelegramInstance | null>(null);
  const [assistants, setAssistants] = useState<Assistant[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  
  // Edit form states
  const [editName, setEditName] = useState('');
  const [editBotToken, setEditBotToken] = useState('');
  const [editUsername, setEditUsername] = useState('');
  const [selectedAssistantId, setSelectedAssistantId] = useState<string>('');
  const [showBotToken, setShowBotToken] = useState(false);

  // Delete dialog state
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  const API_URL = process.env.NEXT_PUBLIC_API_URL;

  const fetchInstanceDetails = useCallback(async () => {
    if (!accessToken || !telegramId) return;

    setIsLoading(true);
    setError(null);
    try {
      const response = await fetch(`${API_URL}/telegram/${telegramId}`, {
        headers: { Authorization: `Bearer ${accessToken}` },
        cache: 'no-store',
      });

      if (!response.ok) {
        const errData = await response.json().catch(() => ({}));
        throw new Error(errData.message || 'Gagal mengambil detail bot Telegram.');
      }

      const data: TelegramInstance = await response.json();
      setInstance(data);

      // Update form fields
      setEditName(data.name || '');
      setEditBotToken(data.botToken || '');
      setEditUsername(data.username || '');
      setSelectedAssistantId(data.assistant?.id || 'none');

    } catch (err: any) {
      setError(err.message);
      toast.error(`Error: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  }, [accessToken, telegramId, API_URL]);

  const fetchAssistants = useCallback(async () => {
    if (!accessToken) return;
    
    try {
      const response = await fetch(`${API_URL}/assistants`, {
        headers: { Authorization: `Bearer ${accessToken}` },
      });
      
      if (response.ok) {
        const data: Assistant[] = await response.json();
        setAssistants(data);
      }
    } catch (err) {
      console.error('Failed to fetch assistants:', err);
    }
  }, [accessToken, API_URL]);

  useEffect(() => {
    fetchInstanceDetails();
    fetchAssistants();
  }, [fetchInstanceDetails, fetchAssistants]);

  const handleSimpleAction = async (action: 'initialize' | 'disconnect') => {
    const actionMessages = {
      initialize: { loading: 'Menghubungkan bot...', success: 'Bot berhasil dihubungkan.', error: 'Gagal menghubungkan bot.' },      
      disconnect: { loading: 'Memutuskan koneksi bot...', success: 'Bot berhasil diputuskan.', error: 'Gagal memutuskan koneksi bot.' },
    };

    setActionLoading(action);
    toast.loading(actionMessages[action].loading, { id: `action-${action}` });

    try {
      const response = await fetch(`${API_URL}/telegram/${telegramId}/${action}`, {
        method: 'POST',
        headers: { Authorization: `Bearer ${accessToken}` },
      });
      
      const resData = await response.json();
      if (!response.ok) throw new Error(resData.message || actionMessages[action].error);
      toast.success(resData.message || actionMessages[action].success, { id: `action-${action}` });

      fetchInstanceDetails(); // Refresh instance details
    } catch (err: any) {
      toast.error(err.message || actionMessages[action].error, { id: `action-${action}` });
      fetchInstanceDetails(); // Still refresh details to get latest status
    } finally {
      setActionLoading(null);
    }
  };

  const handleUpdateInstance = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!accessToken || !telegramId) return;
    
    setActionLoading('update');
    toast.loading('Menyimpan perubahan...', { id: 'update-instance' });
    
    try {
      const response = await fetch(`${API_URL}/telegram/${telegramId}`, {
        method: 'PUT',
        headers: { 
          'Content-Type': 'application/json', 
          'Authorization': `Bearer ${accessToken}` 
        },
        body: JSON.stringify({
          name: editName.trim(),
          botToken: editBotToken.trim(),
          username: editUsername.trim(),
          assistantId: selectedAssistantId === "none" ? null : selectedAssistantId || null
        }),
      });
      
      const resData = await response.json();
      
      if (!response.ok) {
        throw new Error(resData.message || 'Gagal memperbarui pengaturan bot Telegram.');
      }
      
      toast.success('Bot Telegram berhasil diperbarui', { 
        id: 'update-instance',
        duration: 3000
      });
      
      // Refresh to get the latest data
      await fetchInstanceDetails();
      
    } catch (err: any) {
      toast.error(err.message || 'Gagal memperbarui bot Telegram.', { 
        id: 'update-instance',
        duration: 5000
      });
    } finally {
      setActionLoading(null);
    }
  };

  const handleDeleteInstance = async () => {
    if (!accessToken || !telegramId) return;
    
    setActionLoading('delete');
    toast.loading('Menghapus bot Telegram...', { id: 'delete-instance' });
    
    try {
      const response = await fetch(`${API_URL}/telegram/${telegramId}`, {
        method: 'DELETE',
        headers: { Authorization: `Bearer ${accessToken}` },
      });
      
      const resData = await response.json();
      
      if (!response.ok) {
        throw new Error(resData.message || 'Gagal menghapus bot Telegram.');
      }
      
      toast.success('Bot Telegram berhasil dihapus', { 
        id: 'delete-instance',
        duration: 3000
      });
      
      // Redirect to telegram list page
      router.push('/dashboard/telegram');
      
    } catch (err: any) {
      toast.error(err.message || 'Gagal menghapus bot Telegram.', { 
        id: 'delete-instance',
        duration: 5000
      });
    } finally {
      setActionLoading(null);
      setDeleteDialogOpen(false);
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'connected': return 'default';
      case 'created': return 'secondary';
      case 'disconnected': return 'outline';
      case 'error': return 'destructive';
      case 'failed': return 'destructive';
      default: return 'secondary';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'connected': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'created': return <Settings2 className="h-4 w-4 text-blue-500" />;
      case 'disconnected': return <XCircle className="h-4 w-4 text-gray-500" />;
      case 'error': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'failed': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default: return <Settings2 className="h-4 w-4 text-gray-500" />;
    }
  };

  if (isLoading && !instance) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <main className="flex-1 p-4 md:p-6 space-y-6">
            <Card>
              <CardContent className="flex items-center justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin mr-2" />
                <span>Memuat detail bot Telegram...</span>
              </CardContent>
            </Card>
          </main>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  if (error && !instance) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <main className="flex-1 p-4 md:p-6 space-y-6">
            <Card>
              <CardContent className="flex items-center justify-center py-8 text-center">
                <div>
                  <AlertTriangle className="h-8 w-8 text-destructive mx-auto mb-2" />
                  <p className="text-destructive font-medium">Terjadi Kesalahan</p>
                  <p className="text-muted-foreground">{error}</p>
                  <Button variant="outline" size="sm" onClick={fetchInstanceDetails} className="mt-4">
                    <RefreshCw className="mr-2 h-4 w-4"/> Coba Lagi
                  </Button>
                </div>
              </CardContent>
            </Card>
          </main>
        </SidebarInset>
      </SidebarProvider>
    );
  }
  
  if (!instance) {
    return (
        <SidebarProvider><AppSidebar /><SidebarInset>
            <div className="p-6">Bot tidak ditemukan. <Link href="/dashboard/telegram" className="text-blue-500 hover:underline">Kembali ke daftar</Link>.</div>
        </SidebarInset></SidebarProvider>
    );
  }

  return (
    <SidebarProvider data-telegram-id={telegramId}>
      <AppSidebar />
      <Toaster />
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Hapus Bot Telegram</AlertDialogTitle>
            <AlertDialogDescription>
              Apakah Anda yakin ingin menghapus bot Telegram "{instance.name}"? 
              Tindakan ini tidak dapat dibatalkan dan semua data terkait akan dihapus secara permanen.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={actionLoading === 'delete'}>Batal</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteInstance}
              disabled={actionLoading === 'delete'}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {actionLoading === 'delete' ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Menghapus...
                </>
              ) : (
                'Hapus Bot'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block"><BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink></BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem className="hidden md:block"><BreadcrumbLink href="/dashboard/telegram">Pengaturan Telegram</BreadcrumbLink></BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem><BreadcrumbPage>{instance.name || 'Kelola Bot'}</BreadcrumbPage></BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>

        <main className="flex-1 p-4 md:p-6 space-y-8">
          <div className="mb-6">
            <Link href="/dashboard/telegram" passHref>
              <Button variant="outline" size="sm"><ArrowLeft className="mr-2 h-4 w-4" /> Kembali ke Daftar Bot Telegram</Button>
            </Link>
          </div>

          {/* Instance Overview & Status Card */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-2xl">{instance.name}</CardTitle>
                  <CardDescription>@{instance.username}</CardDescription>
                </div>
                <Button variant="outline" size="icon" onClick={fetchInstanceDetails} title="Refresh Data Bot">
                    <RefreshCw className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Label>Status:</Label>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(instance.status)}
                  <Badge variant={getStatusBadgeVariant(instance.status) as any}>{instance.status}</Badge>
                </div>
              </div>
              {instance.lastConnected && (
                <div className="flex items-center space-x-2">
                  <Label>Terakhir Terhubung:</Label>
                  <span className="text-sm text-muted-foreground">
                    {new Date(instance.lastConnected).toLocaleString()}
                  </span>
                </div>
              )}
              {instance.assistant && (
                <div className="flex items-center space-x-2">
                  <Label>Asisten Terhubung:</Label>
                  <div className="flex items-center space-x-2">
                    <Bot className="h-4 w-4 text-blue-500" />
                    <span className="text-sm font-medium">{instance.assistant.name}</span>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Bot Control Card */}
          <Card>
            <CardHeader>
              <CardTitle>Kontrol Bot</CardTitle>
              <CardDescription>
                Kelola status koneksi bot Telegram Anda.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {instance?.status?.toLowerCase() === 'created' || instance?.status?.toLowerCase() === 'disconnected' || instance?.status?.toLowerCase() === 'failed' ? (
                <div>
                  <p className="text-muted-foreground mb-4">Bot belum terhubung. Klik tombol di bawah untuk menghubungkan bot.</p>
                  <Button
                    onClick={() => handleSimpleAction('initialize')}
                    variant="default"
                    disabled={actionLoading === 'initialize'}
                  >
                    {actionLoading === 'initialize' ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Menghubungkan...
                      </>
                    ) : (
                      <>
                        <PlayCircle className="mr-2 h-4 w-4" /> Hubungkan Bot
                      </>
                    )}
                  </Button>
                </div>
              ) : instance?.status?.toLowerCase() === 'connected' ? (
                <div>
                  <p className="text-green-600 font-semibold mb-4">Bot Terhubung</p>
                  <p className="text-muted-foreground mb-4">Bot Telegram Anda sedang aktif dan siap menerima pesan.</p>
                  <div className="flex space-x-2">
                    <Button
                      onClick={() => handleSimpleAction('disconnect')}
                      variant="outline"
                      disabled={actionLoading === 'disconnect'}
                    >
                      {actionLoading === 'disconnect' ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Memutuskan...
                        </>
                      ) : (
                        <>
                          <StopCircle className="mr-2 h-4 w-4" /> Putuskan Koneksi
                        </>
                      )}
                    </Button>
                    <Button variant="outline" asChild>
                      <a href={`https://t.me/${instance.username}`} target="_blank" rel="noopener noreferrer">
                        <ExternalLink className="mr-2 h-4 w-4" />
                        Buka di Telegram
                      </a>
                    </Button>
                  </div>
                </div>
              ) : instance?.status?.toLowerCase() === 'error' ? (
                <div>
                  <p className="text-destructive font-semibold mb-2">Terjadi Error</p>
                  <p className="text-muted-foreground mb-4">Bot mengalami masalah koneksi. Silakan coba hubungkan kembali.</p>
                  <Button
                    onClick={() => handleSimpleAction('initialize')}
                    variant="outline"
                    disabled={actionLoading === 'initialize'}
                  >
                    {actionLoading === 'initialize' ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Menghubungkan...
                      </>
                    ) : (
                      <>
                        <PlayCircle className="mr-2 h-4 w-4" /> Coba Hubungkan Lagi
                      </>
                    )}
                  </Button>
                </div>
              ) : instance?.status?.toLowerCase() === 'failed' ? (
                <div>
                  <p className="text-destructive font-semibold mb-2">Koneksi Gagal</p>
                  <p className="text-muted-foreground mb-4">Bot gagal terhubung. Periksa token bot dan coba hubungkan kembali.</p>
                  <Button
                    onClick={() => handleSimpleAction('initialize')}
                    variant="outline"
                    disabled={actionLoading === 'initialize'}
                  >
                    {actionLoading === 'initialize' ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Menghubungkan...
                      </>
                    ) : (
                      <>
                        <PlayCircle className="mr-2 h-4 w-4" /> Coba Hubungkan Lagi
                      </>
                    )}
                  </Button>
                </div>
              ) : (
                <div>
                  <p className="text-muted-foreground">Status bot tidak dikenal. Silakan refresh halaman atau hubungi support.</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Edit Instance Card */}
          <Card>
            <CardHeader><CardTitle>Pengaturan Bot Telegram</CardTitle></CardHeader>
            <form onSubmit={handleUpdateInstance}>
              <CardContent className="space-y-4 pb-8">
                <div className="space-y-2">
                  <Label htmlFor="bot-name-edit">Nama Bot</Label>
                  <Input id="bot-name-edit" value={editName} onChange={(e) => setEditName(e.target.value)} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="bot-username-edit">Username Bot</Label>
                  <div className="relative">
                    <Input
                      id="bot-username-edit"
                      value={editUsername}
                      onChange={(e) => setEditUsername(e.target.value)}
                      placeholder="username_bot"
                      className="pl-8"
                    />
                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground text-sm">@</span>
                  </div>
                  <p className="text-xs text-muted-foreground">Username bot Telegram tanpa simbol @.</p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="bot-token-edit">Bot Token</Label>
                  <div className="relative">
                    <Input
                      id="bot-token-edit"
                      value={editBotToken}
                      onChange={(e) => setEditBotToken(e.target.value)}
                      type={showBotToken ? "text" : "password"}
                      className="pr-10"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowBotToken(!showBotToken)}
                    >
                      {showBotToken ? (
                        <EyeOff className="h-4 w-4 text-muted-foreground" />
                      ) : (
                        <Eye className="h-4 w-4 text-muted-foreground" />
                      )}
                    </Button>
                  </div>
                  <p className="text-xs text-muted-foreground">Token bot dari BotFather. Hati-hati saat mengubah token.</p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="assistant-select">Asisten AI</Label>
                  <Select value={selectedAssistantId} onValueChange={setSelectedAssistantId}>
                    <SelectTrigger>
                      <SelectValue placeholder="Pilih asisten AI" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">Tidak ada asisten</SelectItem>
                      {assistants.map((assistant) => (
                        <SelectItem key={assistant.id} value={assistant.id}>
                          {assistant.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground">Pilih asisten AI yang akan menangani percakapan di bot ini.</p>
                </div>
              </CardContent>
              <CardFooter className="border-t pt-4">
                <Button
                  type="submit"
                  disabled={actionLoading === 'update'}
                  className="mr-2"
                >
                  {actionLoading === 'update' ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Menyimpan...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Simpan Perubahan
                    </>
                  )}
                </Button>
              </CardFooter>
            </form>
          </Card>

          {/* Danger Zone Card */}
          <Card className="border-destructive">
            <CardHeader>
              <CardTitle className="text-destructive">Zona Berbahaya</CardTitle>
              <CardDescription>
                Tindakan di bawah ini bersifat permanen dan tidak dapat dibatalkan.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">Hapus Bot Telegram</h4>
                  <p className="text-sm text-muted-foreground">
                    Hapus bot ini secara permanen beserta semua data terkait.
                  </p>
                </div>
                <Button
                  variant="destructive"
                  onClick={() => setDeleteDialogOpen(true)}
                  disabled={actionLoading === 'delete'}
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Hapus Bot
                </Button>
              </div>
            </CardContent>
          </Card>
        </main>
      </SidebarInset>
    </SidebarProvider>
  );
}
