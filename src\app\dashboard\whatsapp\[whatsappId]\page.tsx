"use client";

import React, { useEffect, useState, useCallback, useRef } from 'react';
import Link from 'next/link';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { AppSidebar } from '@/components/app-sidebar';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { SidebarInset, SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
// TODO: Uncomment when backend adds 'enabled' field to WhatsApp instance response
// import { Switch } from '@/components/ui/switch';
import { toast } from 'sonner';
import { Toaster } from "@/components/ui/sonner";
import {
  ArrowLeft,
  Loader2,
  AlertTriangle,
  Save,
  Trash2,
  QrCode,
  PlayCircle,
  StopCircle,
  LogOut,
  CheckCircle,
  XCircle,
  Settings2,
  Bot,
  RefreshCw,
  ExternalLink
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface WhatsAppInstanceDetails {
  id: string;
  name: string;
  phoneNumber: string;
  status: 'connected' | 'disconnected' | 'initializing' | 'qr_pending' | 'error' | string;
  assistantId?: string | null;
  assistant?: {
    id: string;
    name: string;
  } | null;
  enabled?: boolean;
  lastConnected?: string;
}

interface Assistant {
  id: string;
  name: string;
  model: string;
  description?: string;
}

export default function ManageWhatsAppInstancePage() {
  const { accessToken } = useAuth();
  const router = useRouter();
  const params = useParams();
  const whatsappId = params.whatsappId as string;

  const [instance, setInstance] = useState<WhatsAppInstanceDetails | null>(null);
  const [assistants, setAssistants] = useState<Assistant[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  
  const [qrCodeUrl, setQrCodeUrl] = useState<string | null>(null);
  const [qrCodeJson, setQrCodeJson] = useState<string | null>(null);
  const [qrCodeHtmlUrl, setQrCodeHtmlUrl] = useState<string | null>(null);
  const [isQrLoading, setIsQrLoading] = useState(false);
  const [qrPollingIntervalId, setQrPollingIntervalId] = useState<NodeJS.Timeout | null>(null);
  const [statusPollingIntervalId, setStatusPollingIntervalId] = useState<NodeJS.Timeout | null>(null);
  const [isPolling, setIsPolling] = useState(false); // Single flag to track if any polling is active
  const [lastPolledStatus, setLastPolledStatus] = useState<string | null>(null);
  
  // Fixed polling intervals
  const QR_POLLING_INTERVAL = 10000; // 10 seconds for QR code
  const STATUS_POLLING_INTERVAL = 3000; // 3 seconds for status

  // Form states for editing
  const [editName, setEditName] = useState('');
  const [editPhoneNumber, setEditPhoneNumber] = useState('');
  const [selectedAssistantId, setSelectedAssistantId] = useState<string | undefined>(undefined);
  // TODO: Uncomment when backend adds 'enabled' field to WhatsApp instance response
  // const [instanceEnabled, setInstanceEnabled] = useState(false);

  const API_URL = process.env.NEXT_PUBLIC_API_URL;

  // Forward declaration of functions to avoid circular dependencies
  const fetchInstanceDetailsRef = useRef<() => Promise<void>>(async () => {});
  
  // Define fetchQrCode first as it's a dependency for startQrPolling
  const fetchQrCode = useCallback(async (format: 'image' | 'json' | 'html') => {
    if (!whatsappId) {
      return;
    }
    // setIsQrLoading(true); // Managed by manual click handlers now for non-polling visual feedback

    try {
      const qrResponse = await fetch(`${API_URL}/whatsapp/${whatsappId}/qr?format=${format}`);
      if (!qrResponse.ok) {
        let errorMessage = `Gagal mengambil QR code (${format}). Status: ${qrResponse.status}`;
        try {
          const errorBody = await qrResponse.text();
          if (qrResponse.headers.get("content-type")?.includes("application/json")) {
              const errData = JSON.parse(errorBody);
              errorMessage = errData.message || errorMessage + ` - ${errorBody.substring(0,100)}`;
          } else {
              errorMessage += ` - Respon: ${errorBody.substring(0,100)}`;
          }
        } catch (e) { /* ignore */ }
        throw new Error(errorMessage);
      }
      if (format === 'image') {
        const imageBlob = await qrResponse.blob();
        if (imageBlob.size === 0) {
          setQrCodeUrl(null);
          return;
        }
        const objectUrl = URL.createObjectURL(imageBlob);
        setQrCodeUrl(objectUrl);
      } else if (format === 'json') {
        const jsonData = await qrResponse.json();
        setQrCodeJson(jsonData.qrCode || 'Format JSON tidak valid.');
      }
      // console.log(`QR Code (${format}) loaded via polling/auto-fetch.`);
    } catch (err: any) {
      console.error("Error fetching QR code:", err.message);
      if (format === 'image') setQrCodeUrl(null);
      if (format === 'json') setQrCodeJson(null);
    } finally {
      // setIsQrLoading(false); // Managed by manual click handlers
    }
  }, [API_URL, whatsappId]); // whatsappId is a dependency

  // Single function to stop ALL polling
  const stopAllPolling = useCallback(() => {
    // Stop QR polling
    if (qrPollingIntervalId) {
      window.clearInterval(qrPollingIntervalId);
      setQrPollingIntervalId(null);
    }

    // Stop status polling
    if (statusPollingIntervalId) {
      window.clearInterval(statusPollingIntervalId);
      setStatusPollingIntervalId(null);
    }

    // Reset polling state
    setIsPolling(false);
    setLastPolledStatus(null);

    // Clear QR code data
    setQrCodeUrl(null);
    setQrCodeJson(null);
    setQrCodeHtmlUrl(null);
  }, [qrPollingIntervalId, statusPollingIntervalId]);

  // Start polling with clear separation between QR and status polling
  const startPolling = useCallback(() => {
    // Don't start if already polling
    if (isPolling) {
      return;
    }

    // Stop any existing polling first
    stopAllPolling();

    // Set polling flag
    setIsPolling(true);

    // Start QR polling
    fetchQrCode('image'); // Fetch immediately
    const qrInterval = setInterval(() => {
      fetchQrCode('image');
    }, QR_POLLING_INTERVAL);
    setQrPollingIntervalId(qrInterval);

    // Start status polling
    const statusInterval = setInterval(() => {
      if (document.visibilityState === 'visible' && fetchInstanceDetailsRef.current) {
        fetchInstanceDetailsRef.current();
      }
    }, STATUS_POLLING_INTERVAL);
    setStatusPollingIntervalId(statusInterval);
  }, [isPolling, stopAllPolling, fetchQrCode, STATUS_POLLING_INTERVAL, QR_POLLING_INTERVAL]);

  const fetchInstanceDetails = useCallback(async () => {
    // Store reference to this function to avoid circular dependencies
    fetchInstanceDetailsRef.current = fetchInstanceDetails;
    if (!accessToken || !whatsappId) return;

    // Only set loading on initial load, not during polling
    if (!instance) {
      setIsLoading(true);
    }

    setError(null);
    try {
      const response = await fetch(`${API_URL}/whatsapp/${whatsappId}`, {
        headers: { Authorization: `Bearer ${accessToken}` },
        // Add cache busting to prevent cached responses
        cache: 'no-store',
      });
      
      if (!response.ok) {
        const errData = await response.json().catch(() => ({}));
        throw new Error(errData.message || 'Gagal mengambil detail instansi.');
      }
      
      const data: WhatsAppInstanceDetails = await response.json();

      const newStatus = data.status?.toLowerCase();

      setInstance(data);
      setEditName(data.name);
      setEditPhoneNumber(data.phoneNumber);

      // Handle both formats: nested assistant object or direct assistantId
      if (data.assistant && data.assistant.id) {
        setSelectedAssistantId(data.assistant.id);
      } else if (data.assistantId) {
        setSelectedAssistantId(data.assistantId);
      } else {
        setSelectedAssistantId(undefined);
      }

      // TODO: Uncomment when backend adds 'enabled' field to WhatsApp instance response
      // Set enabled state with fallback to false if undefined
      // setInstanceEnabled(data.enabled ?? false);

      // Store previous polled status for comparison
      const previousPolledStatus = lastPolledStatus;
      setLastPolledStatus(newStatus);

      // Check if status has changed since last poll
      const statusChanged = previousPolledStatus && previousPolledStatus !== newStatus;
      
      // IMPORTANT: If we've reached a stable state, IMMEDIATELY stop ALL polling
      if (newStatus === 'connected' || newStatus === 'disconnected' || newStatus === 'error') {
        stopAllPolling(); // Use single function to stop all polling

        // Specific handling for max QR retries error
        const errorMessage = (data as any).message || '';
        if (newStatus === 'error' && errorMessage.toLowerCase().includes('max qr retries')) {
          toast.error('Batas maksimum QR code tercapai. Silakan klik tombol "Munculkan QR Code" untuk mencoba lagi.', {
            id: `whatsapp-maxqr-${whatsappId}`,
            duration: 5000
          });
        }

        // If status has changed to connected, show toast and FORCE REFRESH the page
        if (statusChanged && newStatus === 'connected') {
          toast.success('WhatsApp berhasil terhubung! Menyegarkan halaman...', {
            id: `whatsapp-connected-${whatsappId}`,
            duration: 2000
          });

          // Force refresh the page after a short delay to allow the toast to be seen
          setTimeout(() => {
            window.location.reload();
          }, 1500);
        }

        // Exit early to prevent any further polling logic from executing
        return;
      }

      // Only continue with polling for pending states
      if (newStatus === 'qr_pending' || newStatus === 'initializing' || newStatus === 'connecting') {
        setQrCodeHtmlUrl(`${API_URL}/whatsapp/${whatsappId}/qr?format=html`);

        // Start polling if not already polling
        if (!isPolling) {
          startPolling();
        }
      } else {
        stopAllPolling();
      }

    } catch (err: any) {
      setError(err.message);
      
      // Check if the error is related to max QR retries
      if (err.message && err.message.toLowerCase().includes('max qr retries')) {
        stopAllPolling(); // Use single function to stop all polling
        toast.error('Batas maksimum QR code tercapai. Silakan klik tombol "Munculkan QR Code" untuk mencoba lagi.', {
          id: `whatsapp-maxqr-error-${whatsappId}`,
          duration: 5000
        });
      } else {
        toast.error(`Error: ${err.message}`);
      }
    } finally {
      setIsLoading(false);
    }
  }, [accessToken, whatsappId, API_URL, startPolling, stopAllPolling, lastPolledStatus, isPolling]);
  
  // Status polling has been removed to reduce backend load

  const fetchAssistants = useCallback(async () => {
    if (!accessToken) return;
    try {
      const response = await fetch(`${API_URL}/assistants`, {
        headers: { Authorization: `Bearer ${accessToken}` },
      });
      if (!response.ok) throw new Error('Gagal mengambil daftar asisten.');
      const data: Assistant[] = await response.json();
      setAssistants(data);
    } catch (err: any) {
      toast.error(`Gagal memuat asisten: ${err.message}`);
    }
  }, [accessToken, API_URL]);

  // LocalStorage toast tracking has been removed to simplify the code



  // This effect handles the initial data loading and cleanup
  useEffect(() => {
    let isMounted = true;

    // Only fetch if component is still mounted
    const safelyFetchData = async () => {
      if (isMounted) {
        await fetchInstanceDetails();
        await fetchAssistants();
      }
    };

    safelyFetchData();

    // CRITICAL: This cleanup function runs when the component unmounts or dependencies change
    return () => {
      isMounted = false;

      // Force clear ALL intervals to be absolutely certain
      stopAllPolling();
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [accessToken, whatsappId, stopAllPolling]); // Re-run if accessToken or whatsappId changes
  
  // Separate effect for visibility change listener
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && fetchInstanceDetailsRef.current) {
        fetchInstanceDetailsRef.current();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  const handleSimpleAction = async (action: 'initialize' | 'disconnect' | 'logout') => {
    const actionMessages = {
      initialize: { loading: 'Memunculkan QR Code...', success: 'QR Code berhasil dimunculkan.', error: 'Gagal memunculkan QR Code.' },      
      disconnect: { loading: 'Memutuskan koneksi WhatsApp...', success: 'WhatsApp berhasil diputuskan.', error: 'Gagal memutuskan koneksi WhatsApp.' },
      logout: { loading: 'Disconnect WhatsApp...', success: 'WhatsApp berhasil disconnect.', error: 'Gagal disconnect WhatsApp.' },
    };

    // Stop any existing polling before starting a new action
    stopAllPolling();
    
    // Set loading state for this specific action
    setActionLoading(action);
    toast.loading(actionMessages[action].loading, { id: `action-${action}` });
    try {
      const response = await fetch(`${API_URL}/whatsapp/${whatsappId}/${action}`, {
        method: 'POST',
        headers: { Authorization: `Bearer ${accessToken}` },
      });
      const resData = await response.json();
      if (!response.ok) throw new Error(resData.message || actionMessages[action].error);
      toast.success(resData.message || actionMessages[action].success, { id: `action-${action}` });

      fetchInstanceDetails(); // Refresh instance details
    } catch (err: any) {
      toast.error(err.message || actionMessages[action].error, { id: `action-${action}` });
      if (action === 'initialize') {
        fetchInstanceDetails(); // Still refresh details to get latest status, which might stop polling
      }
    } finally {
      setActionLoading(null);
    }
  };

  // TODO: Uncomment when backend adds 'enabled' field to WhatsApp instance response
  // const handleToggleEnable = async (enabled: boolean) => {
  //   if (!accessToken || !whatsappId || !instance) return;
  //
  //   // Use correct endpoint according to API documentation
  //   const endpoint = enabled ? 'enable' : 'disable';
  //   const actionText = enabled ? 'mengaktifkan' : 'menonaktifkan';
  //
  //   toast.loading(`Sedang ${actionText} WhatsApp...`, { id: `toggle-enable` });
  //   try {
  //     const response = await fetch(`${API_URL}/whatsapp/${whatsappId}/${endpoint}`, {
  //       method: 'POST',
  //       headers: { 'Content-Type': 'application/json', Authorization: `Bearer ${accessToken}` },
  //       body: JSON.stringify({ whatsappId }),
  //     });
  //     const resData = await response.json();
  //     if (!response.ok) throw new Error(resData.message || `Gagal ${actionText} WhatsApp.`);
  //
  //     toast.success(resData.message || `WhatsApp berhasil ${enabled ? 'diaktifkan' : 'dinonaktifkan'}.`, { id: `toggle-enable` });
  //     setInstance(prev => prev ? { ...prev, enabled } : null);
  //     setInstanceEnabled(enabled);
  //   } catch (err: any) {
  //     toast.error(err.message || `Gagal ${actionText} WhatsApp.`, { id: `toggle-enable` });
  //   } finally {
  //     setActionLoading(null);
  //   }
  // };

  const handleUpdateInstance = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    // Validate inputs
    if (!editName.trim()) {
      toast.error('Nama WhatsApp tidak boleh kosong', { id: 'update-instance' });
      return;
    }
    
    if (!accessToken || !whatsappId) {
      toast.error('Tidak dapat memproses permintaan. Coba muat ulang halaman.', { id: 'update-instance' });
      return;
    }
    
    setActionLoading('update');
    toast.loading('Menyimpan perubahan...', { id: 'update-instance' });
    
    try {
      const response = await fetch(`${API_URL}/whatsapp/${whatsappId}`, {
        method: 'PUT',
        headers: { 
          'Content-Type': 'application/json', 
          'Authorization': `Bearer ${accessToken}` 
        },
        // Only send name as per backend developer's information
        body: JSON.stringify({ 
          name: editName.trim()
        }),
      });
      
      const resData = await response.json();
      
      if (!response.ok) {
        throw new Error(resData.message || 'Gagal memperbarui pengaturan WhatsApp.');
      }
      
      toast.success('Nama WhatsApp berhasil diperbarui', { 
        id: 'update-instance',
        duration: 3000
      });
      
      // Refresh to get the latest data
      await fetchInstanceDetails();
      
    } catch (err: any) {
      console.error('Error updating instance:', err);
      toast.error(err.message || 'Terjadi kesalahan saat menyimpan perubahan', { 
        id: 'update-instance',
        duration: 5000
      });
    } finally {
      setActionLoading(null);
    }
  };

  const handleAssignAssistant = async () => {
    if (!accessToken || !whatsappId) {
      toast.error('Tidak dapat memproses permintaan. Coba muat ulang halaman.');
      return;
    }
    
    if (!selectedAssistantId) {
      toast.error('Silakan pilih asisten terlebih dahulu');
      return;
    }
    
    // Show different message for "NONE" selection
    const isRemovingAssistant = selectedAssistantId === 'NONE';
    
    // Set loading state first
    setActionLoading('assign');
    
    // Show loading toast with a simple message
    toast.loading('Menyimpan perubahan...', { 
      id: 'assign-assistant'
    });
    
    try {
      // If removing assistant, send null as assistantId
      const assistantIdToSend = isRemovingAssistant ? null : selectedAssistantId;
      
      const response = await fetch(`${API_URL}/whatsapp/${whatsappId}/assistant`, {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json', 
          'Authorization': `Bearer ${accessToken}` 
        },
        body: JSON.stringify({ 
          whatsappId, 
          assistantId: assistantIdToSend 
        }),
      });
      
      const resData = await response.json();
      
      if (!response.ok) {
        throw new Error(resData.message || 'Gagal memperbarui asisten.');
      }
      
      // Use a simple success message
      const successMessage = isRemovingAssistant 
        ? 'Asisten berhasil dihapus' 
        : 'Asisten berhasil ditetapkan';
      
      // Dismiss loading toast and show success message using the same ID
      toast.success(successMessage, { id: 'assign-assistant' });
      
      // Refresh instance details to show updated assistant
      await fetchInstanceDetails();
      
    } catch (err: any) {
      console.error('Error assigning assistant:', err);
      // Use the same toast ID to replace the loading toast with the error
      toast.error('Gagal menyimpan perubahan. Silakan coba lagi.', { id: 'assign-assistant' });
    } finally {
      setActionLoading(null);
    }
  };

  const handleDeleteInstance = async () => {
    if (!accessToken || !whatsappId || !instance) {
      toast.error('Tidak dapat memproses permintaan. Coba muat ulang halaman.');
      return;
    }
    
    // Show confirmation dialog
    const confirmed = window.confirm(
      `Apakah Anda yakin ingin menghapus WhatsApp ${instance.name} (${instance.phoneNumber})?\n\n` +
      'Tindakan ini akan menghapus semua data terkait dan tidak dapat dibatalkan.'
    );
    
    if (!confirmed) return;
    
    setActionLoading('delete');
    toast.loading('Menghapus WhatsApp...', { 
      id: 'delete-instance',
      duration: 0 // Prevent auto-dismissal
    });
    
    try {
      const response = await fetch(`${API_URL}/whatsapp/${whatsappId}`, {
        method: 'DELETE',
        headers: { 
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
      });
      
      const resData = await response.json();
      
      if (!response.ok) {
        throw new Error(resData.message || 'Gagal menghapus WhatsApp.');
      }
      
      toast.success('WhatsApp berhasil dihapus', { 
        id: 'delete-instance',
        duration: 3000
      });
      
      // Navigate back to WhatsApp list after a short delay
      setTimeout(() => {
        router.push('/dashboard/whatsapp');
      }, 1500);
      
    } catch (err: any) {
      console.error('Error deleting instance:', err);
      toast.error(err.message || 'Terjadi kesalahan saat menghapus WhatsApp', { 
        id: 'delete-instance',
        duration: 5000
      });
    } finally {
      setActionLoading(null);
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    const normalizedStatus = status?.toLowerCase();
    switch (normalizedStatus) {
      case 'connected': return 'success';
      case 'disconnected': return 'outline';
      case 'initializing': case 'qr_pending': return 'warning';
      case 'error': return 'destructive';
      default: return 'secondary';
    }
  };

  // Helper function to check if an assistant is assigned to this WhatsApp instance
  const hasAssistantAssigned = () => {
    if (!instance) return false;
    
    // Check for direct assistantId property
    if (instance.assistantId && instance.assistantId !== null) {
      return true;
    }
    
    // Check for nested assistant object with id
    if (instance.assistant && instance.assistant.id) {
      return true;
    }
    
    return false;
  };

  if (isLoading && !instance) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <div className="flex justify-center items-center h-screen">
            <Loader2 className="h-12 w-12 animate-spin text-primary" />
            <p className="ml-3 text-lg text-muted-foreground">Loading...</p>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  if (error && !instance) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
             <SidebarTrigger className="-ml-1" /> <Separator orientation="vertical" className="h-6"/>
             <Link href="/dashboard/whatsapp"><Button variant="outline" size="sm"><ArrowLeft className="mr-2 h-4 w-4"/>Kembali</Button></Link>
          </header>
          <main className="p-6">
            <Card className="border-destructive bg-destructive/10">
              <CardHeader className="flex flex-row items-center space-x-3 pb-2">
                <AlertTriangle className="h-8 w-8 text-destructive" />
                <CardTitle className="text-destructive text-xl">Gagal Memuat Instansi</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-destructive/90">Terjadi kesalahan: {error}</p>
                <Button variant="outline" size="sm" onClick={fetchInstanceDetails} className="mt-4">
                  <RefreshCw className="mr-2 h-4 w-4"/> Coba Lagi
                </Button>
              </CardContent>
            </Card>
          </main>
        </SidebarInset>
      </SidebarProvider>
    );
  }
  
  if (!instance) {
    // Fallback if instance is null after loading and no error (should ideally not happen if API is consistent)
    return (
        <SidebarProvider><AppSidebar /><SidebarInset>
            <div className="p-6">Instansi tidak ditemukan. <Link href="/dashboard/whatsapp" className="text-blue-500 hover:underline">Kembali ke daftar</Link>.</div>
        </SidebarInset></SidebarProvider>
    );
  }

  return (
    <SidebarProvider data-whatsapp-id={whatsappId}>
      <AppSidebar />
      <Toaster />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block"><BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink></BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem className="hidden md:block"><BreadcrumbLink href="/dashboard/whatsapp">Pengaturan WhatsApp</BreadcrumbLink></BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem><BreadcrumbPage>{instance.name || 'Kelola Instansi'}</BreadcrumbPage></BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>

        <main className="flex-1 p-4 md:p-6 space-y-8">
          <div className="mb-6">
            <Link href="/dashboard/whatsapp" passHref>
              <Button variant="outline" size="sm"><ArrowLeft className="mr-2 h-4 w-4" /> Kembali ke Daftar WhatsApp</Button>
            </Link>
          </div>

          {/* Warning Banner when no assistant is assigned */}
          {!hasAssistantAssigned() && (
            <div className="bg-yellow-100 dark:bg-yellow-900/30 border-l-4 border-yellow-500 dark:border-yellow-600 rounded-md p-4 mb-6 flex items-start shadow-sm">
              <AlertTriangle className="h-6 w-6 text-yellow-600 dark:text-yellow-400 mr-3 flex-shrink-0" />
              <div>
                <h3 className="font-medium text-yellow-800 dark:text-yellow-300 text-base">WhatsApp belum memiliki asisten AI</h3>
                <p className="text-yellow-700 dark:text-yellow-400 mt-1">
                  WhatsApp ini tidak akan dapat menjawab pesan secara otomatis tanpa asisten AI. Silakan tetapkan asisten untuk mengaktifkan fitur ini.
                </p>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="mt-3 bg-white dark:bg-yellow-900/40 border-yellow-400 dark:border-yellow-600 text-yellow-700 dark:text-yellow-300 hover:bg-yellow-50 dark:hover:bg-yellow-900/60"
                  onClick={() => {
                    // Scroll to assistant selection
                    document.getElementById('assign-assistant')?.scrollIntoView({ behavior: 'smooth', block: 'center' });
                  }}
                >
                  <Bot className="mr-2 h-4 w-4" /> Tetapkan Asisten Sekarang
                </Button>
              </div>
            </div>
          )}

          {/* Instance Overview & Status Card */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-2xl">{instance.name}</CardTitle>
                  <CardDescription>{instance.phoneNumber}</CardDescription>
                </div>
                <Button variant="outline" size="icon" onClick={fetchInstanceDetails} title="Refresh Data Instansi">
                    <RefreshCw className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Label>Status:</Label>
                <Badge variant={getStatusBadgeVariant(instance.status) as any}>{instance.status}</Badge>
              </div>
              {/* TODO: Uncomment when backend adds 'enabled' field to WhatsApp instance response */}
              {/* <div className="flex items-center space-x-2">
                <Label>Enabled:</Label>
                <Switch
                  checked={instanceEnabled}
                  onCheckedChange={handleToggleEnable}
                  id={`enable-switch-${instance.id}`}
                />
                <Label htmlFor={`enable-switch-${instance.id}`} className="text-sm text-muted-foreground">
                  {instanceEnabled ? 'Menerima pesan' : 'Tidak menerima pesan'}
                </Label>
              </div> */}
              <p className="text-sm text-muted-foreground">Terakhir Terhubung: {instance.lastConnected ? new Date(instance.lastConnected).toLocaleString() : '-'}</p>
              <p className="text-sm text-muted-foreground">Asisten Terhubung: {instance.assistant?.name || assistants.find(a => a.id === instance.assistantId)?.name || 'Tidak ada'}</p>
            </CardContent>
          </Card>

          {/* Assign Assistant Card - Moved up for better visibility */}
          <Card className="border-2 border-primary bg-primary/5">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Bot className="mr-2 h-5 w-5 text-primary" /> 
                Tetapkan Asisten AI
                <Badge variant="outline" className="ml-2 bg-primary/20">Penting</Badge>
              </CardTitle>
              <CardDescription>
                WhatsApp Anda membutuhkan asisten AI untuk dapat menjawab pesan secara otomatis.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="assign-assistant">Pilih Asisten</Label>
                <Select value={selectedAssistantId} onValueChange={setSelectedAssistantId}>
                  <SelectTrigger id="assign-assistant">
                    <SelectValue placeholder="Pilih asisten untuk WhatsApp ini" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="NONE">Tidak Ada (Hapus Asisten)</SelectItem>
                    {assistants.map(assistant => (
                      <SelectItem key={assistant.id} value={assistant.id}>{assistant.name}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
            <CardFooter>
              <Button 
                onClick={handleAssignAssistant} 
                disabled={!selectedAssistantId || selectedAssistantId === instance.assistantId || actionLoading === 'assign'}
                className="min-w-[200px]"
              >
                {actionLoading === 'assign' ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Memproses...
                  </>
                ) : (
                  <>
                    <Bot className="mr-2 h-4 w-4" /> 
                    {selectedAssistantId === 'NONE' ? 'Hapus Asisten' : 'Simpan Penetapan Asisten'}
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>

          {/* QR Code Section */}
          <Card>
            <CardHeader>
              <CardTitle>Scan QR Code</CardTitle>
              <CardDescription>
                Scan QR code ini menggunakan WhatsApp di ponsel Anda untuk menghubungkan.
                <br />
                <span className="text-sm text-muted-foreground mt-1 block">
                  💡 <strong>Tips:</strong> Jika QR code susah di-scan, coba ubah ke light mode untuk kontras yang lebih baik.
                </span>
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center space-y-4">
              {(['connecting', 'initializing', 'qr_pending'].includes(instance?.status?.toLowerCase() || '')) ? (
                <>
                  {isQrLoading && <Loader2 className="h-8 w-8 animate-spin mx-auto" />}
                  {!isQrLoading && qrCodeUrl && (
                    <img src={qrCodeUrl} alt="WhatsApp QR Code" className="mx-auto border rounded-md" style={{ display: 'block', width: '200px', height: '200px', backgroundColor: 'white' }} />
                  )}
                  {!isQrLoading && !qrCodeUrl && (
                    <p className="text-muted-foreground">Menunggu QR Code atau gagal memuat. Polling aktif jika status sesuai.</p>
                  )}
                  {/* Display JSON QR if available, for debugging or other purposes */}
                  {qrCodeJson && (
                    <div className="p-2 bg-muted rounded-md text-xs overflow-auto max-h-32 mt-2">
                        <pre>{qrCodeJson}</pre>
                    </div>
                  )}
                </>
              ) : instance?.status?.toLowerCase() === 'connected' ? (
                <p className="text-green-600 font-semibold">Nomor Terhubung</p>
              ) : instance?.status?.toLowerCase() === 'error' ? (
                <div>
                  <p className="text-destructive font-semibold mb-2">Terjadi Error</p>
                  <p className="text-muted-foreground mb-4">QR Code tidak tersedia. Silakan klik tombol "Munculkan QR Code" untuk mencoba lagi.</p>
                  <Button 
                    onClick={() => handleSimpleAction('initialize')} 
                    variant="outline"
                    disabled={actionLoading === 'initialize'}
                  >
                    {actionLoading === 'initialize' ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Memproses...
                      </>
                    ) : (
                      <>
                        <QrCode className="mr-2 h-4 w-4" /> Munculkan QR Code Baru
                      </>
                    )}
                  </Button>
                </div>
              ) : (
                <p className="text-muted-foreground">QR Code tidak tersedia untuk status whatsapp saat ini: {instance?.status || 'Tidak diketahui'}.</p>
              )}
               <div className="flex justify-center gap-2 pt-2">
                  <Button 
                    onClick={() => { setIsQrLoading(true); fetchQrCode('image').finally(() => setIsQrLoading(false)); }} 
                    variant="outline" 
                    size="sm" 
                    disabled={isQrLoading || !(instance?.status === 'connecting' || instance?.status === 'INITIALIZING' || instance?.status === 'QR_PENDING')}
                  >
                    <QrCode className="mr-2 h-4 w-4"/>Muat Gambar
                  </Button>
                  <Button 
                    onClick={() => { setIsQrLoading(true); fetchQrCode('json').finally(() => setIsQrLoading(false)); }} 
                    variant="outline" 
                    size="sm" 
                    disabled={isQrLoading || !(instance?.status === 'connecting' || instance?.status === 'INITIALIZING' || instance?.status === 'QR_PENDING')}
                  >
                    <QrCode className="mr-2 h-4 w-4"/>Muat JSON
                  </Button>
                  {qrCodeHtmlUrl && 
                      <a href={qrCodeHtmlUrl} target="_blank" rel="noopener noreferrer">
                          <Button 
                            variant="outline" 
                            size="sm"
                            disabled={!(instance?.status === 'connecting' || instance?.status === 'INITIALIZING' || instance?.status === 'QR_PENDING')}
                          >
                            <ExternalLink className="mr-2 h-4 w-4"/>Buka HTML QR
                          </Button>
                      </a>
                  }
               </div>
            </CardContent>
          </Card>

          {/* Actions Card */}
          <Card>
            <CardHeader><CardTitle>Pengaturan Konektivitas</CardTitle></CardHeader>
            <CardContent className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              <Button 
                onClick={() => handleSimpleAction('initialize')} 
                disabled={instance.status === 'connected' || instance.status === 'initializing' || instance.status === 'qr_pending' || actionLoading === 'initialize'}
              >
                {actionLoading === 'initialize' ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Memproses...
                  </>
                ) : (
                  <>
                    <QrCode className="mr-2 h-4 w-4" /> Munculkan QR Code
                  </>
                )}
              </Button>
              {/* Hide disconnect button as requested */}
              {/* <Button
                onClick={() => handleSimpleAction('disconnect')}
                variant="outline"
                disabled={instance.status === 'disconnected' || instance.status === 'initializing' || actionLoading === 'disconnect'}
              >
                {actionLoading === 'disconnect' ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Memproses...
                  </>
                ) : (
                  <>
                    <StopCircle className="mr-2 h-4 w-4" /> Putuskan
                  </>
                )}
              </Button> */}
              <Button
                onClick={() => handleSimpleAction('logout')}
                variant="outline"
                disabled={instance.status === 'disconnected' || actionLoading === 'logout'}
              >
                {actionLoading === 'logout' ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Memproses...
                  </>
                ) : (
                  <>
                    <LogOut className="mr-2 h-4 w-4" /> Disconnect
                  </>
                )}
              </Button>
            </CardContent>
          </Card>

          {/* Assign Assistant Card - Already moved up for better visibility */}

          {/* Edit Instance Card */}
          <Card>
            <CardHeader><CardTitle>Pengaturan WhatsApp</CardTitle></CardHeader>
            <form onSubmit={handleUpdateInstance}>
              <CardContent className="space-y-4 pb-8">
                <div className="space-y-2">
                  <Label htmlFor="instance-name-edit">Nama WhatsApp</Label>
                  <Input id="instance-name-edit" value={editName} onChange={(e) => setEditName(e.target.value)} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone-number-edit">Nomor Telepon</Label>
                  <Input 
                    id="phone-number-edit" 
                    value={editPhoneNumber} 
                    disabled={true} 
                    className="bg-muted cursor-not-allowed"
                  />
                  <p className="text-xs text-muted-foreground">Nomor telepon tidak dapat diubah setelah WhatsApp dibuat.</p>
                </div>
              </CardContent>
              <CardFooter className="border-t pt-4">
                <Button 
                  type="submit" 
                  disabled={actionLoading === 'update'}
                  className="min-w-[150px] mt-2"
                >
                  {actionLoading === 'update' ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Menyimpan...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" /> Simpan Perubahan
                    </>
                  )}
                </Button>
              </CardFooter>
            </form>
          </Card>

          {/* Delete Instance Card */}
          <Card className="border-destructive">
            <CardHeader><CardTitle className="text-destructive">Hapus WhatsApp</CardTitle></CardHeader>
            <CardContent>
              <CardDescription className="text-destructive/80">
                Menghapus WhatsApp ini akan menghapus semua data terkait dan tidak dapat diurungkan.
              </CardDescription>
            </CardContent>
            <CardFooter>
              <Button variant="destructive" onClick={handleDeleteInstance}>
                {actionLoading === 'delete' ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Menghapus...
                  </>
                ) : (
                  <>
                    <Trash2 className="mr-2 h-4 w-4" /> Hapus WhatsApp Ini
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>

        </main>
      </SidebarInset>
    </SidebarProvider>
  );
}
