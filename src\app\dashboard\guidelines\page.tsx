"use client";

import { AppSidebar } from "@/components/app-sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  BookOpen,
  Bot,
  MessageCircle,
  Globe,
  FileText,
  Zap,
  Users,
  Settings,
  ArrowRight,
  CheckCircle,
  AlertCircle,
  Lightbulb,
  Target,
  Sparkles,
  HelpCircle
} from "lucide-react";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import Link from 'next/link';

export default function GuidelinesPage() {
  return (
    <ProtectedRoute>
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <header className="flex h-16 shrink-0 items-center gap-2 border-b">
            <div className="flex items-center gap-2 px-4">
              <SidebarTrigger className="-ml-1" />
              <Separator
                orientation="vertical"
                className="mr-2 data-[orientation=vertical]:h-4"
              />
              <Breadcrumb>
                <BreadcrumbList>
                  <BreadcrumbItem className="hidden md:block">
                    <BreadcrumbLink href="/dashboard">
                      Dashboard
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator className="hidden md:block" />
                  <BreadcrumbItem>
                    <BreadcrumbPage>Guidelines</BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
            </div>
          </header>

          <main className="flex-1 p-4 md:p-6 space-y-6">
            <div className="flex justify-between items-center mb-6">
              <div>
                <h2 className="text-2xl font-bold tracking-tight">Panduan Penggunaan Heylo</h2>
                <p className="text-muted-foreground">
                  Petunjuk lengkap untuk memaksimalkan platform AI assistant Anda
                </p>
              </div>
            </div>

            {/* Video Tutorial Section */}
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BookOpen className="h-5 w-5" />
                  Video Tutorial
                </CardTitle>
                <CardDescription>
                  Tonton video tutorial lengkap untuk memahami cara menggunakan Heylo
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="aspect-video w-full rounded-lg overflow-hidden bg-muted">
                  <iframe
                    width="100%"
                    height="100%"
                    src="https://www.youtube.com/embed/9NRA6JfmBho"
                    title="Tutorial Heylo - Cara Menggunakan Platform AI Assistant"
                    style={{ border: 0 }}
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                    allowFullScreen
                    className="w-full h-full"
                  ></iframe>
                </div>
                <div className="mt-4 flex flex-col sm:flex-row gap-2">
                  <Button size="sm" variant="outline" asChild>
                    <a href="https://www.youtube.com/watch?v=9NRA6JfmBho" target="_blank" rel="noopener noreferrer">
                      <BookOpen className="h-3 w-3 mr-1" />
                      Buka di YouTube
                    </a>
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Quick Navigation */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Navigasi Cepat
                </CardTitle>
                <CardDescription>
                  Pilih topik yang ingin Anda pelajari
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <Button variant="outline" className="h-auto p-4 justify-start" asChild>
                    <a href="#getting-started">
                      <div className="flex items-center gap-3">
                        <Sparkles className="h-5 w-5 text-primary" />
                        <div className="text-left">
                          <div className="font-medium">Memulai</div>
                          <div className="text-xs text-muted-foreground">Setup awal platform</div>
                        </div>
                      </div>
                    </a>
                  </Button>
                  
                  <Button variant="outline" className="h-auto p-4 justify-start" asChild>
                    <a href="#assistants">
                      <div className="flex items-center gap-3">
                        <Bot className="h-5 w-5 text-primary" />
                        <div className="text-left">
                          <div className="font-medium">Asisten AI</div>
                          <div className="text-xs text-muted-foreground">Membuat & mengelola asisten</div>
                        </div>
                      </div>
                    </a>
                  </Button>
                  
                  <Button variant="outline" className="h-auto p-4 justify-start" asChild>
                    <a href="#channels">
                      <div className="flex items-center gap-3">
                        <MessageCircle className="h-5 w-5 text-primary" />
                        <div className="text-left">
                          <div className="font-medium">Channel</div>
                          <div className="text-xs text-muted-foreground">WhatsApp & Web Widget</div>
                        </div>
                      </div>
                    </a>
                  </Button>
                  
                  <Button variant="outline" className="h-auto p-4 justify-start" asChild>
                    <a href="#knowledge">
                      <div className="flex items-center gap-3">
                        <FileText className="h-5 w-5 text-primary" />
                        <div className="text-left">
                          <div className="font-medium">Knowledge Base</div>
                          <div className="text-xs text-muted-foreground">Upload & kelola dokumen</div>
                        </div>
                      </div>
                    </a>
                  </Button>
                  
                  <Button variant="outline" className="h-auto p-4 justify-start" asChild>
                    <a href="#best-practices">
                      <div className="flex items-center gap-3">
                        <Lightbulb className="h-5 w-5 text-primary" />
                        <div className="text-left">
                          <div className="font-medium">Best Practices</div>
                          <div className="text-xs text-muted-foreground">Tips & trik optimasi</div>
                        </div>
                      </div>
                    </a>
                  </Button>
                  
                  <Button variant="outline" className="h-auto p-4 justify-start" asChild>
                    <a href="#troubleshooting">
                      <div className="flex items-center gap-3">
                        <Settings className="h-5 w-5 text-primary" />
                        <div className="text-left">
                          <div className="font-medium">Troubleshooting</div>
                          <div className="text-xs text-muted-foreground">Solusi masalah umum</div>
                        </div>
                      </div>
                    </a>
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Getting Started Section */}
            <Card id="getting-started">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Sparkles className="h-5 w-5" />
                  1. Memulai dengan Heylo
                </CardTitle>
                <CardDescription>
                  Langkah-langkah awal untuk setup platform Anda
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <div className="h-6 w-6 rounded-full bg-primary/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-xs font-semibold text-primary">1</span>
                    </div>
                    <div>
                      <h4 className="font-medium mb-1">Buat Akun & Login</h4>
                      <p className="text-sm text-muted-foreground mb-2">
                        Daftar akun baru atau login dengan akun yang sudah ada. Pastikan email Anda terverifikasi.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-3">
                    <div className="h-6 w-6 rounded-full bg-primary/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-xs font-semibold text-primary">2</span>
                    </div>
                    <div>
                      <h4 className="font-medium mb-1">Pilih Paket Langganan</h4>
                      <p className="text-sm text-muted-foreground mb-2">
                        Mulai dengan free trial 7 hari atau pilih paket yang sesuai kebutuhan Anda.
                      </p>
                      <Button size="sm" variant="outline" asChild>
                        <Link href="/dashboard/plans">
                          Lihat Paket
                          <ArrowRight className="h-3 w-3 ml-1" />
                        </Link>
                      </Button>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-3">
                    <div className="h-6 w-6 rounded-full bg-primary/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-xs font-semibold text-primary">3</span>
                    </div>
                    <div>
                      <h4 className="font-medium mb-1">Eksplorasi Dashboard</h4>
                      <p className="text-sm text-muted-foreground mb-2">
                        Familiarisasi dengan interface dashboard dan menu-menu yang tersedia.
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Assistants Section */}
            <Card id="assistants">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bot className="h-5 w-5" />
                  2. Mengelola Asisten AI
                </CardTitle>
                <CardDescription>
                  Cara membuat, mengkonfigurasi, dan mengoptimalkan asisten AI Anda
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h4 className="font-medium flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      Membuat Asisten Baru
                    </h4>
                    <div className="space-y-3 text-sm text-muted-foreground">
                      <p>• Klik "Buat Asisten Baru" di dashboard</p>
                      <p>• Berikan nama yang jelas dan deskriptif</p>
                      <p>• Tulis instruksi yang spesifik dan detail</p>
                      <p>• Upload dokumen ke Knowledge Base jika perlu</p>                    
                      <p>• Test asisten di Playground sebelum deploy</p>
                    </div>
                    <Button size="sm" variant="outline" asChild>
                      <Link href="/dashboard/assistants/new">
                        Buat Asisten
                        <ArrowRight className="h-3 w-3 ml-1" />
                      </Link>
                    </Button>
                  </div>
                  
                  <div className="space-y-4">
                    <h4 className="font-medium flex items-center gap-2">
                      <Lightbulb className="h-4 w-4 text-yellow-500" />
                      Tips Instruksi Efektif
                    </h4>
                    <div className="space-y-3 text-sm text-muted-foreground">
                      <p>• Gunakan bahasa yang jelas dan spesifik</p>
                      <p>• Berikan contoh percakapan yang diinginkan</p>
                      <p>• Tentukan tone dan personality asisten</p>
                      <p>• Sebutkan batasan dan hal yang tidak boleh dilakukan</p>
                      <p>• Update instruksi berdasarkan feedback pengguna</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Channels Section */}
            <Card id="channels">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageCircle className="h-5 w-5" />
                  3. Mengelola Channel Komunikasi
                </CardTitle>
                <CardDescription>
                  Setup dan konfigurasi WhatsApp dan Web Widget untuk asisten Anda
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* WhatsApp Section */}
                  <div className="space-y-4">
                    <h4 className="font-medium flex items-center gap-2">
                      <MessageCircle className="h-4 w-4 text-green-500" />
                      WhatsApp Integration
                    </h4>
                    <div className="space-y-3">
                      <div className="p-3 rounded-lg bg-muted/50">
                        <h5 className="font-medium text-sm mb-2">Setup WhatsApp:</h5>
                        <div className="space-y-2 text-sm text-muted-foreground">
                          <p>• Klik "Hubungkan WhatsApp" di dashboard</p>
                          <p>• Scan QR code dengan WhatsApp Web</p>
                          <p>• Pilih asisten yang akan digunakan</p>
                          <p>• Test koneksi dengan mengirim pesan</p>
                        </div>
                      </div>
                      <div className="p-3 rounded-lg bg-yellow-50 border border-yellow-200">
                        <div className="flex items-start gap-2">
                          <AlertCircle className="h-4 w-4 text-yellow-600 mt-0.5" />
                          <div>
                            <p className="text-sm font-medium text-yellow-800">Penting:</p>
                            <p className="text-sm text-yellow-700">Jangan logout dari WhatsApp Web di perangkat lain untuk menjaga koneksi tetap aktif.</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <Button size="sm" variant="outline" asChild>
                      <Link href="/dashboard/whatsapp">
                        Kelola WhatsApp
                        <ArrowRight className="h-3 w-3 ml-1" />
                      </Link>
                    </Button>
                  </div>

                  {/* Web Widget Section */}
                  <div className="space-y-4">
                    <h4 className="font-medium flex items-center gap-2">
                      <Globe className="h-4 w-4 text-blue-500" />
                      Web Widget Integration
                    </h4>
                    <div className="space-y-3">
                      <div className="p-3 rounded-lg bg-muted/50">
                        <h5 className="font-medium text-sm mb-2">Setup Web Widget:</h5>
                        <div className="space-y-2 text-sm text-muted-foreground">
                          <p>• Buat widget baru di menu Webchat</p>
                          <p>• Kustomisasi warna dan tampilan</p>
                          <p>• Copy kode snippet yang disediakan</p>
                          <p>• Paste di website Anda sebelum tag &lt;/body&gt;</p>
                        </div>
                      </div>
                      <div className="p-3 rounded-lg bg-blue-50 border border-blue-200">
                        <div className="flex items-start gap-2">
                          <Lightbulb className="h-4 w-4 text-blue-600 mt-0.5" />
                          <div>
                            <p className="text-sm font-medium text-blue-800">Tips:</p>
                            <p className="text-sm text-blue-700">Test widget di staging environment sebelum deploy ke production.</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <Button size="sm" variant="outline" asChild>
                      <Link href="/dashboard/webchat">
                        Kelola Webchat
                        <ArrowRight className="h-3 w-3 ml-1" />
                      </Link>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Knowledge Base Section */}
            <Card id="knowledge">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  4. Knowledge Base Management
                </CardTitle>
                <CardDescription>
                  Upload dan kelola dokumen untuk meningkatkan kemampuan asisten AI
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-4 rounded-lg border">
                      <h4 className="font-medium mb-2 flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        Format File Didukung
                      </h4>
                      <div className="space-y-1 text-sm text-muted-foreground">
                        <p>• PDF (.pdf)</p>
                        <p>• Microsoft Word (.docx)</p>
                        <p>• Text Files (.txt)</p>
                        <p>• CSV (.csv)</p>
                        <p>• Markdown (.md)</p>
                      </div>
                    </div>

                    <div className="p-4 rounded-lg border">
                      <h4 className="font-medium mb-2 flex items-center gap-2">
                        <Target className="h-4 w-4 text-blue-500" />
                        Best Practices
                      </h4>
                      <div className="space-y-1 text-sm text-muted-foreground">
                        <p>• Gunakan nama file yang deskriptif</p>
                        <p>• Organisir dokumen berdasarkan topik</p>
                        <p>• Update dokumen secara berkala</p>
                        <p>• Hapus dokumen yang sudah tidak relevan</p>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 rounded-lg bg-primary/5 border border-primary/20">
                    <h4 className="font-medium mb-2 text-primary">Cara Upload Dokumen:</h4>
                    <div className="space-y-2 text-sm text-muted-foreground">
                      <p>1. Buka menu Knowledge Base</p>
                      <p>2. Klik tombol "Upload Dokumen"</p>
                      <p>3. Pilih file dari komputer Anda</p>
                      <p>4. Tunggu proses upload dan indexing selesai</p>
                      <p>5. Dokumen siap digunakan oleh asisten AI</p>
                    </div>
                  </div>
                </div>

                <Button size="sm" variant="outline" asChild>
                  <Link href="/dashboard/knowledge">
                    Kelola Knowledge Base
                    <ArrowRight className="h-3 w-3 ml-1" />
                  </Link>
                </Button>
              </CardContent>
            </Card>

            {/* Best Practices Section */}
            <Card id="best-practices">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Lightbulb className="h-5 w-5" />
                  5. Best Practices & Tips Optimasi
                </CardTitle>
                <CardDescription>
                  Panduan untuk memaksimalkan performa dan efektivitas asisten AI Anda
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h4 className="font-medium flex items-center gap-2">
                      <Bot className="h-4 w-4 text-primary" />
                      Optimasi Asisten AI
                    </h4>
                    <div className="space-y-3">
                      <div className="p-3 rounded-lg bg-green-50 border border-green-200">
                        <h5 className="font-medium text-sm text-green-800 mb-2">✅ Do's:</h5>
                        <div className="space-y-1 text-sm text-green-700">
                          <p>• Berikan instruksi yang spesifik dan jelas</p>
                          <p>• Gunakan contoh percakapan dalam instruksi</p>
                          <p>• Test asisten secara berkala</p>
                          <p>• Monitor feedback pengguna</p>
                          <p>• Update knowledge base secara rutin</p>
                        </div>
                      </div>

                      <div className="p-3 rounded-lg bg-red-50 border border-red-200">
                        <h5 className="font-medium text-sm text-red-800 mb-2">❌ Don'ts:</h5>
                        <div className="space-y-1 text-sm text-red-700">
                          <p>• Jangan gunakan instruksi yang terlalu umum</p>
                          <p>• Hindari informasi yang sensitif di instruksi</p>
                          <p>• Jangan abaikan feedback negatif</p>
                          <p>• Hindari overload knowledge base</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h4 className="font-medium flex items-center gap-2">
                      <Users className="h-4 w-4 text-blue-500" />
                      Pengalaman Pengguna
                    </h4>
                    <div className="space-y-3">
                      <div className="p-3 rounded-lg bg-muted/50">
                        <h5 className="font-medium text-sm mb-2">Response Time:</h5>
                        <div className="space-y-1 text-sm text-muted-foreground">
                          <p>• Target: &lt; 5 - 7 detik untuk respons standar</p>
                          <p>• Monitor latency secara berkala</p>
                          <p>• Optimasi knowledge base jika lambat</p>
                        </div>
                      </div>

                      <div className="p-3 rounded-lg bg-muted/50">
                        <h5 className="font-medium text-sm mb-2">Tone & Personality:</h5>
                        <div className="space-y-1 text-sm text-muted-foreground">
                          <p>• Konsisten dengan brand voice</p>
                          <p>• Ramah dan profesional</p>
                          <p>• Sesuaikan dengan target audience</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="p-4 rounded-lg bg-primary/5 border border-primary/20">
                  <h4 className="font-medium mb-3 text-primary flex items-center gap-2">
                    <Target className="h-4 w-4" />
                    Metrics yang Perlu Dimonitor
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <p className="font-medium mb-1">Response Rate</p>
                      <p className="text-muted-foreground">Persentase pesan yang berhasil dijawab</p>
                    </div>
                    <div>
                      <p className="font-medium mb-1">User Satisfaction</p>
                      <p className="text-muted-foreground">Feedback positif dari pengguna</p>
                    </div>
                    <div>
                      <p className="font-medium mb-1">Resolution Time</p>
                      <p className="text-muted-foreground">Waktu rata-rata menyelesaikan pertanyaan</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Troubleshooting Section */}
            <Card id="troubleshooting">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  6. Troubleshooting & FAQ
                </CardTitle>
                <CardDescription>
                  Solusi untuk masalah umum yang mungkin Anda hadapi
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="p-4 rounded-lg border">
                    <h4 className="font-medium mb-2 flex items-center gap-2">
                      <AlertCircle className="h-4 w-4 text-red-500" />
                      WhatsApp Tidak Terhubung
                    </h4>
                    <div className="space-y-2 text-sm text-muted-foreground">
                      <p><strong>Gejala:</strong> Status "Disconnected" atau pesan tidak terkirim</p>
                      <p><strong>Solusi:</strong></p>
                      <ul className="list-disc list-inside space-y-1 ml-4">
                        <li>Pastikan WhatsApp Web masih aktif di browser</li>
                        <li>Scan ulang QR code jika diperlukan</li>
                        <li>Periksa koneksi internet</li>
                        <li>Restart browser dan coba lagi</li>
                      </ul>
                    </div>
                  </div>

                  <div className="p-4 rounded-lg border">
                    <h4 className="font-medium mb-2 flex items-center gap-2">
                      <AlertCircle className="h-4 w-4 text-yellow-500" />
                      Asisten Memberikan Jawaban yang Tidak Relevan
                    </h4>
                    <div className="space-y-2 text-sm text-muted-foreground">
                      <p><strong>Penyebab:</strong> Instruksi kurang spesifik atau knowledge base tidak optimal</p>
                      <p><strong>Solusi:</strong></p>
                      <ul className="list-disc list-inside space-y-1 ml-4">
                        <li>Review dan perbaiki instruksi asisten</li>
                        <li>Tambahkan contoh percakapan yang diinginkan</li>
                        <li>Update atau tambah dokumen di knowledge base</li>
                        <li>Test di Playground sebelum deploy</li>
                      </ul>
                    </div>
                  </div>

                  <div className="p-4 rounded-lg border">
                    <h4 className="font-medium mb-2 flex items-center gap-2">
                      <AlertCircle className="h-4 w-4 text-blue-500" />
                      Web Widget Tidak Muncul
                    </h4>
                    <div className="space-y-2 text-sm text-muted-foreground">
                      <p><strong>Kemungkinan Penyebab:</strong> Kode snippet tidak terpasang dengan benar</p>
                      <p><strong>Solusi:</strong></p>
                      <ul className="list-disc list-inside space-y-1 ml-4">
                        <li>Pastikan kode snippet dipasang sebelum tag &lt;/body&gt;</li>
                        <li>Periksa console browser untuk error JavaScript</li>
                        <li>Pastikan domain website sudah benar di konfigurasi</li>
                        <li>Clear cache browser dan reload halaman</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <div className="p-4 rounded-lg bg-primary/5 border border-primary/20">
                  <h4 className="font-medium mb-2 text-primary flex items-center gap-2">
                    <HelpCircle className="h-4 w-4" />
                    Butuh Bantuan Lebih Lanjut?
                  </h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    Jika masalah Anda tidak terdaftar di atas atau memerlukan bantuan teknis lebih lanjut,
                    jangan ragu untuk menghubungi tim support kami.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-2">
                    <Button size="sm" variant="outline" asChild>
                      <a href="https://wa.link/il1t5g" target="_blank" rel="noopener noreferrer">
                        <MessageCircle className="h-3 w-3 mr-1" />
                        WhatsApp Support
                      </a>
                    </Button>
                    <Button size="sm" variant="outline" asChild>
                      <a href="https://status.heylo.co.id" target="_blank" rel="noopener noreferrer">
                        <Globe className="h-3 w-3 mr-1" />
                        Status Layanan
                      </a>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </main>
        </SidebarInset>
      </SidebarProvider>
    </ProtectedRoute>
  );
}
