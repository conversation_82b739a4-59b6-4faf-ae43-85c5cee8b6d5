import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
  baseURL: process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1',
});

export async function POST(req: NextRequest) {
  try {
    const { prompt } = await req.json();
    
    if (!prompt || typeof prompt !== 'string') {
      return NextResponse.json(
        { error: 'Prompt is required and must be a string' },
        { status: 400 }
      );
    }
    
    // Skip analysis for very short prompts
    if (prompt.trim().length < 20) {
      return NextResponse.json({
        score: 0,
        feedback: ['Prompt terlalu pendek untuk dianalisis.'],
        enhancedPrompt: null
      });
    }
    
    // Call OpenAI to analyze the prompt
    const response = await openai.chat.completions.create({
      model: 'meta-llama/llama-4-maverick',
      messages: [
        {
          role: 'system',
          content: `Kamu adalah analis prompt yang ahli. Tugasmu adalah menganalisis prompt instruksi untuk asisten AI dan memberikan:
1. Skor kualitas prompt (0-100) yang SANGAT KRITIS dan BERVARIASI
2. Umpan balik spesifik tentang kekuatan dan kelemahan prompt
3. Versi yang ditingkatkan dari prompt asli (enhancedPrompt)

Berikan respons dalam format JSON dengan struktur berikut:
{
  "score": number,
  "feedback": string[],
  "enhancedPrompt": string
}

PENTING UNTUK SCORING:
- Berikan skor yang SANGAT BERVARIASI dan KRITIS
- Prompt pendek dan tidak spesifik harus mendapat skor rendah (20-40)
- Prompt yang cukup baik tapi masih kurang detail: skor menengah (50-70)
- Hanya prompt yang sangat terstruktur, detail, dan lengkap yang layak mendapat skor 80+
- JANGAN memberi skor tinggi (>80) untuk prompt yang biasa saja

Kriteria penilaian prompt yang baik:
- Kejelasan: Apakah tujuan dan instruksi jelas?
- Spesifisitas: Apakah memberikan detail yang cukup?
- Struktur: Apakah terorganisir dengan baik?
- Batasan: Apakah menetapkan batasan yang jelas?
- Contoh: Apakah menyertakan contoh jika relevan?
- Nada: Apakah menentukan nada/gaya yang diinginkan?

Untuk enhancedPrompt, tingkatkan prompt asli dengan mempertahankan maksud aslinya sambil menambahkan struktur, kejelasan, dan spesifisitas. Buat versi yang ditingkatkan ini KOMPREHENSIF dan LENGKAP, dengan panjang minimal 200-300 kata untuk memastikan kualitas tinggi.`
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.42,
      response_format: { type: 'json_object' }
    });
    
    // Parse the response
    const content = response.choices[0].message.content;
    if (!content) {
      throw new Error('Empty response from OpenAI');
    }
    
    let result;
    try {
      result = JSON.parse(content);
    } catch (e) {
      console.error('Failed to parse OpenAI response:', content);
      throw new Error('Invalid response format from AI service');
    }
    
    // Validate the result
    if (!result.score || !Array.isArray(result.feedback) || !result.enhancedPrompt) {
      throw new Error('Invalid response structure from AI service');
    }
    
    // Return the analysis
    return NextResponse.json({
      score: result.score,
      feedback: result.feedback,
      enhancedPrompt: result.enhancedPrompt
    });
    
  } catch (error: any) {
    console.error('Error analyzing prompt:', error);
    
    return NextResponse.json(
      { error: error.message || 'Failed to analyze prompt' },
      { status: 500 }
    );
  }
}
