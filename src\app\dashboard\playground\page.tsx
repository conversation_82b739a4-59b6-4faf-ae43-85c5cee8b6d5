"use client";

import { AppSidebar } from "@/components/app-sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/contexts/AuthContext";
import React, { useEffect, useState, useCallback, useRef } from "react";
import {
  Loader2,
  Send,
  PlusCircle,
  MessageSquare,
  ArrowLeft,
  Sparkles,
  Bot,
  User,
  Clock,
  Zap,
  RefreshCw,
  Trash2,
  ImagePlus,
  X
} from 'lucide-react';
import { toast } from "sonner";
import { Toaster } from "@/components/ui/sonner";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface Assistant {
  id: string;
  name: string;
  description?: string;
  instructions?: string;
}

interface Chat {
  id: string;
  assistantId: string;
  title: string;
  lastMessageAt: string;
  messageCount: number;
  createdAt: string;
  updatedAt: string;
}

interface Message {
  id: string;
  chatId: string;
  role: 'user' | 'assistant';
  content: string;
  createdAt: string;
  // For local image preview before backend response
  localImagePreview?: string;
  hasImage?: boolean;
}

interface ExistingChatResponseData {
  userMessage: Message;
  assistantMessage: Message;
}

interface NewChatResponseData {
  chat: Chat;
  userMessage: Message;
  assistantMessage: Message;
}

export default function PlaygroundPage() {
  const { accessToken, user } = useAuth();
  const [assistants, setAssistants] = useState<Assistant[]>([]);
  const [selectedAssistant, setSelectedAssistant] = useState<Assistant | null>(null);
  const [chats, setChats] = useState<Chat[]>([]);
  const [selectedChat, setSelectedChat] = useState<Chat | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [userInput, setUserInput] = useState("");
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [imageCache, setImageCache] = useState<Map<string, string>>(new Map());
  
  const [isLoadingAssistants, setIsLoadingAssistants] = useState(false);
  const [isLoadingChats, setIsLoadingChats] = useState(false);
  const [isLoadingMessages, setIsLoadingMessages] = useState(false);
  const [isSendingMessage, setIsSendingMessage] = useState(false);
  const [deletingChatId, setDeletingChatId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isTyping, setIsTyping] = useState(false);
  
  // Delete confirmation modal state
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [chatToDelete, setChatToDelete] = useState<string | null>(null);
  
  // UI State - Always show chat list when assistant is selected
  const [showChatList, setShowChatList] = useState(true);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  // Handle image selection
  const handleImageSelect = useCallback((file: File) => {
    if (!file.type.startsWith('image/')) {
      toast.error('File harus berupa gambar');
      return;
    }

    if (file.size > 10 * 1024 * 1024) { // 10MB limit
      toast.error('Ukuran file maksimal 10MB');
      return;
    }

    setSelectedImage(file);

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setImagePreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  }, []);

  // Remove selected image
  const removeSelectedImage = useCallback(() => {
    setSelectedImage(null);
    setImagePreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, []);

  // Fetch image with authorization token
  const fetchImageWithAuth = useCallback(async (imageUrl: string): Promise<string | null> => {
    try {
      const response = await fetch(imageUrl, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch image');
      }

      const blob = await response.blob();
      const objectUrl = URL.createObjectURL(blob);

      // Cache the result
      setImageCache(prev => {
        const newCache = new Map(prev);
        newCache.set(imageUrl, objectUrl);
        return newCache;
      });

      return objectUrl;
    } catch (error) {
      console.error('Error fetching image:', error);
      return null;
    }
  }, [accessToken]);

  // Pre-fetch all images when messages change
  useEffect(() => {
    const preloadImages = async () => {
      const imageUrls = messages
        .filter(msg => msg.content.startsWith('https://api.heylo.co.id/files/') && msg.content.includes('/stream'))
        .map(msg => msg.content)
        .filter(url => !imageCache.has(url)); // Only fetch uncached images

      for (const url of imageUrls) {
        await fetchImageWithAuth(url);
      }
    };

    if (messages.length > 0) {
      preloadImages();
    }
  }, [messages, fetchImageWithAuth]);

  // Component for displaying images with authorization or local preview
  const MessageImage = ({ message, className, style }: {
    message: Message;
    className?: string;
    style?: React.CSSProperties;
  }) => {
    // If message has local image preview, show it immediately
    if (message.localImagePreview) {
      return (
        <img
          src={message.localImagePreview}
          alt="Uploaded image"
          className={className}
          style={style}
        />
      );
    }

    // If content is image URL from backend, fetch with auth
    if (message.content.startsWith('https://api.heylo.co.id/files/') && message.content.includes('/stream')) {
      const cachedUrl = imageCache.get(message.content);

      if (cachedUrl) {
        return (
          <img
            src={cachedUrl}
            alt="Uploaded image"
            className={className}
            style={style}
          />
        );
      }

      // Show loading state if not cached yet
      return (
        <div className={`flex items-center justify-center bg-muted rounded-lg ${className}`} style={style}>
          <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
        </div>
      );
    }

    // No image to display
    return null;
  };

  // Auto-resize textarea
  const adjustTextareaHeight = useCallback(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
    }
  }, []);

  // Fetch Assistants
  const fetchAssistants = useCallback(async () => {
    if (!accessToken) return;
    setIsLoadingAssistants(true);
    setError(null);
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/assistants`, {
        headers: { 'Authorization': `Bearer ${accessToken}` },
      });
      if (!response.ok) throw new Error("Gagal mengambil data asisten.");
      const data: Assistant[] = await response.json();
      setAssistants(data);
      if (data.length > 0 && !selectedAssistant) {
        setSelectedAssistant(data[0]);
      }
    } catch (err: any) { 
      setError(err.message);
      toast.error(err.message);
    }
    finally { setIsLoadingAssistants(false); }
  }, [accessToken, selectedAssistant]);

  // Fetch Chats for a selected assistant
  const fetchChatsForAssistant = useCallback(async (assistantId: string) => {
    if (!accessToken) return;
    setIsLoadingChats(true);
    setChats([]);
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/assistants/${assistantId}/chats`, {
        headers: { 'Authorization': `Bearer ${accessToken}` },
      });
      if (!response.ok) {
        const errData = await response.json();
        throw new Error(errData.message || "Gagal mengambil data percakapan.");
      }
      const data: { data: Chat[] } = await response.json();
      setChats(data.data || []);
    } catch (err: any) { 
      console.error(err.message);
      setChats([]);
    }
    finally { setIsLoadingChats(false); }
  }, [accessToken]);

  // Fetch Messages for a selected chat
  const fetchMessagesForChat = useCallback(async (assistantId: string, chatId: string) => {
    if (!accessToken) return;
    setIsLoadingMessages(true);
    setMessages([]);
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/assistants/${assistantId}/chats/${chatId}/messages`, {
        headers: { 'Authorization': `Bearer ${accessToken}` },
      });
      if (!response.ok) {
        const errData = await response.json();
        throw new Error(errData.message || "Gagal mengambil data pesan.");
      }
      const data: Message[] = await response.json(); 
      setMessages(data || []); 
      setTimeout(scrollToBottom, 100);
    } catch (err: any) { 
      console.error(err.message);
      setMessages([]);
    }
    finally { setIsLoadingMessages(false); }
  }, [accessToken, scrollToBottom]);

  useEffect(() => {
    fetchAssistants();
  }, [fetchAssistants]);

  // Auto-fetch chats when assistant is selected
  useEffect(() => {
    if (selectedAssistant) {
      fetchChatsForAssistant(selectedAssistant.id);
    }
  }, [selectedAssistant, fetchChatsForAssistant]);
  
  useEffect(() => {
    if (messages.length > 0) {
      scrollToBottom();
    }
  }, [messages, scrollToBottom]);

  useEffect(() => {
    if (selectedChat && !selectedChat.id.startsWith('new-chat-')) {
      fetchMessagesForChat(selectedAssistant?.id || '', selectedChat.id);
    }
  }, [selectedChat, fetchMessagesForChat, selectedAssistant]);

  useEffect(() => {
    adjustTextareaHeight();
  }, [userInput, adjustTextareaHeight]);

  const handleAssistantSelect = (assistantId: string) => {
    const assistant = assistants.find(a => a.id === assistantId);
    setSelectedAssistant(assistant || null);
    setSelectedChat(null);
    setMessages([]);
    removeSelectedImage();
    setShowChatList(true); // Always show chat list when assistant is selected
  };

  const handleChatSelect = (chat: Chat) => {
    setSelectedChat(chat);
    setShowChatList(false);
    removeSelectedImage();
    if (selectedAssistant) {
      fetchMessagesForChat(selectedAssistant.id, chat.id);
    }
  };
  
  // Open delete confirmation modal
  const openDeleteModal = (e: React.MouseEvent, chatId: string) => {
    e.stopPropagation(); // Prevent chat selection when clicking delete button
    setChatToDelete(chatId);
    setDeleteModalOpen(true);
  };
  
  // Delete Chat
  const handleDeleteChat = async () => {
    if (!chatToDelete || !accessToken || !selectedAssistant) return;
    
    setDeleteModalOpen(false);
    setDeletingChatId(chatToDelete);
    setError(null);
    
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/assistants/${selectedAssistant.id}/chats/${chatToDelete}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${accessToken}` },
      });
      
      if (!response.ok) throw new Error('Gagal menghapus percakapan.');
      
      // Remove the deleted chat from the list
      setChats(prevChats => prevChats.filter(chat => chat.id !== chatToDelete));
      
      // If the deleted chat was selected, clear the selection
      if (selectedChat?.id === chatToDelete) {
        setSelectedChat(null);
        setMessages([]);
      }
      
      toast.success('Percakapan berhasil dihapus');
    } catch (err: any) {
      setError(err.message);
      toast.error(err.message);
    } finally {
      setDeletingChatId(null);
      setChatToDelete(null);
    }
  };

  const handleNewChat = useCallback(() => {
    const dummyChat = {
      id: 'new-chat-' + Date.now(),
      assistantId: selectedAssistant?.id || '',
      title: 'Percakapan Baru',
      lastMessageAt: new Date().toISOString(),
      messageCount: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    setSelectedChat(dummyChat);
    setMessages([]);
    setUserInput('');
    removeSelectedImage();
    setShowChatList(false);
  }, [selectedAssistant, removeSelectedImage]);

  const handleSendMessage = async () => {
    if ((!userInput.trim() && !selectedImage) || !selectedAssistant || isSendingMessage) return;

    const isNewChat = selectedChat?.id.startsWith('new-chat-');
    const messageContent = userInput.trim();
    const imageFile = selectedImage;
    const localImagePreview = imagePreview; // Save current preview

    // Clear inputs
    setUserInput("");
    removeSelectedImage();
    setIsSendingMessage(true);

    // Immediately add user message to the UI
    const tempUserMessage: Message = {
      id: `temp-${Date.now()}`,
      chatId: selectedChat?.id || 'new',
      role: 'user',
      content: messageContent,
      createdAt: new Date().toISOString(),
      localImagePreview: localImagePreview || undefined,
      hasImage: !!imageFile
    };

    if (selectedChat) {
      setMessages(prev => [...prev, tempUserMessage]);
    } else {
      setMessages([tempUserMessage]);
    }

    setIsTyping(true);

    try {
      if (selectedChat && !isNewChat) {
        // Send message to existing chat using FormData
        const formData = new FormData();
        if (messageContent) {
          formData.append('message', messageContent);
        }
        if (imageFile) {
          formData.append('file', imageFile);
        }

        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/assistants/${selectedAssistant.id}/chats/${selectedChat.id}/messages`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${accessToken}`,
          },
          body: formData,
        });

        if (!response.ok) {
          const errData = await response.json();
          throw new Error(errData.message || "Gagal mengirim pesan.");
        }

        const data: ExistingChatResponseData = await response.json();

        setMessages(prev => {
          const withoutTemp = prev.filter(msg => msg.id !== tempUserMessage.id);

          // Preserve local image preview if backend userMessage doesn't have image URL yet
          const enhancedUserMessage = {
            ...data.userMessage,
            localImagePreview: tempUserMessage.localImagePreview || data.userMessage.localImagePreview,
            hasImage: tempUserMessage.hasImage || data.userMessage.hasImage
          };

          return [...withoutTemp, enhancedUserMessage, data.assistantMessage];
        });

        setChats(prevChats => 
          prevChats.map(chat => 
            chat.id === selectedChat.id 
              ? { ...chat, lastMessageAt: data.assistantMessage.createdAt, messageCount: chat.messageCount + 2 }
              : chat
          )
        );
      } else {
        // Start new chat using FormData
        const formData = new FormData();
        if (messageContent) {
          formData.append('message', messageContent);
        }
        if (imageFile) {
          formData.append('file', imageFile);
        }

        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/assistants/${selectedAssistant.id}/chats`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${accessToken}`,
          },
          body: formData,
        });

        if (!response.ok) {
          const errData = await response.json();
          throw new Error(errData.message || "Gagal memulai percakapan baru.");
        }

        const data: NewChatResponseData = await response.json();

        setSelectedChat(data.chat);

        // Preserve local image preview if backend userMessage doesn't have image URL yet
        const enhancedUserMessage = {
          ...data.userMessage,
          localImagePreview: tempUserMessage.localImagePreview || data.userMessage.localImagePreview,
          hasImage: tempUserMessage.hasImage || data.userMessage.hasImage
        };

        setMessages([enhancedUserMessage, data.assistantMessage]);
        setChats(prevChats => [data.chat, ...prevChats]);
      }
    } catch (err: any) {
      toast.error(err.message);
      
      if (selectedChat) {
        setMessages(prev => prev.filter(msg => msg.id !== tempUserMessage.id));
      } else {
        setMessages([]);
      }
    } finally {
      setIsSendingMessage(false);
      setIsTyping(false);
    }
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return date.toLocaleTimeString('id-ID', { hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString('id-ID', { day: 'numeric', month: 'short' });
    }
  };

  return (
    <SidebarProvider>
      <AppSidebar />
      <Toaster />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>Playground AI</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>

        <div className="flex flex-1 flex-col h-[calc(100vh-4rem)] overflow-hidden">
          {/* Assistant Selection Header */}
          <div className="border-b bg-background p-4 shrink-0">
            <div className="flex justify-between items-center mb-4">
              <div>
                <h2 className="text-2xl font-bold tracking-tight">Playground AI</h2>
                <p className="text-muted-foreground">
                  Uji coba dan berinteraksi dengan asisten AI Anda
                </p>
              </div>
              {selectedAssistant && (
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={handleNewChat}
                  className="shrink-0"
                >
                  <PlusCircle className="mr-2 h-4 w-4" /> 
                  Chat Baru
                </Button>
              )}
            </div>
            
            <div className="flex items-center gap-3">
              <Select 
                onValueChange={handleAssistantSelect} 
                disabled={isLoadingAssistants || assistants.length === 0}
                value={selectedAssistant?.id || ""}
              >
                <SelectTrigger className="w-full sm:w-64">
                  <SelectValue placeholder={
                    isLoadingAssistants ? "Memuat..." : 
                    assistants.length === 0 ? "Tidak ada asisten" : 
                    "Pilih Asisten"
                  } />
                </SelectTrigger>
                <SelectContent>
                  {assistants.map(assistant => (
                    <SelectItem key={assistant.id} value={assistant.id}>
                      <div className="flex items-center gap-2">
                        <Avatar className="h-6 w-6">
                          <AvatarFallback className="text-xs">
                            {assistant.name.charAt(0).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <span className="truncate">{assistant.name}</span>
                      </div>
                    </SelectItem>
                  ))}
                  {assistants.length === 0 && !isLoadingAssistants && (
                    <div className="p-2 text-sm text-muted-foreground">
                      Tidak ada asisten. <a href="/dashboard/assistants/new" className="text-primary hover:underline">Buat baru?</a>
                    </div>
                  )}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <div className="m-4 p-4 rounded-lg border border-destructive/20 bg-destructive/10 text-destructive shrink-0">
              <div className="flex items-center gap-2">
                <MessageSquare className="h-4 w-4" />
                <p className="text-sm font-medium">Terjadi kesalahan</p>
              </div>
              <p className="text-sm mt-1">{error}</p>
            </div>
          )}

          {/* Main Content */}
          <div className="flex flex-1 overflow-hidden min-h-0">
            {/* Chat List Sidebar - Always visible when assistant is selected */}
            {selectedAssistant && (
              <div className={`${
                showChatList ? 'flex' : 'hidden'
              } sm:flex flex-col w-full sm:w-80 border-r bg-background shrink-0`}>
                
                
                <ScrollArea className="flex-1 min-h-0">
                  {isLoadingChats ? (
                    <div className="flex justify-center items-center h-32">
                      <Loader2 className="h-6 w-6 animate-spin" />
                    </div>
                  ) : chats.length === 0 ? (
                    <div className="flex flex-col items-center justify-center h-full p-6 text-center">
                      <MessageSquare className="h-12 w-12 text-muted-foreground mb-3" />
                      <p className="text-sm text-muted-foreground mb-2">Belum ada percakapan</p>
                      <p className="text-xs text-muted-foreground">Mulai chat baru untuk memulai</p>
                    </div>
                  ) : (
                    <div className="p-3 space-y-2">
                      {chats.map(chat => (
                        <div 
                          key={chat.id} 
                          className={`p-3 rounded-lg cursor-pointer transition-all hover:bg-muted/50 ${
                            selectedChat?.id === chat.id ? 'bg-muted border border-primary/20' : ''
                          }`}
                          onClick={() => handleChatSelect(chat)}
                        >
                          <div className="space-y-2">
                            <div className="flex justify-between items-start gap-2">
                              <h4 className="text-sm font-medium line-clamp-2 flex-1">{chat.title}</h4>
                              <div className="flex items-center gap-1">
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-6 w-6 rounded-full hover:bg-destructive/10 hover:text-destructive"
                                  onClick={(e) => openDeleteModal(e, chat.id)}
                                  disabled={deletingChatId === chat.id}
                                >
                                  {deletingChatId === chat.id ? 
                                    <Loader2 className="h-3 w-3 animate-spin" /> : 
                                    <Trash2 className="h-3 w-3" />}
                                </Button>
                                <Badge variant="secondary" className="text-xs shrink-0">
                                  {chat.messageCount}
                                </Badge>
                              </div>
                            </div>
                            <div className="flex items-center justify-between text-xs text-muted-foreground">
                              <div className="flex items-center gap-1">
                                <Clock className="h-3 w-3 shrink-0" />
                                <span className="truncate">{formatTime(chat.lastMessageAt)}</span>
                              </div>
                              <span className="text-xs text-muted-foreground/70 shrink-0">
                                {new Date(chat.createdAt).toLocaleDateString('id-ID', { 
                                  day: 'numeric', 
                                  month: 'short' 
                                })}
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </ScrollArea>
              </div>
            )}

            {/* Chat Window */}
            <div className={`${
              showChatList && selectedAssistant ? 'hidden' : 'flex'
            } sm:flex flex-col flex-1 min-h-0 relative overflow-hidden`}>
              {!selectedAssistant ? (
                <div className="flex-1 flex items-center justify-center bg-muted/20">
                  <div className="text-center max-w-md p-6">
                    <div className="relative mb-6">
                      <div className="w-20 h-20 rounded-2xl bg-primary/10 flex items-center justify-center mx-auto">
                        <Sparkles className="h-10 w-10 text-primary" />
                      </div>
                      <div className="absolute -top-1 -right-1 w-6 h-6 rounded-full bg-primary flex items-center justify-center">
                        <Zap className="h-3 w-3 text-primary-foreground" />
                      </div>
                    </div>
                    <h3 className="text-xl font-semibold mb-2">Selamat Datang di AI Playground</h3>
                    <p className="text-muted-foreground mb-6">Pilih asisten AI untuk mulai berinteraksi dan menguji kemampuannya</p>
                    {assistants.length === 0 && (
                      <Button asChild>
                        <a href="/dashboard/assistants/new">Buat Asisten Pertama</a>
                      </Button>
                    )}
                  </div>
                </div>
              ) : (
                <>
                  {/* Chat Header */}
                  <div className="p-4 border-b bg-background/95 backdrop-blur shrink-0">
                    <div className="flex items-center gap-3">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="sm:hidden"
                        onClick={() => setShowChatList(true)}
                      >
                        <ArrowLeft className="h-4 w-4" />
                      </Button>
                      
                      <Avatar className="h-8 w-8">
                        <AvatarFallback className="text-sm">
                          {selectedAssistant.name.charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      
                      <div className="flex-1 min-w-0">
                        <h3 className="font-semibold text-sm truncate">{selectedAssistant.name}</h3>
                        {selectedChat && (
                          <p className="text-xs text-muted-foreground truncate">
                            {selectedChat.id.startsWith('new-chat-') ? 'Percakapan Baru' : selectedChat.title}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Messages Area - Only this should scroll */}
                  <div className="absolute inset-0 top-[8.5rem] bottom-[5.5rem] overflow-y-auto">
                    <div className="p-4 space-y-4">
                        {isLoadingMessages ? (
                          <div className="flex justify-center items-center h-32">
                            <Loader2 className="h-8 w-8 animate-spin" />
                          </div>
                        ) : messages.length === 0 ? (
                          <div className="flex items-center justify-center h-full min-h-[200px]">
                            <div className="text-center">
                              <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
                              <p className="text-muted-foreground">
                                {selectedChat?.id.startsWith('new-chat-') 
                                  ? `Mulai percakapan dengan ${selectedAssistant?.name}`
                                  : 'Belum ada pesan dalam percakapan ini'}
                              </p>
                            </div>
                          </div>
                        ) : (
                          <>
                            {messages.map(msg => {
                              const hasImage = msg.localImagePreview || (msg.content.startsWith('https://api.heylo.co.id/files/') && msg.content.includes('/stream'));
                              const hasText = msg.content && !(msg.content.startsWith('https://api.heylo.co.id/files/') && msg.content.includes('/stream'));

                              return (
                                <div key={msg.id} className={`flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                                  <div className={`flex gap-3 max-w-[85%] ${msg.role === 'user' ? 'flex-row-reverse' : 'flex-row'}`}>
                                    <Avatar className="h-8 w-8 shrink-0 self-end">
                                      <AvatarFallback className="text-xs">
                                        {msg.role === 'user' ? (
                                          <User className="h-4 w-4" />
                                        ) : (
                                          selectedAssistant.name.charAt(0).toUpperCase()
                                        )}
                                      </AvatarFallback>
                                    </Avatar>

                                    <div className="flex flex-col gap-2">
                                      {/* Display image outside chat bubble */}
                                      {hasImage && (
                                        <div className={`flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                                          <MessageImage
                                            message={msg}
                                            className="max-w-full h-auto rounded-lg"
                                            style={{ maxHeight: '300px', maxWidth: '250px' }}
                                          />
                                        </div>
                                      )}

                                      {/* Display text content in chat bubble (only if there's text) */}
                                      {hasText && (
                                        <div className={`rounded-2xl p-4 ${
                                          msg.role === 'user'
                                            ? 'bg-primary text-primary-foreground'
                                            : 'bg-muted border'
                                        }`}>
                                          <p className="text-sm whitespace-pre-wrap break-words">{msg.content}</p>
                                          <p className={`text-xs mt-2 ${
                                            msg.role === 'user'
                                              ? 'text-primary-foreground/70'
                                              : 'text-muted-foreground'
                                          }`}>
                                            {formatTime(msg.createdAt)}
                                          </p>
                                        </div>
                                      )}

                                      {/* Display timestamp for image-only messages */}
                                      {hasImage && !hasText && (
                                        <div className={`flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                                          <p className="text-xs text-muted-foreground px-2">
                                            {formatTime(msg.createdAt)}
                                          </p>
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              );
                            })}
                            
                            {/* Typing Indicator */}
                            {isTyping && (
                              <div className="flex justify-start">
                                <div className="flex gap-3 max-w-[85%]">
                                  <Avatar className="h-8 w-8 shrink-0">
                                    <AvatarFallback className="text-xs">
                                      {selectedAssistant.name.charAt(0).toUpperCase()}
                                    </AvatarFallback>
                                  </Avatar>
                                  
                                  <div className="bg-muted border rounded-2xl p-4">
                                    <div className="flex items-center gap-2">
                                      <div className="flex gap-1">
                                        <div className="w-2 h-2 bg-muted-foreground/60 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                                        <div className="w-2 h-2 bg-muted-foreground/60 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                                        <div className="w-2 h-2 bg-muted-foreground/60 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                                      </div>
                                      <span className="text-xs text-muted-foreground">{selectedAssistant.name} sedang mengetik...</span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            )}
                            
                            <div ref={messagesEndRef} />
                          </>
                        )}
                      </div>
                  </div>

                  {/* Message Input - Fixed at bottom */}
                  <div className="p-4 border-t bg-background/95 backdrop-blur absolute bottom-0 left-0 right-0 z-10">
                    {/* Image Preview */}
                    {imagePreview && (
                      <div className="mb-3 p-3 border rounded-lg bg-muted/50">
                        <div className="flex items-start gap-3">
                          <div className="relative">
                            <img
                              src={imagePreview}
                              alt="Preview"
                              className="w-16 h-16 object-cover rounded-lg border"
                            />
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium truncate">{selectedImage?.name}</p>
                            <p className="text-xs text-muted-foreground">
                              {selectedImage && (selectedImage.size / 1024 / 1024).toFixed(2)} MB
                            </p>
                          </div>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={removeSelectedImage}
                            className="shrink-0 h-8 w-8 p-0 hover:bg-destructive/10 hover:text-destructive"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    )}

                    <form onSubmit={(e) => { e.preventDefault(); handleSendMessage(); }} className="flex items-end gap-3">
                      <div className="flex-1 relative">
                        <Textarea
                          ref={textareaRef}
                          value={userInput}
                          onChange={(e) => setUserInput(e.target.value)}
                          placeholder={`Pesan untuk ${selectedAssistant.name}...`}
                          className="min-h-[44px] max-h-[120px] resize-none border-2 focus:border-primary/50 transition-colors pr-12"
                          disabled={isSendingMessage || isLoadingMessages}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter' && !e.shiftKey) {
                              e.preventDefault();
                              handleSendMessage();
                            }
                          }}
                          rows={1}
                        />
                      </div>

                      {/* Image Upload Button */}
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) {
                            handleImageSelect(file);
                          }
                        }}
                        className="hidden"
                      />

                      <Button
                        type="button"
                        variant="outline"
                        size="icon"
                        onClick={() => fileInputRef.current?.click()}
                        disabled={isSendingMessage || isLoadingMessages}
                        className="shrink-0 h-11 w-11 rounded-xl"
                      >
                        <ImagePlus className="h-5 w-5" />
                      </Button>

                      <Button
                        type="submit"
                        disabled={(!userInput.trim() && !selectedImage) || isSendingMessage || isLoadingMessages}
                        className="shrink-0 h-11 w-11 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
                        size="icon"
                      >
                        {isSendingMessage ? (
                          <Loader2 className="h-5 w-5 animate-spin" />
                        ) : (
                          <Send className="h-5 w-5" />
                        )}
                      </Button>
                    </form>

                    <p className="text-xs text-muted-foreground mt-2 hidden sm:block">
                      Tekan Enter untuk mengirim, Shift+Enter untuk baris baru • Klik ikon gambar untuk upload image
                    </p>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </SidebarInset>
      
      {/* Delete Chat Confirmation Modal */}
      <AlertDialog open={deleteModalOpen} onOpenChange={setDeleteModalOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Hapus Percakapan</AlertDialogTitle>
            <AlertDialogDescription>
              Apakah Anda yakin ingin menghapus percakapan ini? Tindakan ini tidak dapat dibatalkan.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Batal</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteChat}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {deletingChatId ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Menghapus...
                </>
              ) : (
                'Hapus'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </SidebarProvider>
  );
}
