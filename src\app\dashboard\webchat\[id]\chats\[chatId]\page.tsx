"use client";

import React, { useEffect, useState, useCallback } from 'react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { AppSidebar } from '@/components/app-sidebar';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { SidebarInset, SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar';
import { ArrowLeft, MessageSquare, AlertTriangle, Loader2, User, Bot, Calendar, Globe } from 'lucide-react';

interface ChatMessage {
  id: string;
  chatId: string;
  role: 'user' | 'assistant';
  content: string;
  createdAt: string;
  updatedAt: string;
}

interface ChatSession {
  id: string;
  webchatId: string;
  isOnline: boolean;
  autoReplyEnabled: boolean;
  createdAt: string;
  messageCount: number;
  latestMessage?: {
    userId: string;
    role: string;
    content: string;
    createdAt: string;
  };
}

interface MessagesResponse {
  data: ChatMessage[];
  totalItems: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
}

export default function WebchatChatDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { accessToken } = useAuth();
  const webchatId = params.id as string; // Changed from webchatId to id
  const chatId = params.chatId as string;

  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [chatSession, setChatSession] = useState<ChatSession | null>(null);
  const [webchatName, setWebchatName] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchChatData = useCallback(async () => {
    if (!accessToken || !webchatId || !chatId) {
      setIsLoading(false);
      return;
    }
    setIsLoading(true);
    setError(null);
    try {
      // Fetch webchat info
      const webchatResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/webchat/${webchatId}`, {
        headers: { Authorization: `Bearer ${accessToken}` },
      });
      
      if (webchatResponse.ok) {
        const webchatData = await webchatResponse.json();
        setWebchatName(webchatData.name);
      }

      // Fetch chat session info
      const chatsResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/webchat/${webchatId}/chats`, {
        headers: { Authorization: `Bearer ${accessToken}` },
      });
      
      if (chatsResponse.ok) {
        const chatsData = await chatsResponse.json();
        const currentChat = chatsData.data.find((chat: ChatSession) => chat.id === chatId);
        if (currentChat) {
          setChatSession(currentChat);
        }
      }

      // Fetch messages
      const messagesResponse = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/webchat/${webchatId}/chats/${chatId}/messages?limit=100`,
        {
          headers: { Authorization: `Bearer ${accessToken}` },
        }
      );
      
      if (!messagesResponse.ok) {
        const errorData = await messagesResponse.json().catch(() => ({ message: 'Gagal mengambil pesan.' }));
        throw new Error(errorData.message || 'Gagal mengambil pesan.');
      }
      
      const messagesData: MessagesResponse = await messagesResponse.json();
      setMessages(messagesData.data);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  }, [accessToken, webchatId, chatId]);



  useEffect(() => {
    fetchChatData();
  }, [fetchChatData]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('id-ID');
  };

  if (isLoading) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <div className="flex items-center justify-center h-screen">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p>Memuat detail percakapan...</p>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  if (error) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <div className="flex items-center justify-center h-screen">
            <div className="text-center">
              <AlertTriangle className="h-8 w-8 mx-auto mb-4 text-destructive" />
              <p className="text-destructive">{error}</p>
              <Button asChild className="mt-4">
                <Link href="/dashboard/webchat/chats">Kembali ke Daftar Percakapan</Link>
              </Button>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbLink href="/dashboard/webchat">Webchat</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbLink href="/dashboard/webchat/chats">Histori Percakapan</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>Detail Percakapan</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>

        <main className="flex-1 p-4 md:p-6 space-y-6">
          <div className="mb-6">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => router.back()}
            >
              <ArrowLeft className="h-4 w-4 mr-1" /> Kembali
            </Button>
          </div>

          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <h1 className="text-xl sm:text-2xl font-bold tracking-tight">Detail Percakapan Webchat</h1>
              <p className="text-muted-foreground">
                Widget: {webchatName} • {messages.length} pesan
              </p>
            </div>
          </div>

          {/* Messages */}
          <div>
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MessageSquare className="h-5 w-5" />
                    Riwayat Pesan
                  </CardTitle>
                  <CardDescription>
                    Semua pesan dalam percakapan ini
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {messages.length === 0 ? (
                    <div className="text-center py-8">
                      <MessageSquare className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                      <p className="text-muted-foreground">Belum ada pesan dalam percakapan ini</p>
                    </div>
                  ) : (
                    <div className="space-y-4 max-h-[600px] overflow-y-auto">
                      {messages.map((message) => (
                        <div
                          key={message.id}
                          className={`flex gap-3 ${
                            message.role === 'assistant' ? 'justify-start' : 'justify-end'
                          }`}
                        >
                          <div
                            className={`flex gap-3 max-w-[80%] ${
                              message.role === 'assistant' ? 'flex-row' : 'flex-row-reverse'
                            }`}
                          >
                            <div className="flex-shrink-0">
                              {message.role === 'assistant' ? (
                                <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center">
                                  <Bot className="h-4 w-4 text-primary-foreground" />
                                </div>
                              ) : (
                                <div className="w-8 h-8 rounded-full bg-muted flex items-center justify-center">
                                  <User className="h-4 w-4 text-muted-foreground" />
                                </div>
                              )}
                            </div>
                            <div
                              className={`rounded-lg p-3 ${
                                message.role === 'assistant'
                                  ? 'bg-muted text-foreground'
                                  : 'bg-primary text-primary-foreground'
                              }`}
                            >
                              <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                              <p
                                className={`text-xs mt-2 ${
                                  message.role === 'assistant'
                                    ? 'text-muted-foreground'
                                    : 'text-primary-foreground/70'
                                }`}
                              >
                                {formatDate(message.createdAt)}
                              </p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
          </div>
        </main>
      </SidebarInset>
    </SidebarProvider>
  );
}
