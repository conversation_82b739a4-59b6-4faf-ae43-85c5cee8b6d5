"use client";

import { AppSidebar } from "@/components/app-sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Globe, ArrowLeft, Loader2, <PERSON><PERSON> } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import React, { useEffect, useState } from "react";
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { toast } from "sonner";
import { Toaster } from "@/components/ui/sonner";

interface Assistant {
  id: string;
  name: string;
  description: string;
}

interface WebchatForm {
  name: string;
  title: string;
  greetingMessage: string;
  position: string;
  color: string;
  siteUrl: string;
  assistantId: string;
  isEnabled: boolean;
}

export default function NewWebchatPage() {
  const [assistants, setAssistants] = useState<Assistant[]>([]);
  const [isLoadingAssistants, setIsLoadingAssistants] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { accessToken } = useAuth();
  const router = useRouter();

  const [formData, setFormData] = useState<WebchatForm>({
    name: '',
    title: '',
    greetingMessage: 'Halo! Ada yang bisa saya bantu?',
    position: 'bottom-right',
    color: '#02C1B0',
    siteUrl: '',
    assistantId: '',
    isEnabled: true,
  });

  const [errors, setErrors] = useState<Partial<WebchatForm>>({});

  useEffect(() => {
    const fetchAssistants = async () => {
      if (!accessToken) {
        setIsLoadingAssistants(false);
        return;
      }
      
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/assistants`, {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
          },
        });
        if (!response.ok) {
          throw new Error('Failed to fetch assistants');
        }
        const data: Assistant[] = await response.json();
        setAssistants(data);
      } catch (err) {
        toast.error("Gagal memuat daftar asisten");
      }
      setIsLoadingAssistants(false);
    };

    fetchAssistants();
  }, [accessToken]);

  const validateForm = (): boolean => {
    const newErrors: Partial<WebchatForm> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Nama webchat harus diisi';
    }

    if (!formData.title.trim()) {
      newErrors.title = 'Judul widget harus diisi';
    }

    if (!formData.greetingMessage.trim()) {
      newErrors.greetingMessage = 'Pesan sambutan harus diisi';
    }

    if (!formData.siteUrl.trim()) {
      newErrors.siteUrl = 'URL website harus diisi';
    } else if (!formData.siteUrl.startsWith('https://')) {
      newErrors.siteUrl = 'URL harus menggunakan format https://domain.com';
    }

    if (!formData.assistantId || formData.assistantId === 'loading' || formData.assistantId === 'no-assistants') {
      newErrors.assistantId = 'Pilih asisten yang akan digunakan';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    if (!accessToken) {
      toast.error("Authentication token not found");
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/webchat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`,
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create webchat');
      }

      sessionStorage.setItem('webchatToast', 'Webchat berhasil dibuat!');
      router.push('/dashboard/webchat');
    } catch (err: any) {
      toast.error(`Error: ${err.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof WebchatForm, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const positions = [
    { value: 'bottom-right', label: 'Kanan Bawah' },
    { value: 'bottom-left', label: 'Kiri Bawah' },
    { value: 'top-right', label: 'Kanan Atas' },
    { value: 'top-left', label: 'Kiri Atas' },
  ];

  return (
    <SidebarProvider>
      <AppSidebar />
      <Toaster />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbLink href="/dashboard/webchat">Webchat</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>Buat Baru</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>
        
        <main className="flex-1 p-4 md:p-6 space-y-6">
          <div className="mb-6">
            <Link href="/dashboard/webchat" passHref>
              <Button variant="outline" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" /> Kembali ke Daftar Webchat
              </Button>
            </Link>
          </div>

          <div className="mb-6">
            <h2 className="text-2xl font-bold tracking-tight">Buat Webchat Widget</h2>
            <p className="text-muted-foreground">
              Konfigurasi widget chat untuk website Anda
            </p>
          </div>

            {/* Form */}
            <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Globe className="h-5 w-5" />
                    Konfigurasi Widget
                  </CardTitle>
                  <CardDescription>
                    Atur tampilan dan perilaku widget chat
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSubmit} className="space-y-4">
                    {/* Basic Info */}
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="name">Nama Webchat *</Label>
                        <Input
                          id="name"
                          value={formData.name}
                          onChange={(e) => handleInputChange('name', e.target.value)}
                          placeholder="Contoh: Widget Customer Support"
                          className={errors.name ? 'border-destructive' : ''}
                        />
                        {errors.name && (
                          <p className="text-sm text-destructive mt-1">{errors.name}</p>
                        )}
                      </div>

                      <div>
                        <Label htmlFor="title">Judul Widget *</Label>
                        <Input
                          id="title"
                          value={formData.title}
                          onChange={(e) => handleInputChange('title', e.target.value)}
                          placeholder="Contoh: Customer Support"
                          className={errors.title ? 'border-destructive' : ''}
                        />
                        {errors.title && (
                          <p className="text-sm text-destructive mt-1">{errors.title}</p>
                        )}
                      </div>

                      <div>
                        <Label htmlFor="greetingMessage">Pesan Sambutan *</Label>
                        <Textarea
                          id="greetingMessage"
                          value={formData.greetingMessage}
                          onChange={(e) => handleInputChange('greetingMessage', e.target.value)}
                          placeholder="Pesan yang akan ditampilkan saat widget dibuka"
                          className={errors.greetingMessage ? 'border-destructive' : ''}
                        />
                        {errors.greetingMessage && (
                          <p className="text-sm text-destructive mt-1">{errors.greetingMessage}</p>
                        )}
                      </div>

                      <div>
                        <Label htmlFor="siteUrl">URL Website *</Label>
                        <Input
                          id="siteUrl"
                          value={formData.siteUrl}
                          onChange={(e) => handleInputChange('siteUrl', e.target.value)}
                          placeholder="https://domain.com"
                          className={errors.siteUrl ? 'border-destructive' : ''}
                        />
                        {errors.siteUrl && (
                          <p className="text-sm text-destructive mt-1">{errors.siteUrl}</p>
                        )}
                      </div>

                      <div>
                        <Label htmlFor="assistantId">Pilih Asisten *</Label>
                        <Select
                          value={formData.assistantId}
                          onValueChange={(value) => handleInputChange('assistantId', value)}
                        >
                          <SelectTrigger className={errors.assistantId ? 'border-destructive' : ''}>
                            <SelectValue placeholder="Pilih asisten yang akan digunakan" />
                          </SelectTrigger>
                          <SelectContent>
                            {isLoadingAssistants ? (
                              <SelectItem value="loading" disabled>
                                <div className="flex items-center gap-2">
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                  Memuat asisten...
                                </div>
                              </SelectItem>
                            ) : assistants.length === 0 ? (
                              <SelectItem value="no-assistants" disabled>
                                <div className="flex items-center gap-2">
                                  <Bot className="h-4 w-4" />
                                  Belum ada asisten
                                </div>
                              </SelectItem>
                            ) : (
                              assistants.map((assistant) => (
                                <SelectItem key={assistant.id} value={assistant.id}>
                                  <div className="flex items-center gap-2">
                                    <Bot className="h-4 w-4" />
                                    <div>
                                      <div className="font-medium">{assistant.name}</div>
                                      {assistant.description && (
                                        <div className="text-xs text-muted-foreground">
                                          {assistant.description}
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                </SelectItem>
                              ))
                            )}
                          </SelectContent>
                        </Select>
                        {errors.assistantId && (
                          <p className="text-sm text-destructive mt-1">{errors.assistantId}</p>
                        )}
                        {assistants.length === 0 && !isLoadingAssistants && (
                          <p className="text-sm text-muted-foreground mt-1">
                            <Link href="/dashboard/assistants/new" className="text-primary hover:underline">
                              Buat asisten terlebih dahulu
                            </Link>
                          </p>
                        )}
                      </div>
                    </div>

                    {/* Appearance */}
                    <div className="space-y-4 pt-4 border-t">
                      <h3 className="font-medium">Tampilan Widget</h3>
                      
                      <div>
                        <Label htmlFor="position">Posisi Widget</Label>
                        <Select
                          value={formData.position}
                          onValueChange={(value) => handleInputChange('position', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {positions.map((position) => (
                              <SelectItem key={position.value} value={position.value}>
                                {position.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label htmlFor="color">Warna Utama</Label>
                        <div className="flex items-center gap-3">
                          <Input
                            id="color"
                            type="color"
                            value={formData.color}
                            onChange={(e) => handleInputChange('color', e.target.value)}
                            className="w-16 h-10 p-1 border rounded"
                          />
                          <Input
                            value={formData.color}
                            onChange={(e) => handleInputChange('color', e.target.value)}
                            placeholder="#02C1B0"
                            className="flex-1"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Settings */}
                    <div className="space-y-4 pt-4 border-t">
                      <h3 className="font-medium">Pengaturan</h3>

                      <div className="flex items-center justify-between">
                        <div>
                          <Label htmlFor="isEnabled">Aktifkan Widget</Label>
                          <p className="text-sm text-muted-foreground">
                            Widget akan berfungsi di website
                          </p>
                        </div>
                        <Switch
                          id="isEnabled"
                          checked={formData.isEnabled}
                          onCheckedChange={(checked) => handleInputChange('isEnabled', checked)}
                        />
                      </div>
                    </div>

                    <div className="pt-6">
                      <Button
                        type="submit"
                        disabled={isSubmitting || assistants.length === 0}
                        className="w-full"
                      >
                        {isSubmitting ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            Membuat...
                          </>
                        ) : (
                          'Buat Widget'
                        )}
                      </Button>
                    </div>
                  </form>
                </CardContent>
            </Card>

        </main>
      </SidebarInset>
    </SidebarProvider>
  )
}
