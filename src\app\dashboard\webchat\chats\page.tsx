"use client";

import React, { useEffect, useState, useCallback } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { AppSidebar } from '@/components/app-sidebar';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Separator } from '@/components/ui/separator';
import { SidebarInset, SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar';
import { MessageSquare, AlertTriangle, Loader2, Eye, Globe, ArrowLeft, Calendar, User, Plus } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious
} from '@/components/ui/pagination';

interface WebchatChat {
  id: string;
  webchatId: string;
  webchatName?: string;
  isOnline: boolean;
  autoReplyEnabled: boolean;
  createdAt: string;
  messageCount: number;
  latestMessage?: {
    userId: string;
    role: string;
    content: string;
    createdAt: string;
  };
}

interface WebchatChatsResponse {
  data: WebchatChat[];
  totalItems: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
}

interface WebchatInstance {
  id: string;
  name: string;
  siteUrl: string;
}

export default function WebchatChatsPage() {
  const { accessToken } = useAuth();
  const [chats, setChats] = useState<WebchatChat[]>([]);
  const [webchats, setWebchats] = useState<WebchatInstance[]>([]);
  const [selectedWebchat, setSelectedWebchat] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Fetch webchat instances
  const fetchWebchats = useCallback(async () => {
    if (!accessToken) return;

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/webchat`, {
        headers: { Authorization: `Bearer ${accessToken}` },
      });

      if (!response.ok) {
        throw new Error('Gagal mengambil data webchat.');
      }

      const data: WebchatInstance[] = await response.json();
      setWebchats(data || []);

      if (data && data.length > 0 && !selectedWebchat) {
        setSelectedWebchat(data[0].id);
      }
    } catch (err: any) {
      console.error('Error fetching webchat instances:', err);
      setWebchats([]);
    }
  }, [accessToken, selectedWebchat]);

  // Fetch chats for selected webchat
  const fetchChats = useCallback(async () => {
    if (!accessToken || !selectedWebchat) {
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/webchat/${selectedWebchat}/chats?page=${currentPage}&limit=${itemsPerPage}`,
        {
          headers: { Authorization: `Bearer ${accessToken}` },
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Gagal mengambil data percakapan.' }));
        throw new Error(errorData.message || 'Gagal mengambil data percakapan.');
      }

      const data: WebchatChatsResponse = await response.json();
      setChats(data?.data || []);
      setTotalPages(data?.totalPages || 1);
      setTotalItems(data?.totalItems || 0);
      setCurrentPage(data?.currentPage || 1);
    } catch (err: any) {
      setError(err.message);
      setChats([]);
    } finally {
      setIsLoading(false);
    }
  }, [accessToken, selectedWebchat, currentPage, itemsPerPage]);

  useEffect(() => {
    fetchWebchats();
  }, [fetchWebchats]);

  useEffect(() => {
    fetchChats();
  }, [fetchChats, selectedWebchat, currentPage, itemsPerPage]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('id-ID');
  };

  const truncateMessage = (message: string, maxLength: number = 50) => {
    if (!message || typeof message !== 'string') return '';
    if (message.length <= maxLength) return message;
    return message.substring(0, maxLength) + '...';
  };

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbLink href="/dashboard/webchat">Webchat</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>Histori Percakapan</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>

        <main className="flex-1 p-4 md:p-6 space-y-6">
          <div className="mb-6">
            <Link href="/dashboard/webchat" passHref>
              <Button variant="outline" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" /> Kembali ke Daftar Webchat
              </Button>
            </Link>
          </div>

          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div>
              <h1 className="text-2xl font-bold tracking-tight">Histori Percakapan Webchat</h1>
              <p className="text-muted-foreground">
                Lihat dan kelola percakapan dari widget webchat Anda.
              </p>
            </div>
          </div>

          {/* Webchat selector */}
          <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
            <div className="w-full md:w-72">
              <Select
                value={selectedWebchat}
                onValueChange={setSelectedWebchat}
                disabled={isLoading || !webchats || webchats.length === 0}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Pilih Widget Webchat" />
                </SelectTrigger>
                <SelectContent>
                  {webchats && webchats.map(webchat => (
                    <SelectItem key={webchat.id} value={webchat.id}>
                      {webchat.name} ({webchat.siteUrl})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="w-full md:w-48">
              <Select
                value={itemsPerPage.toString()}
                onValueChange={(value) => setItemsPerPage(parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Item per halaman" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5 item</SelectItem>
                  <SelectItem value="10">10 item</SelectItem>
                  <SelectItem value="20">20 item</SelectItem>
                  <SelectItem value="50">50 item</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {isLoading && (
            <div className="flex flex-col items-center justify-center py-20">
              <div className="relative">
                <div className="w-16 h-16 rounded-full bg-primary animate-pulse"></div>
                <Globe className="absolute inset-0 m-auto h-8 w-8 text-primary-foreground animate-bounce" />
              </div>
              <p className="mt-6 text-lg font-medium text-muted-foreground">Memuat percakapan...</p>
            </div>
          )}

          {error && !isLoading && (
            <div className="flex flex-col items-center justify-center py-20">
              <div className="p-4 rounded-full bg-destructive/10 mb-4">
                <AlertTriangle className="h-8 w-8 text-destructive" />
              </div>
              <h3 className="text-lg font-semibold text-destructive mb-2">Terjadi Kesalahan</h3>
              <p className="text-muted-foreground text-center mb-6">{error}</p>
              <Button onClick={() => fetchChats()} variant="outline">
                Coba Lagi
              </Button>
            </div>
          )}

          {!isLoading && !error && (!selectedWebchat || !webchats || webchats.length === 0) && (
            <div className="flex flex-col items-center justify-center py-20">
              <div className="relative mb-8">
                <div className="w-24 h-24 rounded-3xl bg-primary/10 dark:bg-primary/20 flex items-center justify-center">
                  <MessageSquare className="h-12 w-12 text-primary" />
                </div>
                <div className="absolute -top-2 -right-2 w-8 h-8 rounded-full bg-primary flex items-center justify-center">
                  <Plus className="h-4 w-4 text-primary-foreground" />
                </div>
              </div>
              <h3 className="text-2xl font-bold mb-3">Tidak Ada Widget Webchat</h3>
              <p className="text-muted-foreground text-center max-w-md mb-8">
                Anda belum memiliki widget webchat yang terhubung. Buat widget webchat untuk mulai berinteraksi dengan pengguna melalui website.
              </p>
              <Button asChild size="lg" className="bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg">
                <Link href="/dashboard/webchat/new">
                  <MessageSquare className="mr-2 h-5 w-5" />
                  Buat Widget Webchat
                </Link>
              </Button>
            </div>
          )}

          {!isLoading && !error && selectedWebchat && chats && chats.length === 0 && (
            <div className="flex flex-col items-center justify-center py-20">
              <div className="relative mb-8">
                <div className="w-24 h-24 rounded-3xl bg-primary/10 dark:bg-primary/20 flex items-center justify-center">
                  <MessageSquare className="h-12 w-12 text-primary" />
                </div>
              </div>
              <h3 className="text-2xl font-bold mb-3">Belum Ada Percakapan</h3>
              <p className="text-muted-foreground text-center max-w-md mb-8">
                Widget webchat ini belum memiliki riwayat percakapan. Percakapan akan muncul di sini setelah pengguna mulai berinteraksi dengan widget di website.
              </p>
            </div>
          )}

          {!isLoading && !error && chats && chats.length > 0 && (
            (() => {
              const filteredChats = chats.filter(chat => chat.messageCount > 0);
              if (filteredChats.length === 0) {
                return (
                  <div className="flex flex-col items-center justify-center py-20">
                    <div className="relative mb-8">
                      <div className="w-24 h-24 rounded-3xl bg-primary/10 dark:bg-primary/20 flex items-center justify-center">
                        <MessageSquare className="h-12 w-12 text-primary" />
                      </div>
                    </div>
                    <h3 className="text-2xl font-bold mb-3">Tidak Ada Percakapan Aktif</h3>
                    <p className="text-muted-foreground text-center max-w-md mb-8">
                      Tidak ada percakapan dengan pesan lebih dari nol untuk ditampilkan saat ini.
                    </p>
                  </div>
                );
              }
              return (
            <Card>
              <CardHeader>
                <CardTitle>Daftar Percakapan</CardTitle>
                <CardDescription>
                  Menampilkan {chats.filter(chat => chat.messageCount > 0)?.length || 0} percakapan dari total {totalItems} percakapan (hanya menampilkan percakapan dengan pesan lebih dari 0).
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>ID Percakapan</TableHead>
                      <TableHead>Pesan Terakhir</TableHead>
                      <TableHead>Jumlah Pesan</TableHead>
                      <TableHead>Waktu</TableHead>
                      <TableHead className="text-right">Aksi</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {chats && chats.filter(chat => chat.messageCount > 0).map((chat) => (
                      <TableRow key={chat.id}>
                        <TableCell className="font-medium">{chat.id.substring(0, 8)}...</TableCell>
                        <TableCell>
                          {chat.latestMessage ? (
                            <div>
                              <div className="text-sm">
                                {truncateMessage(chat.latestMessage.content)}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {chat.latestMessage.role === 'user' ? 'User' : 'Assistant'}
                              </div>
                            </div>
                          ) : (
                            <span className="text-muted-foreground">Belum ada pesan</span>
                          )}
                        </TableCell>
                        <TableCell>{chat.messageCount}</TableCell>
                        <TableCell>
                          {formatDate(chat.latestMessage?.createdAt || chat.createdAt)}
                        </TableCell>
                        <TableCell className="text-right">
                          <Link href={`/dashboard/webchat/${chat.webchatId}/chats/${chat.id}`} passHref>
                            <Button variant="outline" size="sm">
                              <Eye className="mr-1 h-3 w-3" /> Lihat Detail
                            </Button>
                          </Link>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
              {totalPages > 1 && (
                <CardFooter className="flex justify-center">
                  <Pagination>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious
                          href="#"
                          onClick={(e) => {
                            e.preventDefault();
                            if (currentPage > 1) setCurrentPage(currentPage - 1);
                          }}
                          className={currentPage <= 1 ? "pointer-events-none opacity-50" : ""}
                        />
                      </PaginationItem>

                      {Array.from({ length: totalPages }, (_, i) => i + 1)
                        .filter(page => {
                          // Show current page, first page, last page, and pages around current page
                          return page === 1 ||
                                 page === totalPages ||
                                 (page >= currentPage - 1 && page <= currentPage + 1);
                        })
                        .map((page, index, array) => {
                          // Add ellipsis
                          const showEllipsisBefore = index > 0 && array[index - 1] !== page - 1;
                          const showEllipsisAfter = index < array.length - 1 && array[index + 1] !== page + 1;

                          return (
                            <React.Fragment key={page}>
                              {showEllipsisBefore && (
                                <PaginationItem>
                                  <span className="px-4 py-2">...</span>
                                </PaginationItem>
                              )}

                              <PaginationItem>
                                <PaginationLink
                                  href="#"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    setCurrentPage(page);
                                  }}
                                  isActive={page === currentPage}
                                >
                                  {page}
                                </PaginationLink>
                              </PaginationItem>

                              {showEllipsisAfter && (
                                <PaginationItem>
                                  <span className="px-4 py-2">...</span>
                                </PaginationItem>
                              )}
                            </React.Fragment>
                          );
                        })}

                      <PaginationItem>
                        <PaginationNext
                          href="#"
                          onClick={(e) => {
                            e.preventDefault();
                            if (currentPage < totalPages) setCurrentPage(currentPage + 1);
                          }}
                          className={currentPage >= totalPages ? "pointer-events-none opacity-50" : ""}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                </CardFooter>
              )}
            </Card>
          );
        })()
      )}
        </main>
      </SidebarInset>
    </SidebarProvider>
  );
}
