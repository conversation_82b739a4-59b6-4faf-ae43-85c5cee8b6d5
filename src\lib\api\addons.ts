/**
 * API functions for managing add-ons
 */

/**
 * Interface for add-on price tier
 */
export interface AddonPriceTier {
  quantity: number;
  price: number;
}

/**
 * Interface for available add-ons
 */
export interface Addon {
  id: string;
  code: string;
  name: string;
  description: string;
  price: number;
  minQuantity: number;
  maxQuantity: number;
  prices: AddonPriceTier[];
}

/**
 * Interface for add-on purchase request item
 */
export interface AddonPurchaseItem {
  id: string;
  quantity: number;
}

/**
 * Interface for add-on purchase request
 */
export interface AddonPurchaseRequest {
  items: AddonPurchaseItem[];
}

/**
 * Interface for invoice item
 */
export interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  price: number;
}

/**
 * Interface for invoice total
 */
export interface InvoiceTotal {
  type: string;
  description: string;
  amount: number;
}

/**
 * Interface for invoice response
 */
export interface InvoiceResponse {
  id: string;
  userId: string;
  planId: string;
  purchaseId: string;
  items: InvoiceItem[];
  totals: InvoiceTotal[];
  paidDate: string;
  metadata: Record<string, any>;
  status: string;
  invoiceCode: string;
  invoiceLink: string;
  paymentLink: string;
  paymentToken: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Fetch available add-ons
 * @returns Promise with array of available add-ons
 */
export async function fetchAddons(accessToken: string): Promise<Addon[]> {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/plans/addon`, {
      headers: { Authorization: `Bearer ${accessToken}` },
    });

    if (!response.ok) {
      throw new Error("Gagal mengambil data add-on.");
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching add-ons:", error);
    throw error;
  }
}

/**
 * Purchase add-ons
 * @param accessToken User's access token
 * @param purchaseRequest Add-on purchase request
 * @returns Promise with invoice response
 */
export async function purchaseAddons(
  accessToken: string,
  purchaseRequest: AddonPurchaseRequest
): Promise<InvoiceResponse> {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/plans/addon`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(purchaseRequest),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || "Gagal membeli add-on.");
    }

    return await response.json();
  } catch (error) {
    console.error("Error purchasing add-ons:", error);
    throw error;
  }
}
