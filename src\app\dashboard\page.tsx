"use client";

import { AppSidebar } from "@/components/app-sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Bot, MessageCircle, History, Loader2, CheckCircle, Circle, ArrowRight, Sparkles, Target, Zap, Gift, Star } from "lucide-react";
// ThemeSwitcher sudah tersedia di AppSidebar
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import React, { useEffect, useState, useCallback } from "react";
import { useAuth } from "@/contexts/AuthContext";
import Link from 'next/link';

interface Assistant {
  id: string;
  name: string;
}

interface ChatItem {
  id: string;
}

interface ChatResponse {
  data: ChatItem[];
  totalItems: number;
}

interface WhatsAppInstance {
  id: string;
  name: string;
  status: string;
}

interface DashboardStats {
  assistants: number;
  whatsAppInstances: number;
  webchatWidgets: number;
  telegramInstances: number;
  chatHistories: number;
}

interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  maxAssistants: number;
  maxWhatsappInstances: number;
  maxMessages: number;
  maxStorageInMB: number;
}

interface Subscription {
  id: string;
  userId: string;
  plan: SubscriptionPlan;
  startDate: string;
  endDate: string;
  status: string;
  autoRenew: boolean;
}

export default function Page() {
  const { accessToken } = useAuth();
  const [stats, setStats] = useState<DashboardStats>({
    assistants: 0,
    whatsAppInstances: 0,
    webchatWidgets: 0,
    telegramInstances: 0,
    chatHistories: 0,
  });
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [isLoadingSubscription, setIsLoadingSubscription] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDashboardStats = useCallback(async () => {
    if (!accessToken) {
      setIsLoading(false);
      return;
    }
    setIsLoading(true);
    setError(null);

    try {
      // 1. Fetch Assistants
      const assistantsResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/assistants`, {
        headers: { 'Authorization': `Bearer ${accessToken}` },
      });
      if (!assistantsResponse.ok) throw new Error("Gagal mengambil data asisten.");
      const assistantsData: Assistant[] = await assistantsResponse.json();
      const numAssistants = assistantsData.length;

      // 2. Fetch Chat Histories (Iterative approach)
      let numChatHistories = 0;
      if (numAssistants > 0) {
        const chatCountPromises = assistantsData.map(assistant => 
          fetch(`${process.env.NEXT_PUBLIC_API_URL}/assistants/${assistant.id}/chats?limit=1`, {
            headers: { 'Authorization': `Bearer ${accessToken}` },
          }).then(async (res) => { 
            if (!res.ok) {
              let errorMsg = `Gagal mengambil data chat untuk asisten ${assistant.id}`;
              try {
                const errData = await res.json();
                errorMsg = errData.message || errorMsg;
              } catch (_e) { /* Ignore parsing error, use default */ }
              console.error(errorMsg);
              throw new Error(errorMsg); 
            }
            return res.json();
          })
        );
        try {
          const chatCountsResponses: ChatResponse[] = await Promise.all(chatCountPromises);
          numChatHistories = chatCountsResponses.reduce((sum, current) => sum + (current.totalItems || 0), 0);
        } catch (chatError: unknown) { 
          console.error("Sebagian data chat gagal dimuat: ", (chatError as Error).message);
          throw chatError; 
        }
      }

      // 3. Fetch WhatsApp Instances
      const whatsAppResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/whatsapp`, {
        headers: { 'Authorization': `Bearer ${accessToken}` },
      });
      if (!whatsAppResponse.ok) throw new Error("Gagal mengambil data instansi WhatsApp.");
      const whatsAppInstancesData: WhatsAppInstance[] = await whatsAppResponse.json();
      const numWhatsAppInstances = whatsAppInstancesData.length;

      // 4. Fetch Webchat Widgets
      const webchatResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/webchat`, {
        headers: { 'Authorization': `Bearer ${accessToken}` },
      });
      if (!webchatResponse.ok) throw new Error("Gagal mengambil data webchat widgets.");
      const webchatWidgetsData: any[] = await webchatResponse.json();
      const numWebchatWidgets = webchatWidgetsData.length;

      // 5. Fetch Telegram Instances
      const telegramResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/telegram`, {
        headers: { 'Authorization': `Bearer ${accessToken}` },
      });
      if (!telegramResponse.ok) throw new Error("Gagal mengambil data bot Telegram.");
      const telegramInstancesData: any[] = await telegramResponse.json();
      const numTelegramInstances = telegramInstancesData.length;

      setStats({
        assistants: numAssistants,
        whatsAppInstances: numWhatsAppInstances,
        webchatWidgets: numWebchatWidgets,
        telegramInstances: numTelegramInstances,
        chatHistories: numChatHistories
      });

    } catch (err: unknown) {
      setError((err as Error).message || "Gagal mengambil data dashboard.");
      setStats({ assistants: 0, whatsAppInstances: 0, webchatWidgets: 0, telegramInstances: 0, chatHistories: 0 });
    } finally {
      setIsLoading(false);
    }
  }, [accessToken]);

  // Fetch user's current subscription
  const fetchSubscription = useCallback(async () => {
    if (!accessToken) {
      setIsLoadingSubscription(false);
      return;
    }

    setIsLoadingSubscription(true);

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/plans/subscription`,
        {
          headers: { Authorization: `Bearer ${accessToken}` },
        }
      );

      if (response.status === 404) {
        // User doesn't have a subscription yet
        setSubscription(null);
        return;
      }

      if (!response.ok) {
        throw new Error("Gagal mengambil data langganan.");
      }

      const data: Subscription = await response.json();
      setSubscription(data);
    } catch (err: any) {
      console.error("Error fetching subscription:", err);
      // Don't set error state here to avoid showing error UI when just subscription fails
    } finally {
      setIsLoadingSubscription(false);
    }
  }, [accessToken]);

  useEffect(() => {
    fetchDashboardStats();
    fetchSubscription();
  }, [fetchDashboardStats, fetchSubscription]);

  if (isLoading) {
    return (
      <ProtectedRoute>
        <SidebarProvider>
          <AppSidebar />
          <SidebarInset>
            <div className="flex flex-1 items-center justify-center">
              <Loader2 className="h-10 w-10 animate-spin text-primary" />
            </div>
          </SidebarInset>
        </SidebarProvider>
      </ProtectedRoute>
    );
  }

  if (error) {
    return (
      <ProtectedRoute>
        <SidebarProvider>
          <AppSidebar />
          <SidebarInset>
            <div className="flex flex-1 items-center justify-center p-4">
              <Card className="w-full max-w-md">
                <CardHeader>
                  <CardTitle className="text-destructive">Error</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">Gagal memuat data dashboard:</p>
                  <p className="text-sm text-destructive">{error}</p>
                </CardContent>
                <CardFooter>
                  <Button onClick={fetchDashboardStats} className="w-full">
                    Coba Lagi
                  </Button>
                </CardFooter>
              </Card>
            </div>
          </SidebarInset>
        </SidebarProvider>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <header className="flex h-16 shrink-0 items-center gap-2">
            <div className="flex items-center gap-2 px-4">
              <SidebarTrigger className="-ml-1" />
              <Separator
                orientation="vertical"
                className="mr-2 data-[orientation=vertical]:h-4"
              />
              <Breadcrumb>
                <BreadcrumbList>
                  <BreadcrumbItem className="hidden md:block">
                    <BreadcrumbLink href="/dashboard">
                      Dashboard
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator className="hidden md:block" />
                  <BreadcrumbItem>
                    <BreadcrumbPage>Beranda</BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
            </div>
            {/* ThemeSwitcher dipindahkan ke AppSidebar */}
          </header>
          <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
            {/* Free Trial Banner - Show only if user has never had any subscription */}
            {!isLoadingSubscription && !subscription && (
              <Card className="border-primary/30 bg-gradient-to-r from-primary/10 via-primary/5 to-primary/10 shadow-sm">
                <CardContent className="p-6">
                  <div className="flex items-center gap-4">
                    <div className="h-12 w-12 rounded-full bg-primary/20 flex items-center justify-center flex-shrink-0">
                      <Gift className="h-6 w-6 text-primary" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="text-lg font-semibold text-primary">Mulai Free Trial Sekarang!</h3>
                        <Badge className="bg-primary/20 text-primary border-primary/30">
                          <Star className="h-3 w-3 mr-1" />
                          Gratis
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mb-3">
                        Coba semua fitur premium Heylo selama 7 hari tanpa biaya. Tidak perlu kartu kredit, langsung aktif!
                      </p>
                      <div className="flex flex-col sm:flex-row gap-3">
                        <Button asChild className="gap-2 shadow-sm">
                          <Link href="/dashboard/plans">
                            <Gift className="h-4 w-4" />
                            Mulai Free Trial
                            <ArrowRight className="h-4 w-4" />
                          </Link>
                        </Button>
                        <Button variant="outline" asChild className="gap-2">
                          <Link href="/dashboard/plans">
                            Lihat Semua Paket
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Asisten</CardTitle>
                  <Bot className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.assistants}</div>
                  <p className="text-xs text-muted-foreground">
                    {stats.assistants > 0 ? `${stats.assistants} asisten telah dibuat` : "Belum ada asisten yang dibuat"}
                  </p>
                </CardContent>
                <CardFooter>
                  <Button size="sm" className="w-full" asChild>
                    <Link href="/dashboard/assistants/new">Buat Asisten Baru</Link>
                  </Button>
                </CardFooter>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">WhatsApp</CardTitle>
                  <MessageCircle className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.whatsAppInstances}</div>
                  <p className="text-xs text-muted-foreground">
                    {stats.whatsAppInstances > 0 ? `${stats.whatsAppInstances} nomor terhubung` : "Belum ada koneksi WhatsApp"}
                  </p>
                </CardContent>
                <CardFooter>
                  <Button size="sm" className="w-full" asChild>
                    <Link href="/dashboard/whatsapp/new">Hubungkan WhatsApp</Link>
                  </Button>
                </CardFooter>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Telegram</CardTitle>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4 text-muted-foreground">
                    <path d="m22 2-7 20-4-9-9-4Z"/>
                    <path d="M22 2 11 13"/>
                  </svg>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.telegramInstances}</div>
                  <p className="text-xs text-muted-foreground">
                    {stats.telegramInstances > 0 ? `${stats.telegramInstances} bot terhubung` : "Belum ada bot Telegram"}
                  </p>
                </CardContent>
                <CardFooter>
                  <Button size="sm" className="w-full" asChild>
                    <Link href="/dashboard/telegram/new">Buat Bot Telegram</Link>
                  </Button>
                </CardFooter>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Web Widget</CardTitle>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4 text-muted-foreground">
                    <rect width="18" height="11" x="3" y="11" rx="2" ry="2"/>
                    <path d="m7 11 2-7 2 7 2-7 2 7"/>
                  </svg>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.webchatWidgets}</div>
                  <p className="text-xs text-muted-foreground">
                    {stats.webchatWidgets > 0 ? `${stats.webchatWidgets} widget aktif` : "Belum ada widget webchat"}
                  </p>
                </CardContent>
                <CardFooter>
                  <Button size="sm" className="w-full" asChild>
                    <Link href="/dashboard/webchat/new">Buat Widget</Link>
                  </Button>
                </CardFooter>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Chat History</CardTitle>
                  <History className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.chatHistories}</div>
                  <p className="text-xs text-muted-foreground">
                    {stats.chatHistories > 0 ? `Total ${stats.chatHistories} riwayat percakapan` : "Belum ada riwayat chat"}
                  </p>
                </CardContent>
                <CardFooter>
                  <Button size="sm" className="w-full" asChild>
                    <Link href="/dashboard/whatsapp/chats">Lihat History</Link>
                  </Button>
                </CardFooter>
              </Card>
            </div>
            
            <Card className="col-span-3 overflow-hidden relative">
              {/* Background decoration */}
              <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-primary/5 to-transparent rounded-full -translate-y-16 translate-x-16"></div>
              <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-primary/5 to-transparent rounded-full translate-y-12 -translate-x-12"></div>

              <CardHeader className="pb-4 relative z-10">
                <div className="flex items-center gap-3 mb-2">
                  <div className="h-10 w-10 rounded-full bg-gradient-to-br from-primary to-primary/80 flex items-center justify-center">
                    <Sparkles className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-2xl font-bold">Selamat Datang di Heylo</CardTitle>
                    <CardDescription className="text-base mt-1">
                      Platform untuk membuat asisten AI dan menghubungkannya dengan WhatsApp, Telegram, atau website Anda
                    </CardDescription>
                  </div>
                </div>

                {/* Progress Overview */}
                <div className="mt-4 p-4 rounded-lg bg-primary/5 border border-primary/10">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Progress Setup Anda</span>
                    <Badge variant="outline" className="bg-primary/10">
                      {Math.round(((stats.assistants > 0 ? 1 : 0) + ((stats.whatsAppInstances > 0 || stats.webchatWidgets > 0 || stats.telegramInstances > 0) ? 1 : 0)) / 2 * 100)}% Selesai
                    </Badge>
                  </div>
                  <Progress
                    value={((stats.assistants > 0 ? 1 : 0) + ((stats.whatsAppInstances > 0 || stats.webchatWidgets > 0 || stats.telegramInstances > 0) ? 1 : 0)) / 2 * 100}
                    className="h-2"
                  />
                  <p className="text-xs text-muted-foreground mt-2">
                    {stats.assistants === 0 && stats.whatsAppInstances === 0 && stats.webchatWidgets === 0 && stats.telegramInstances === 0
                      ? "Mari mulai dengan membuat asisten AI pertama Anda!"
                      : stats.assistants > 0 && stats.whatsAppInstances === 0 && stats.webchatWidgets === 0 && stats.telegramInstances === 0
                      ? "Bagus! Sekarang pilih channel komunikasi (WhatsApp, Telegram, atau Web Widget)."
                      : stats.assistants === 0 && (stats.whatsAppInstances > 0 || stats.webchatWidgets > 0 || stats.telegramInstances > 0)
                      ? "Channel sudah terhubung! Buat asisten AI untuk melengkapi setup."
                      : "Setup lengkap! Asisten AI Anda siap menjawab pertanyaan."
                    }
                  </p>
                </div>
              </CardHeader>

              <CardContent className="pt-2 pb-8 relative z-10">
                {/* Enhanced Timeline */}
                <div className="relative mb-8">
                  {/* Timeline line */}
                  <div className="absolute left-0 right-0 top-[45px] h-0.5 bg-gradient-to-r from-primary/20 via-primary/40 to-primary/20"></div>

                  {/* Timeline steps */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 relative">
                    {/* Step 1 */}
                    <div className="flex flex-col items-center">
                      <div className={`flex h-12 w-12 items-center justify-center rounded-full border-2 shadow-lg mb-4 z-10 transition-all ${
                        stats.assistants > 0
                          ? 'bg-primary border-primary text-white'
                          : 'bg-card border-primary/30 text-primary hover:border-primary/50'
                      }`}>
                        {stats.assistants > 0 ? (
                          <CheckCircle className="h-5 w-5" />
                        ) : (
                          <span className="text-base font-semibold">1</span>
                        )}
                      </div>
                      <h3 className="text-sm font-medium text-center mb-1">Buat Asisten AI</h3>
                      <Badge variant={stats.assistants > 0 ? "default" : "outline"} className="text-xs">
                        {stats.assistants > 0 ? "Selesai" : "Belum"}
                      </Badge>
                    </div>

                    {/* Step 2 */}
                    <div className="flex flex-col items-center">
                      <div className={`flex h-12 w-12 items-center justify-center rounded-full border-2 shadow-lg mb-4 z-10 transition-all ${
                        stats.assistants > 0
                          ? 'bg-primary/10 border-primary/50 text-primary'
                          : 'bg-card border-muted text-muted-foreground'
                      }`}>
                        <span className="text-base font-semibold">2</span>
                      </div>
                      <h3 className="text-sm font-medium text-center mb-1">Upload Knowledge Base</h3>
                      <Badge variant="outline" className="text-xs">
                        Opsional
                      </Badge>
                    </div>

                    {/* Step 3 */}
                    <div className="flex flex-col items-center">
                      <div className={`flex h-12 w-12 items-center justify-center rounded-full border-2 shadow-lg mb-4 z-10 transition-all ${
                        stats.whatsAppInstances > 0 || stats.webchatWidgets > 0 || stats.telegramInstances > 0
                          ? 'bg-primary border-primary text-white'
                          : stats.assistants > 0
                          ? 'bg-card border-primary/30 text-primary hover:border-primary/50'
                          : 'bg-card border-muted text-muted-foreground'
                      }`}>
                        {stats.whatsAppInstances > 0 || stats.webchatWidgets > 0 || stats.telegramInstances > 0 ? (
                          <CheckCircle className="h-5 w-5" />
                        ) : (
                          <span className="text-base font-semibold">3</span>
                        )}
                      </div>
                      <h3 className="text-sm font-medium text-center mb-1">Pilih Channel</h3>
                      <Badge variant={stats.whatsAppInstances > 0 || stats.webchatWidgets > 0 || stats.telegramInstances > 0 ? "default" : "outline"} className="text-xs">
                        {stats.whatsAppInstances > 0 || stats.webchatWidgets > 0 || stats.telegramInstances > 0 ? "Terhubung" : "Belum"}
                      </Badge>
                    </div>

                    {/* Step 4 */}
                    <div className="flex flex-col items-center">
                      <div className={`flex h-12 w-12 items-center justify-center rounded-full border-2 shadow-lg mb-4 z-10 transition-all ${
                        stats.assistants > 0 && (stats.whatsAppInstances > 0 || stats.webchatWidgets > 0 || stats.telegramInstances > 0)
                          ? 'bg-gradient-to-br from-primary to-primary/80 border-primary text-white'
                          : 'bg-card border-muted text-muted-foreground'
                      }`}>
                        {stats.assistants > 0 && (stats.whatsAppInstances > 0 || stats.webchatWidgets > 0 || stats.telegramInstances > 0) ? (
                          <Zap className="h-5 w-5" />
                        ) : (
                          <span className="text-base font-semibold">4</span>
                        )}
                      </div>
                      <h3 className="text-sm font-medium text-center mb-1">Mulai Berinteraksi</h3>
                      <Badge variant={stats.assistants > 0 && (stats.whatsAppInstances > 0 || stats.webchatWidgets > 0 || stats.telegramInstances > 0) ? "default" : "outline"} className="text-xs">
                        {stats.assistants > 0 && (stats.whatsAppInstances > 0 || stats.webchatWidgets > 0 || stats.telegramInstances > 0) ? "Siap" : "Menunggu"}
                      </Badge>
                    </div>
                  </div>
                </div>
                
                {/* Quick Actions */}
                {stats.assistants === 0 || (stats.whatsAppInstances === 0 && stats.webchatWidgets === 0 && stats.telegramInstances === 0) ? (
                  <div className="mb-6 p-4 rounded-xl bg-gradient-to-r from-primary/10 via-primary/5 to-primary/10 border border-primary/20">
                    <div className="flex items-center gap-2 mb-3">
                      <Target className="h-5 w-5 text-primary" />
                      <h3 className="font-semibold text-primary">Langkah Selanjutnya</h3>
                    </div>
                    <div className="flex flex-col sm:flex-row gap-3">
                      {stats.assistants === 0 && (
                        <Button asChild className="flex-1 gap-2 shadow-sm">
                          <Link href="/dashboard/assistants/new">
                            <Bot className="h-4 w-4" />
                            Buat Asisten AI Pertama
                            <ArrowRight className="h-4 w-4 ml-auto" />
                          </Link>
                        </Button>
                      )}
                      {stats.assistants > 0 && stats.whatsAppInstances === 0 && stats.webchatWidgets === 0 && stats.telegramInstances === 0 && (
                        <>
                          <Button asChild className="flex-1 gap-2 shadow-sm">
                            <Link href="/dashboard/whatsapp/new">
                              <MessageCircle className="h-4 w-4" />
                              Hubungkan WhatsApp
                              <ArrowRight className="h-4 w-4 ml-auto" />
                            </Link>
                          </Button>
                          <Button asChild variant="outline" className="flex-1 gap-2">
                            <Link href="/dashboard/telegram/new">
                              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                                <path d="m22 2-7 20-4-9-9-4Z"/>
                                <path d="M22 2 11 13"/>
                              </svg>
                              Buat Bot Telegram
                              <ArrowRight className="h-4 w-4 ml-auto" />
                            </Link>
                          </Button>
                          <Button asChild variant="outline" className="flex-1 gap-2">
                            <Link href="/dashboard/webchat/new">
                              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                                <rect width="18" height="11" x="3" y="11" rx="2" ry="2"/>
                                <path d="m7 11 2-7 2 7 2-7 2 7"/>
                              </svg>
                              Buat Web Widget
                              <ArrowRight className="h-4 w-4 ml-auto" />
                            </Link>
                          </Button>
                        </>
                      )}
                      <Button variant="outline" asChild className="flex-1 gap-2">
                        <Link href="/dashboard/playground">
                          <Zap className="h-4 w-4" />
                          Coba Playground AI
                        </Link>
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="mb-6 p-4 rounded-xl bg-primary/5 border border-primary/20">
                    <div className="flex items-center gap-2 mb-3">
                      <CheckCircle className="h-5 w-5 text-primary" />
                      <h3 className="font-semibold text-primary">Setup Selesai!</h3>
                    </div>
                    <p className="text-sm text-muted-foreground mb-3">
                      Asisten AI Anda sudah siap melayani pelanggan melalui {
                        [
                          stats.whatsAppInstances > 0 && 'WhatsApp',
                          stats.webchatWidgets > 0 && 'Web Widget',
                          stats.telegramInstances > 0 && 'Telegram'
                        ].filter(Boolean).join(', ').replace(/, ([^,]*)$/, ' dan $1') || 'channel yang terhubung'
                      }. Mulai pantau percakapan dan analisis performa.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-3">
                      <Button variant="outline" asChild className="flex-1 gap-2">
                        <Link href="/dashboard/assistants">
                          <Bot className="h-4 w-4" />
                          Kelola Asisten
                        </Link>
                      </Button>
                    </div>
                  </div>
                )}

                {/* Enhanced Step Details */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Step 1 Detail */}
                  <div className={`rounded-xl border p-6 shadow-sm transition-all hover:shadow-md ${
                    stats.assistants > 0
                      ? 'border-primary/30 bg-primary/5'
                      : 'border-border/60 bg-card/50 hover:border-primary/20'
                  }`}>
                    <div className="flex items-center gap-3 mb-4">
                      <div className={`h-10 w-10 rounded-full flex items-center justify-center ${
                        stats.assistants > 0
                          ? 'bg-primary/20'
                          : 'bg-primary/10'
                      }`}>
                        {stats.assistants > 0 ? (
                          <CheckCircle className="h-5 w-5 text-primary" />
                        ) : (
                          <Bot className="h-5 w-5 text-primary" />
                        )}
                      </div>
                      <div>
                        <h3 className="font-semibold text-base">Buat Asisten AI</h3>
                        <Badge variant={stats.assistants > 0 ? "default" : "outline"} className="mt-1">
                          {stats.assistants > 0 ? `${stats.assistants} Asisten` : "Belum Ada"}
                        </Badge>
                      </div>
                    </div>
                    <p className="text-sm text-muted-foreground mb-4">
                      Buat asisten AI dengan nama, deskripsi, dan instruksi sesuai kebutuhan Anda.
                      Berikan instruksi yang jelas dan spesifik untuk hasil terbaik.
                    </p>
                    {stats.assistants === 0 && (
                      <Button variant="outline" size="sm" asChild className="w-full gap-2">
                        <Link href="/dashboard/assistants/new">
                          <Bot className="h-4 w-4" />
                          Buat Asisten Pertama
                        </Link>
                      </Button>
                    )}
                  </div>

                  {/* Step 2 Detail */}
                  <div className="rounded-xl border border-border/60 bg-card/50 p-6 shadow-sm transition-all hover:shadow-md hover:border-primary/20">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 text-primary">
                          <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
                          <polyline points="14 2 14 8 20 8" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="font-semibold text-base">Upload Knowledge Base</h3>
                        <Badge variant="outline" className="mt-1">Opsional</Badge>
                      </div>
                    </div>
                    <p className="text-sm text-muted-foreground mb-4">
                      Tambahkan dokumen pengetahuan untuk meningkatkan kemampuan asisten Anda.
                      Asisten dapat menggunakan informasi dari dokumen untuk memberikan jawaban yang lebih akurat.
                    </p>
                    <Button variant="outline" size="sm" asChild className="w-full gap-2">
                      <Link href="/dashboard/knowledge">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                          <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
                          <polyline points="14 2 14 8 20 8" />
                        </svg>
                        Kelola Knowledge Base
                      </Link>
                    </Button>
                  </div>

                  {/* Step 3 Detail - WhatsApp */}
                  <div className={`rounded-xl border p-6 shadow-sm transition-all hover:shadow-md ${
                    stats.whatsAppInstances > 0
                      ? 'border-primary/30 bg-primary/5'
                      : 'border-border/60 bg-card/50 hover:border-primary/20'
                  }`}>
                    <div className="flex items-center gap-3 mb-4">
                      <div className={`h-10 w-10 rounded-full flex items-center justify-center ${
                        stats.whatsAppInstances > 0
                          ? 'bg-primary/20'
                          : 'bg-primary/10'
                      }`}>
                        {stats.whatsAppInstances > 0 ? (
                          <CheckCircle className="h-5 w-5 text-primary" />
                        ) : (
                          <MessageCircle className="h-5 w-5 text-primary" />
                        )}
                      </div>
                      <div>
                        <h3 className="font-semibold text-base">Channel: WhatsApp</h3>
                        <Badge variant={stats.whatsAppInstances > 0 ? "default" : "outline"} className="mt-1">
                          {stats.whatsAppInstances > 0 ? `${stats.whatsAppInstances} Terhubung` : "Belum Terhubung"}
                        </Badge>
                      </div>
                    </div>
                    <p className="text-sm text-muted-foreground mb-4">
                      Scan QR code untuk menghubungkan asisten dengan nomor WhatsApp Anda.
                      Setelah terhubung, asisten Anda siap menerima dan menjawab pesan WhatsApp.
                    </p>
                    {stats.whatsAppInstances === 0 && (
                      <Button variant="outline" size="sm" asChild className="w-full gap-2">
                        <Link href="/dashboard/whatsapp/new">
                          <MessageCircle className="h-4 w-4" />
                          Hubungkan WhatsApp
                        </Link>
                      </Button>
                    )}
                  </div>

                  {/* Step 3 Detail - Telegram */}
                  <div className={`rounded-xl border p-6 shadow-sm transition-all hover:shadow-md ${
                    stats.telegramInstances > 0
                      ? 'border-primary/30 bg-primary/5'
                      : 'border-border/60 bg-card/50 hover:border-primary/20'
                  }`}>
                    <div className="flex items-center gap-3 mb-4">
                      <div className={`h-10 w-10 rounded-full flex items-center justify-center ${
                        stats.telegramInstances > 0
                          ? 'bg-primary/20'
                          : 'bg-primary/10'
                      }`}>
                        {stats.telegramInstances > 0 ? (
                          <CheckCircle className="h-5 w-5 text-primary" />
                        ) : (
                          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 text-primary">
                            <path d="m22 2-7 20-4-9-9-4Z"/>
                            <path d="M22 2 11 13"/>
                          </svg>
                        )}
                      </div>
                      <div>
                        <h3 className="font-semibold text-base">Channel: Telegram</h3>
                        <Badge variant={stats.telegramInstances > 0 ? "default" : "outline"} className="mt-1">
                          {stats.telegramInstances > 0 ? `${stats.telegramInstances} Bot Aktif` : "Belum Ada Bot"}
                        </Badge>
                      </div>
                    </div>
                    <p className="text-sm text-muted-foreground mb-4">
                      Buat bot Telegram menggunakan BotFather dan hubungkan dengan asisten AI Anda.
                      Bot akan siap menerima dan menjawab pesan di platform Telegram.
                    </p>
                    {stats.telegramInstances === 0 && (
                      <Button variant="outline" size="sm" asChild className="w-full gap-2">
                        <Link href="/dashboard/telegram/new">
                          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                            <path d="m22 2-7 20-4-9-9-4Z"/>
                            <path d="M22 2 11 13"/>
                          </svg>
                          Buat Bot Telegram
                        </Link>
                      </Button>
                    )}
                  </div>

                  {/* Step 3 Detail - Webchat */}
                  <div className={`rounded-xl border p-6 shadow-sm transition-all hover:shadow-md ${
                    stats.webchatWidgets > 0
                      ? 'border-primary/30 bg-primary/5'
                      : 'border-border/60 bg-card/50 hover:border-primary/20'
                  }`}>
                    <div className="flex items-center gap-3 mb-4">
                      <div className={`h-10 w-10 rounded-full flex items-center justify-center ${
                        stats.webchatWidgets > 0
                          ? 'bg-primary/20'
                          : 'bg-primary/10'
                      }`}>
                        {stats.webchatWidgets > 0 ? (
                          <CheckCircle className="h-5 w-5 text-primary" />
                        ) : (
                          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 text-primary">
                            <rect width="18" height="11" x="3" y="11" rx="2" ry="2"/>
                            <path d="m7 11 2-7 2 7 2-7 2 7"/>
                          </svg>
                        )}
                      </div>
                      <div>
                        <h3 className="font-semibold text-base">Channel: Web Widget</h3>
                        <Badge variant={stats.webchatWidgets > 0 ? "default" : "outline"} className="mt-1">
                          {stats.webchatWidgets > 0 ? `${stats.webchatWidgets} Widget Aktif` : "Belum Ada Widget"}
                        </Badge>
                      </div>
                    </div>
                    <p className="text-sm text-muted-foreground mb-4">
                      Buat widget chat untuk website Anda. Pengunjung dapat langsung berinteraksi
                      dengan asisten AI melalui widget chat yang terintegrasi di website.
                    </p>
                    {stats.webchatWidgets === 0 && (
                      <Button variant="outline" size="sm" asChild className="w-full gap-2">
                        <Link href="/dashboard/webchat/new">
                          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                            <rect width="18" height="11" x="3" y="11" rx="2" ry="2"/>
                            <path d="m7 11 2-7 2 7 2-7 2 7"/>
                          </svg>
                          Buat Web Widget
                        </Link>
                      </Button>
                    )}
                  </div>

                  {/* Step 4 Detail */}
                  <div className={`rounded-xl border p-6 shadow-sm transition-all hover:shadow-md ${
                    stats.assistants > 0 && (stats.whatsAppInstances > 0 || stats.webchatWidgets > 0 || stats.telegramInstances > 0)
                      ? 'border-primary/30 bg-primary/5'
                      : 'border-border/60 bg-card/50 hover:border-primary/20'
                  }`}>
                    <div className="flex items-center gap-3 mb-4">
                      <div className={`h-10 w-10 rounded-full flex items-center justify-center ${
                        stats.assistants > 0 && (stats.whatsAppInstances > 0 || stats.webchatWidgets > 0 || stats.telegramInstances > 0)
                          ? 'bg-primary/20'
                          : 'bg-primary/10'
                      }`}>
                        {stats.assistants > 0 && (stats.whatsAppInstances > 0 || stats.webchatWidgets > 0 || stats.telegramInstances > 0) ? (
                          <Zap className="h-5 w-5 text-primary" />
                        ) : (
                          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 text-primary">
                            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
                          </svg>
                        )}
                      </div>
                      <div>
                        <h3 className="font-semibold text-base">Mulai Berinteraksi</h3>
                        <Badge variant={stats.assistants > 0 && (stats.whatsAppInstances > 0 || stats.webchatWidgets > 0 || stats.telegramInstances > 0) ? "default" : "outline"} className="mt-1">
                          {stats.assistants > 0 && (stats.whatsAppInstances > 0 || stats.webchatWidgets > 0 || stats.telegramInstances > 0) ? "Siap Digunakan" : "Menunggu Setup"}
                        </Badge>
                      </div>
                    </div>
                    <p className="text-sm text-muted-foreground mb-4">
                      Asisten Anda siap membantu menjawab pertanyaan melalui {
                        [
                          stats.whatsAppInstances > 0 && 'WhatsApp',
                          stats.webchatWidgets > 0 && 'Web Widget',
                          stats.telegramInstances > 0 && 'Telegram'
                        ].filter(Boolean).join(', ').replace(/, ([^,]*)$/, ' dan $1') || 'channel yang terhubung'
                      }.
                      Pantau percakapan dan analisis performa asisten Anda melalui dashboard.
                    </p>

                  </div>
                </div>

                {/* Enhanced Tips section */}
                <div className="mt-8 rounded-xl border border-primary/20 bg-gradient-to-br from-primary/5 via-primary/3 to-transparent p-6">
                  <div className="flex items-start gap-4">
                    <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 text-primary">
                        <circle cx="12" cy="12" r="10" />
                        <path d="M12 16v-4" />
                        <path d="M12 8h.01" />
                      </svg>
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-primary mb-2">💡 Tips untuk Hasil Terbaik</h3>
                      <div className="space-y-2 text-sm text-muted-foreground">
                        <p>• <strong>Instruksi yang jelas:</strong> Berikan instruksi yang spesifik dan detail pada asisten Anda</p>
                        <p>• <strong>Knowledge Base:</strong> Upload dokumen relevan untuk meningkatkan akurasi jawaban</p>
                        <p>• <strong>Pilih channel yang tepat:</strong> WhatsApp untuk komunikasi personal, Telegram untuk komunitas, Web Widget untuk website</p>
                        <p>• <strong>Monitor performa:</strong> Pantau percakapan secara berkala untuk evaluasi dan perbaikan</p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
              

            </Card>
          </div>
        </SidebarInset>
      </SidebarProvider>
    </ProtectedRoute>
  );
}
