"use client";

import { AppSidebar } from "@/components/app-sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useAuth } from "@/contexts/AuthContext";
import Link from 'next/link';
import React, { useEffect, useState, useCallback } from "react";
import { Loader2, FileText, AlertCircle, Bot, Plus, BookOpen } from 'lucide-react';
import { But<PERSON> } from "@/components/ui/button";

interface Assistant {
  id: string;
  name: string;
  // other assistant props if needed
}

interface AssistantFile {
  id: string;
  name: string;
  size: number;
  createdAt: string;
  // other file props if needed
}

interface EnrichedFile extends AssistantFile {
  assistantId: string;
  assistantName: string;
}

export default function KnowledgeBasePage() {
  const { accessToken } = useAuth();
  const [enrichedFiles, setEnrichedFiles] = useState<EnrichedFile[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchAllFilesData = useCallback(async () => {
    if (!accessToken) return;

    setIsLoading(true);
    setError(null);
    setEnrichedFiles([]);

    try {
      // 1. Fetch all assistants
      const assistantsResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/assistants`, {
        headers: { 'Authorization': `Bearer ${accessToken}` },
      });

      if (!assistantsResponse.ok) {
        throw new Error(`Gagal mengambil data asisten: ${assistantsResponse.statusText}`);
      }
      const assistants: Assistant[] = await assistantsResponse.json();

      if (assistants.length === 0) {
        setIsLoading(false);
        return; // No assistants, so no files to show
      }

      // 2. For each assistant, fetch its files and enrich them
      const allFilesPromises = assistants.map(async (assistant) => {
        const filesResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/assistants/${assistant.id}/files`, {
          headers: { 'Authorization': `Bearer ${accessToken}` },
        });
        if (!filesResponse.ok) {
          console.error(`Gagal mengambil file untuk asisten ${assistant.name} (${assistant.id})`);
          return []; // Return empty for this assistant on error, or handle error more globally
        }
        const files: AssistantFile[] = await filesResponse.json();
        return files.map(file => ({ 
          ...file, 
          assistantId: assistant.id, 
          assistantName: assistant.name 
        }));
      });

      const filesByAssistant = await Promise.all(allFilesPromises);
      const flatFiles = filesByAssistant.flat(); // Flatten the array of arrays
      setEnrichedFiles(flatFiles);

    } catch (err: any) {
      setError(err.message || "Terjadi kesalahan tak terduga saat mengambil data basis pengetahuan.");
    } finally {
      setIsLoading(false);
    }
  }, [accessToken]);

  useEffect(() => {
    fetchAllFilesData();
  }, [fetchAllFilesData]);

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>Basis Pengetahuan</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>
        <main className="flex-1 p-4 md:p-6 space-y-6">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h2 className="text-2xl font-bold tracking-tight">Basis Pengetahuan</h2>
              <p className="text-muted-foreground">
                Lihat semua file yang diunggah ke asisten Anda
              </p>
            </div>
          </div>
          
          <Card>
            <CardContent>
              {isLoading ? (
                <div className="flex flex-col items-center justify-center py-20">
                  <div className="relative">
                    <div className="w-16 h-16 rounded-full bg-primary animate-pulse"></div>
                    <BookOpen className="absolute inset-0 m-auto h-8 w-8 text-primary-foreground animate-bounce" />
                  </div>
                  <p className="mt-6 text-lg font-medium text-muted-foreground">Memuat basis pengetahuan...</p>
                </div>
              ) : error ? (
                <div className="flex flex-col items-center justify-center py-20">
                  <div className="p-4 rounded-full bg-destructive/10 mb-4">
                    <AlertCircle className="h-8 w-8 text-destructive" />
                  </div>
                  <h3 className="text-lg font-semibold text-destructive mb-2">Terjadi Kesalahan</h3>
                  <p className="text-muted-foreground text-center mb-6">{error}</p>
                  <Button onClick={fetchAllFilesData} variant="outline">
                    Coba Lagi
                  </Button>
                </div>
              ) : enrichedFiles.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-20">
                  <div className="relative mb-8">
                    <div className="w-24 h-24 rounded-3xl bg-primary/10 dark:bg-primary/20 flex items-center justify-center">
                      <BookOpen className="h-12 w-12 text-primary" />
                    </div>
                    <div className="absolute -top-2 -right-2 w-8 h-8 rounded-full bg-primary flex items-center justify-center">
                      <FileText className="h-4 w-4 text-primary-foreground" />
                    </div>
                  </div>
                  <h3 className="text-2xl font-bold mb-3">Basis Pengetahuan Kosong</h3>
                  <p className="text-muted-foreground text-center max-w-md mb-8">
                    Unggah file ke asisten AI Anda untuk memperkaya basis pengetahuan. File-file ini akan membantu asisten memberikan jawaban yang lebih akurat.
                  </p>
                  <Button asChild size="lg" className="bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg">
                    <Link href="/dashboard/assistants/new">
                      <Bot className="mr-2 h-5 w-5" />
                      Buat Asisten Baru
                    </Link>
                  </Button>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nama File</TableHead>
                      <TableHead className="hidden sm:table-cell">Ukuran</TableHead>
                      <TableHead className="hidden md:table-cell">Diunggah</TableHead>
                      <TableHead>Asisten Terkait</TableHead>
                      <TableHead className="text-right">Tindakan</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {enrichedFiles.map((file) => (
                      <TableRow key={`${file.assistantId}-${file.id}`}>
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-2">
                            <FileText className="h-4 w-4 text-muted-foreground" />
                            {file.name}
                          </div>
                        </TableCell>
                        <TableCell className="hidden sm:table-cell">{(file.size / 1024).toFixed(2)} KB</TableCell>
                        <TableCell className="hidden md:table-cell">{new Date(file.createdAt).toLocaleDateString()}</TableCell>
                        <TableCell>{file.assistantName}</TableCell>
                        <TableCell className="text-right">
                          <Button variant="outline" size="sm" asChild>
                            <Link href={`/dashboard/assistants/${file.assistantId}/edit`}>
                              Edit Asisten
                            </Link>
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </main>
      </SidebarInset>
    </SidebarProvider>
  );
}
