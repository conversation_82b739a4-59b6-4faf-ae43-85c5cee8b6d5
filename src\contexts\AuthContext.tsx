"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useRouter } from 'next/navigation'; // Using next/navigation for App Router

// Define User type based on API response
interface User {
  id: string;
  name: string;
  email: string;
  phoneNumber?: string; // Optional based on typical profile data
  imageUrl?: string;    // Optional
  status?: string;      // Optional
}

interface AuthContextType {
  accessToken: string | null;
  user: User | null; 
  isLoading: boolean;
  login: (token: string) => Promise<void>;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [user, setUser] = useState<User | null>(null); 
  const [isLoading, setIsLoading] = useState(true); // Start with loading true
  const router = useRouter();

  // Function to fetch user profile
  const fetchUserProfile = async (token: string) => {
    try {
      const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/auth/profile`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (res.ok) {
        const userData: User = await res.json();
        setUser(userData);
      } else {
        // Token might be invalid/expired, or other server error
        console.error("Failed to fetch profile, logging out:", res.status);
        logout(); // Clear session
      }
    } catch (error) {
      console.error("Error fetching profile:", error);
      logout(); // Clear session on network error etc.
    }
  };

  useEffect(() => {
    const initializeAuth = async () => {
      const token = localStorage.getItem('accessToken');
      if (token) {
        setAccessToken(token);
        await fetchUserProfile(token); // Fetch profile if token exists
      } else {
        setUser(null); // Ensure user is null if no token
      }
      setIsLoading(false);
    };
    initializeAuth();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Removed router from deps to prevent re-runs on route change, logout handles redirect

  const login = async (token: string) => {
    localStorage.setItem('accessToken', token);
    setAccessToken(token);
    setIsLoading(true); // Set loading true while fetching profile
    await fetchUserProfile(token); // Fetch user profile after login
    setIsLoading(false);
    // Redirect only if user is successfully set (fetchUserProfile doesn't logout)
    // If fetchUserProfile calls logout, it will handle the redirect.
    // Check if still mounted or if user is set before pushing.
    // For now, AuthContext's fetchUserProfile handles logout and redirect on failure.
    if (localStorage.getItem('accessToken')) { // Check if token still exists (not cleared by failed profile fetch)
        router.push('/dashboard');
    }
  };

  const logout = () => {
    localStorage.removeItem('accessToken');
    setAccessToken(null);
    setUser(null);
    router.push('/login');
  };

  return (
    <AuthContext.Provider value={{ accessToken, user, isLoading, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};