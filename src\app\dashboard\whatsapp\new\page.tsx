"use client";

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { AppSidebar } from '@/components/app-sidebar';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { SidebarInset, SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar';
import { ArrowLeft, Loader2, AlertTriangle, Save, ArrowRight } from 'lucide-react';
import { toast } from 'sonner';

interface NewWhatsAppInstanceResponse {
  id: string;
  name: string;
  phoneNumber: string;
  userId: string;
  status: string;
  assistantId: string | null;
  enabled: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function NewWhatsAppInstancePage() {
  const { accessToken } = useAuth();
  const router = useRouter();
  const [name, setName] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isLimitExceeded, setIsLimitExceeded] = useState(false);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!accessToken) {
      setError("Autentikasi diperlukan.");
      toast.error("Autentikasi diperlukan.");
      return;
    }
    if (!name.trim() || !phoneNumber.trim()) {
      setError("Nama WhatsApp dan nomor telepon tidak boleh kosong.");
      toast.error("Nama WhatsApp dan nomor telepon tidak boleh kosong.");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/whatsapp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify({ name, phoneNumber }),
      });

      const responseData = await response.json();

      if (!response.ok) {
        // Check if this is a limit exceeded error
        if (responseData.message && responseData.message.includes('exceeded the maximum number of whatsapp instances')) {
          setIsLimitExceeded(true);
          return;
        }
        
        // Check if this is a subscription not found error
        if (responseData.message && (responseData.message.includes('Subscription not found') || responseData.message.includes('subscription'))) {
          setError('subscription_required');
          return;
        }
        
        throw new Error(responseData.message || 'Gagal membuat WhatsApp baru.');
      }
      
      // Store success message in sessionStorage to show after redirect
      sessionStorage.setItem('whatsappToast', `WhatsApp "${responseData.name}" berhasil dibuat!`);
      
      // Redirect to the WhatsApp instances list page
      router.push('/dashboard/whatsapp');
    } catch (err: any) {
      setError(err.message);
      toast.error(`Gagal membuat WhatsApp: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="/dashboard/whatsapp">Pengaturan WhatsApp</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>Buat WhatsApp Baru</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>

        <main className="flex-1 p-4 md:p-6">
          <div className="mb-6">
            <Link href="/dashboard/whatsapp" passHref>
              <Button variant="outline" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" /> Kembali ke Daftar WhatsApp
              </Button>
            </Link>
          </div>

          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle>Buat WhatsApp Baru</CardTitle>
              <CardDescription>
                Isi detail di bawah ini untuk membuat WhatsApp baru.
              </CardDescription>
            </CardHeader>
            <form onSubmit={handleSubmit}>
              <CardContent className="space-y-6 pb-8">
                {error && error !== 'subscription_required' && (
                  <div className="flex items-center gap-2 rounded-md border border-destructive bg-destructive/10 p-3 text-sm text-destructive mb-4">
                    <AlertTriangle className="h-5 w-5 flex-shrink-0" />
                    <p>{error}</p>
                  </div>
                )}

                {error === 'subscription_required' && (
                  <div className="rounded-md border border-primary bg-primary/5 p-4 text-sm mb-4">
                    <div className="flex items-center gap-2 mb-2">
                      <AlertTriangle className="h-5 w-5 text-primary flex-shrink-0" />
                      <p className="font-medium text-primary">Langganan Diperlukan</p>
                    </div>
                    <p className="text-muted-foreground mb-3">
                      Untuk membuat instance WhatsApp, Anda memerlukan paket langganan aktif. Silakan pilih paket langganan yang sesuai dengan kebutuhan Anda untuk mulai mengintegrasikan WhatsApp dengan asisten AI Anda.
                    </p>
                    <Button 
                      variant="default"
                      onClick={() => router.push('/dashboard/plans')}
                      type="button"
                    >
                      <ArrowRight className="mr-2 h-4 w-4" />
                      Lihat Paket Langganan
                    </Button>
                  </div>
                )}

                {isLimitExceeded && (
                  <div className="rounded-md border border-amber-500 bg-amber-50 p-4 text-sm mb-4">
                    <div className="flex items-center gap-2 mb-2">
                      <AlertTriangle className="h-5 w-5 text-amber-600 flex-shrink-0" />
                      <p className="font-medium text-amber-800">Batas WhatsApp Tercapai</p>
                    </div>
                    <p className="text-amber-700 mb-3">
                      Anda telah mencapai batas maksimum jumlah WhatsApp yang dapat dibuat dengan paket langganan Anda saat ini.
                      Untuk membuat WhatsApp baru, Anda perlu meningkatkan paket langganan Anda.
                    </p>
                    <Button 
                      variant="default" 
                      className="bg-amber-600 hover:bg-amber-700 text-white"
                      onClick={() => router.push('/dashboard/plans')}
                      type="button"
                    >
                      <ArrowRight className="mr-2 h-4 w-4" />
                      Upgrade Paket Langganan
                    </Button>
                  </div>
                )}
                <div className="space-y-2">
                  <Label htmlFor="instance-name">Nama WhatsApp</Label>
                  <Input 
                    id="instance-name" 
                    placeholder="Contoh: WhatsApp Bisnis Saya" 
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    disabled={isLoading}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone-number">Nomor Telepon</Label>
                  <Input 
                    id="phone-number" 
                    placeholder="+6281234567890" 
                    value={phoneNumber}
                    onChange={(e) => setPhoneNumber(e.target.value)}
                    disabled={isLoading}
                  />
                  <p className="text-xs text-muted-foreground">
                    Masukkan nomor telepon lengkap dengan kode negara (contoh: +62 untuk Indonesia).
                  </p>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end pt-4 border-t">
                <Button type="submit" disabled={isLoading} className="min-w-[150px]">
                  {isLoading ? (
                    <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Menyimpan...</>
                  ) : (
                    <><Save className="mr-2 h-4 w-4" /> Simpan WhatsApp</>
                  )}
                </Button>
              </CardFooter>
            </form>
          </Card>
        </main>
      </SidebarInset>
    </SidebarProvider>
  );
}
