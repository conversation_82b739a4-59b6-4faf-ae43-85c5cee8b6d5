"use client";

import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { MessageCircle, Loader2, Send, Trash2, X, Paperclip } from "lucide-react";
import { useTheme } from 'next-themes';
import { cn } from '@/lib/utils';
import { toast } from "sonner";

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  fileUrl?: string; // URL untuk preview gambar (opsional)
}

export function ChatWidget() {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      role: 'assistant',
      content: 'Halo! Saya asisten Heylo. Ada yang bisa saya bantu tentang platform AI kami?',
      timestamp: new Date(),
    },
  ]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [file, setFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { theme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // After mounting, we have access to the theme
  useEffect(() => {
    setMounted(true);
  }, []);

  // Scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, isLoading]);

  // Handle clipboard paste events for screenshots
  useEffect(() => {
    const handlePaste = (e: ClipboardEvent) => {
      if (!isOpen || isLoading) return;
      
      const items = e.clipboardData?.items;
      if (!items) return;

      for (let i = 0; i <items.length; i++) {
        if (items[i].type.indexOf('image') !== -1) {
          const blob = items[i].getAsFile();
          if (blob) {
            // Create a file with a meaningful name from the blob
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const pastedFile = new File([blob], `screenshot-${timestamp}.png`, { type: blob.type });
            setFile(pastedFile);
            
            // Show a brief toast notification
            toast("Screenshot berhasil ditambahkan ke pesan");
            break;
          }
        }
      }
    };

    // Add the event listener to the document
    document.addEventListener('paste', handlePaste);
    
    // Clean up
    return () => {
      document.removeEventListener('paste', handlePaste);
    };
  }, [isOpen, isLoading]);

  const handleSendMessage = async () => {
    if (!input.trim() && !file) return;

    // Create a new user message
    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: input,
      timestamp: new Date(),
    };

    // Add user message to chat
    setMessages((prev) => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);

    // Handle file if present
    let fileContent = '';
    let fileDataUrl = '';
    
    if (file) {
      try {
        // Handle different file types
        if (file.type.startsWith('text/') || file.type === 'application/json') {
          // For text files, read the content
          fileContent = await file.text();
          fileContent = `\n\nIsi file ${file.name}:\n${fileContent}`;
        } else if (file.type.startsWith('image/')) {
          // For images, create a preview message
          fileContent = '';
          
          // Create a data URL for preview
          fileDataUrl = await new Promise((resolve) => {
            const reader = new FileReader();
            reader.onloadend = () => resolve(reader.result as string);
            reader.readAsDataURL(file);
          });
          
          // Add file preview to messages if it's an image
          const imagePreviewMessage: Message = {
            id: `file-${Date.now()}`,
            role: 'user',
            content: input.trim() || `Saya mengunggah gambar: ${file.name}`,
            timestamp: new Date(),
            fileUrl: fileDataUrl,
          };
          setMessages((prev) => [...prev, imagePreviewMessage]);
        } else {
          // For other files, just mention they were uploaded
          fileContent = `\n\n[File diunggah: ${file.name}]`;
        }
      } catch (error) {
        console.error('Error reading file:', error);
        fileContent = `\n\n[Error membaca file: ${file.name}]`;
      }
      
      setFile(null);
    }

    try {
      // Prepare message to send to API
      let messageToSend;
      
      if (fileDataUrl && fileDataUrl.startsWith('data:image/')) {
        // If we have an image, send the data URL directly
        messageToSend = input.trim() + ' ' + fileDataUrl;
      } else {
        // Otherwise just send the text message with any file content
        messageToSend = input.trim() + fileContent;
      }
      
      // Prepare conversation history (last 15 messages)
      const conversationHistory = messages.slice(-15).map(msg => ({
        role: msg.role,
        content: msg.content
      }));
      
      // Call the API with message and conversation history
      const response = await fetch('/api/chat-support', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          message: messageToSend,
          history: conversationHistory
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to get response');
      }

      const data = await response.json();

      // Add AI response to chat
      const assistantMessage: Message = {
        id: Date.now().toString(),
        role: 'assistant',
        content: data.response,
        timestamp: new Date(),
      };

      setMessages((prev) => [...prev, assistantMessage]);
    } catch (error) {
      console.error('Error sending message:', error);
      
      // Add error message
      const errorMessage: Message = {
        id: Date.now().toString(),
        role: 'assistant',
        content: 'Maaf, terjadi kesalahan saat memproses pesan Anda. Silakan coba lagi nanti.',
        timestamp: new Date(),
      };

      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleFileUpload = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0]);
    }
  };

  const toggleChat = () => {
    setIsOpen(!isOpen);
  };

  // Function to clear chat history
  const clearChat = () => {
    setMessages([]);
    toast.success("Riwayat chat telah dihapus");
  };

  // Function to convert URLs in text to clickable links
  const convertLinksToAnchors = (text: string) => {
    // Regex to match URLs but exclude trailing punctuation
    const urlRegex = /(https?:\/\/[^\s]+?)([.,;:!?)'"\]\}]*)(?=\s|$)/g;
    
    return text.replace(urlRegex, (match, url, punctuation) => {
      // Return the URL as a link, followed by any punctuation outside the link
      return `<a href="${url}" target="_blank" rel="noopener noreferrer" class="text-primary underline hover:text-primary/80">${url}</a>${punctuation}`;
    });
  };

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {/* Chat button */}
      <Button
        onClick={toggleChat}
        className={cn(
          "h-14 w-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center",
          isOpen 
            ? "bg-destructive hover:bg-destructive/90 rotate-90" 
            : "bg-primary hover:bg-primary/90 animate-pulse-slow"
        )}
        aria-label={isOpen ? "Close chat" : "Open chat"}
      >
        <div className="relative">
          {isOpen ? (
            <X className="h-6 w-6 transition-transform duration-300" />
          ) : (
            <>
              <div className="w-6 h-6 flex items-center justify-center">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="transition-transform duration-300">
                  <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 13.8214 2.48697 15.5291 3.33782 17L2.5 21.5L7 20.6622C8.47087 21.513 10.1786 22 12 22Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M12 13.5C12.8284 13.5 13.5 12.8284 13.5 12C13.5 11.1716 12.8284 10.5 12 10.5C11.1716 10.5 10.5 11.1716 10.5 12C10.5 12.8284 11.1716 13.5 12 13.5Z" fill="currentColor"/>
                  <path d="M7.5 13.5C8.32843 13.5 9 12.8284 9 12C9 11.1716 8.32843 10.5 7.5 10.5C6.67157 10.5 6 11.1716 6 12C6 12.8284 6.67157 13.5 7.5 13.5Z" fill="currentColor"/>
                  <path d="M16.5 13.5C17.3284 13.5 18 12.8284 18 12C18 11.1716 17.3284 10.5 16.5 10.5C15.6716 10.5 15 11.1716 15 12C15 12.8284 15.6716 13.5 16.5 13.5Z" fill="currentColor"/>
                </svg>
              </div>
            </>
          )}
        </div>
      </Button>

      {/* Chat panel */}
      {isOpen && (
        <div className="absolute bottom-16 right-0 w-[380px] overflow-hidden rounded-2xl shadow-lg bg-background">
          <div className="bg-gradient-to-r from-primary to-primary/90 text-primary-foreground py-3 px-4 rounded-t-xl flex items-center justify-between">
            <div className="text-sm font-medium flex items-center gap-2">
              <Avatar className="h-7 w-7 ring-2 ring-white/20 shadow-md">
                <AvatarImage src="/faviconheylonew.png" alt="Heylo" className="object-contain p-0.5" />
                <AvatarFallback>H</AvatarFallback>
              </Avatar>
              <div className="flex flex-col">
                <span className="font-semibold">Heylo Support</span>
                <span className="text-[10px] text-primary-foreground/80 font-normal">Asisten AI yang siap membantu</span>
              </div>
            </div>
            <div className="flex items-center gap-1">
              {messages.length > 0 && (
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="h-6 w-6 text-primary-foreground/80 hover:text-primary-foreground hover:bg-primary-foreground/10 rounded-full"
                  onClick={clearChat}
                  title="Hapus riwayat chat"
                >
                  <Trash2 className="h-3.5 w-3.5" />
                </Button>
              )}
              <Button 
                variant="ghost" 
                size="icon" 
                className="h-6 w-6 text-primary-foreground/80 hover:text-primary-foreground hover:bg-primary-foreground/10 rounded-full"
                onClick={toggleChat}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
          
          <div className="bg-gradient-to-b from-background/50 to-background">
            <ScrollArea className="h-[380px] p-4">
              <div className="flex flex-col gap-3">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={cn(
                      "flex flex-col max-w-[85%] p-3 shadow-sm",
                      message.role === 'user'
                        ? "bg-primary text-primary-foreground self-end chat-message-user rounded-t-2xl rounded-bl-2xl"
                        : "bg-card border border-border/40 self-start chat-message-assistant rounded-t-2xl rounded-br-2xl"
                    )}
                  >
                    <div className="text-sm whitespace-pre-wrap break-words overflow-hidden">
                      {message.role === 'assistant' ? (
                        <div className="markdown-content">
                          {message.content.split('\n').map((line, i) => {
                            // Handling headings with ###
                            if (/^###\s+(.+)$/.test(line)) {
                              const match = line.match(/^###\s+(.+)$/);
                              if (match) {
                                const [_, heading] = match;
                                return (
                                  <h3 key={i} className="text-base font-bold mt-3 mb-2">{heading}</h3>
                                );
                              }
                            }
                            
                            // Handling numbered lists (1. 2. 3. etc)
                            if (/^\d+\.\s+\*\*(.+?)\*\*:?(.*)$/.test(line)) {
                              const match = line.match(/^(\d+)\.\s+\*\*(.+?)\*\*:?(.*)$/);
                              if (match) {
                                const [_, number, bold, rest] = match;
                                return (
                                  <div key={i} className="flex gap-2 mb-2">
                                    <div className="flex-shrink-0 font-medium">{number}.</div>
                                    <div className="flex-1 min-w-0">
                                      <span className="font-medium">{bold}</span>
                                      <span className="break-words">{rest}</span>
                                    </div>
                                  </div>
                                );
                              }
                            }
                            
                            // Handling bullet points with • character
                            if (/^•\s+(.+)$/.test(line)) {
                              const match = line.match(/^•\s+(.+)$/);
                              if (match) {
                                const [_, content] = match;
                                return (
                                  <div key={i} className="flex gap-2 mb-2">
                                    <div className="flex-shrink-0">•</div>
                                    <div>{content}</div>
                                  </div>
                                );
                              }
                            }
                            
                            // Handling bullet points with bold
                            if (/^-\s+\*\*(.+?)\*\*:?(.*)$/.test(line)) {
                              const match = line.match(/^-\s+\*\*(.+?)\*\*:?(.*)$/);
                              if (match) {
                                const [_, bold, rest] = match;
                                return (
                                  <div key={i} className="flex gap-2 mb-2">
                                    <div className="flex-shrink-0">•</div>
                                    <div>
                                      <span className="font-medium">{bold}</span>
                                      {rest}
                                    </div>
                                  </div>
                                );
                              }
                            }
                            
                            // Handling regular bullet points with -
                            if (/^-\s+(.+)$/.test(line)) {
                              const match = line.match(/^-\s+(.+)$/);
                              if (match) {
                                const [_, content] = match;
                                return (
                                  <div key={i} className="flex gap-2 mb-2">
                                    <div className="flex-shrink-0">•</div>
                                    <div>{content}</div>
                                  </div>
                                );
                              }
                            }
                            
                            // Replace **text** with <span className="font-medium">text</span>
                            if (line.includes('**')) {
                              const parts = line.split(/\*\*(.+?)\*\*/g);
                              return (
                                <p key={i} className="mb-2">
                                  {parts.map((part, j) => {
                                    return j % 2 === 1 ? 
                                      <span key={j} className="font-medium">{part}</span> : 
                                      <span key={j}>{part}</span>;
                                  })}
                                </p>
                              );
                            }
                            
                            // Empty lines become spacing
                            if (line.trim() === '') {
                              return <div key={i} className="h-2"></div>;
                            }
                            
                            // Regular paragraph with clickable links
                            return <p key={i} className="mb-2" dangerouslySetInnerHTML={{ __html: convertLinksToAnchors(line) }}></p>;
                          })}
                        </div>
                      ) : (
                        message.content
                      )}
                      {message.fileUrl && (
                        <div className="mt-2">
                          <img 
                            src={message.fileUrl} 
                            alt="Uploaded image" 
                            className="max-w-full rounded-md max-h-[200px] object-contain mt-2 border border-border/30" 
                          />
                        </div>
                      )}
                    </div>
                    <div className={cn(
                      "text-xs mt-1 self-end",
                      message.role === 'user'
                        ? "text-primary-foreground/70"
                        : "text-muted-foreground"
                    )}>
                      {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </div>
                  </div>
                ))}
                {isLoading && (
                  <div className="flex items-center gap-2 self-start bg-card border border-border/40 p-3 max-w-[85%] shadow-sm chat-message-assistant rounded-t-2xl rounded-br-2xl">
                    <div className="flex gap-1.5">
                      <div className="w-2 h-2 bg-primary rounded-full animate-pulse opacity-40" style={{ animationDelay: '0ms' }}></div>
                      <div className="w-2 h-2 bg-primary rounded-full animate-pulse opacity-60" style={{ animationDelay: '150ms' }}></div>
                      <div className="w-2 h-2 bg-primary rounded-full animate-pulse opacity-80" style={{ animationDelay: '300ms' }}></div>
                    </div>
                    <span className="text-xs font-medium text-muted-foreground">Mengetik...</span>
                  </div>
                )}
                <div ref={messagesEndRef} />
              </div>
            </ScrollArea>
          </div>
          
          {/* File preview area - moved above the input */}
          {file && (
            <div className="mx-3 mb-2 bg-card/80 backdrop-blur-sm rounded-xl p-2.5 text-xs flex justify-between items-center border border-border/30 shadow-sm">
              <div className="flex items-center gap-2 truncate">
                <div className="bg-primary/10 rounded-full p-1.5 flex-shrink-0">
                  <Paperclip className="h-3 w-3 text-primary" />
                </div>
                <span className="truncate font-medium">{file.name}</span>
              </div>
              <Button
                variant="ghost"
                size="icon"
                className="h-5 w-5 flex-shrink-0 hover:bg-destructive/10 hover:text-destructive rounded-full"
                onClick={() => setFile(null)}
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          )}
          
          <div className="p-4 border-t flex gap-2 items-end bg-card/50">
            <Textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Ketik pesan..."
              className="min-h-[44px] max-h-24 resize-none focus-visible:ring-primary/30 rounded-xl border-muted-foreground/20 bg-background"
              disabled={isLoading}
            />
            <div className="flex flex-col gap-2">
              <Button
                type="button"
                variant="outline"
                size="icon"
                className="h-10 w-10 border-dashed hover:border-primary/50 hover:bg-primary/5 rounded-xl"
                onClick={handleFileUpload}
                disabled={isLoading}
                title="Lampirkan file"
              >
                <Paperclip className="h-4 w-4" />
              </Button>
              <Button
                type="button"
                variant="default"
                size="icon"
                className="h-10 w-10 bg-primary hover:bg-primary/90 transition-all duration-200 rounded-xl shadow-md"
                onClick={handleSendMessage}
                disabled={(!input.trim() && !file) || isLoading}
                title="Kirim pesan"
              >
                {isLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21.5 11.1L4.5 2.1C4.3 2 4.1 2 4 2C3.8 2 3.6 2.1 3.5 2.2C3.3 2.3 3.1 2.6 3.1 2.9L5 11.9L3 20.9C3 21.2 3.1 21.4 3.3 21.6C3.4 21.7 3.6 21.8 3.8 21.8C3.9 21.8 4 21.8 4.1 21.7L21.1 12.7C21.3 12.6 21.5 12.4 21.6 12.2C21.6 12 21.6 11.8 21.5 11.6C21.6 11.4 21.6 11.3 21.5 11.1Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M5 12H12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                )}
              </Button>
            </div>
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              className="hidden"
              accept="image/*,text/*,application/pdf,application/json"
            />
          </div>
        </div>
      )}
    </div>
  );
}
