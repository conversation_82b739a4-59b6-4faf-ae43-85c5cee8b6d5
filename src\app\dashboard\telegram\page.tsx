"use client";

import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { AppSidebar } from '@/components/app-sidebar';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Separator } from '@/components/ui/separator';
import { SidebarInset, SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { PlusCircle, Trash2, Settings, AlertTriangle, Loader2, History, Plus } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Toaster } from "@/components/ui/sonner";
import { toast } from "sonner";

interface TelegramInstance {
  id: string;
  name: string;
  username: string;
  status: 'CREATED' | 'CONNECTED' | 'DISCONNECTED' | 'ERROR' | string;
  lastConnected?: string;
  assistant?: {
    id: string;
    name: string;
  };
}

export default function TelegramPage() {
  const { accessToken } = useAuth();
  const [instances, setInstances] = useState<TelegramInstance[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [instanceToDelete, setInstanceToDelete] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const fetchInstances = useCallback(async () => {
    if (!accessToken) {
      setIsLoading(false);
      setError("Autentikasi diperlukan.");
      return;
    }
    setIsLoading(true);
    setError(null);
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/telegram`, {
        headers: { Authorization: `Bearer ${accessToken}` },
      });
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Gagal mengambil data Telegram.' }));
        throw new Error(errorData.message || 'Gagal mengambil data Telegram.');
      }
      const data: TelegramInstance[] = await response.json();
      setInstances(data);
    } catch (err: any) {
      setError(err.message);
      setInstances([]);
    } finally {
      setIsLoading(false);
    }
  }, [accessToken]);

  useEffect(() => {
    fetchInstances();
  }, [fetchInstances]);

  useEffect(() => {
    // Check for success message from sessionStorage (after creating new instance)
    const successMessage = sessionStorage.getItem('telegramToast');
    if (successMessage) {
      toast.success(successMessage);
      sessionStorage.removeItem('telegramToast');
    }
  }, []);

  const handleDeleteInstance = (instanceId: string) => {
    setInstanceToDelete(instanceId);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!instanceToDelete || !accessToken) return;

    setIsDeleting(true);
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/telegram/${instanceToDelete}`, {
        method: 'DELETE',
        headers: { Authorization: `Bearer ${accessToken}` },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Gagal menghapus bot Telegram.' }));
        throw new Error(errorData.message || 'Gagal menghapus bot Telegram.');
      }

      toast.success('Bot Telegram berhasil dihapus.');
      await fetchInstances(); // Refresh the list
    } catch (err: any) {
      toast.error(`Gagal menghapus bot Telegram: ${err.message}`);
    } finally {
      setIsDeleting(false);
      setDeleteDialogOpen(false);
      setInstanceToDelete(null);
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'connected': return 'default';
      case 'created': return 'secondary';
      case 'disconnected': return 'outline';
      case 'error': return 'destructive';
      default: return 'secondary';
    }
  };

  return (
    <SidebarProvider>
      <AppSidebar />
      <Toaster />
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Hapus Bot Telegram</AlertDialogTitle>
            <AlertDialogDescription>
              Apakah Anda yakin ingin menghapus bot Telegram ini? Tindakan ini tidak dapat dibatalkan.
              Semua percakapan dan data terkait bot ini akan dihapus secara permanen.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Batal</AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                confirmDelete();
              }}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Menghapus...
                </>
              ) : (
                'Hapus'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>Pengaturan Telegram</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>

        <main className="flex-1 p-4 md:p-6 space-y-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-2xl font-semibold">Telegram</h1>
              <p className="text-muted-foreground">
                Kelola semua bot Telegram Anda di sini.
              </p>
            </div>
            <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3">
              <Link href="/dashboard/telegram/chats" passHref>
                <Button variant="outline" className="w-full sm:w-auto">
                  <History className="mr-2 h-4 w-4" /> Histori Percakapan
                </Button>
              </Link>
              <Link href="/dashboard/telegram/new" passHref>
                <Button variant="default" className="w-full sm:w-auto">
                  <PlusCircle className="mr-2 h-4 w-4" /> Tambah Bot Telegram Baru
                </Button>
              </Link>
            </div>
          </div>

          {isLoading ? (
            <Card>
              <CardContent className="flex items-center justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin mr-2" />
                <span>Memuat data bot Telegram...</span>
              </CardContent>
            </Card>
          ) : error ? (
            <Card>
              <CardContent className="flex items-center justify-center py-8 text-center">
                <div>
                  <AlertTriangle className="h-8 w-8 text-destructive mx-auto mb-2" />
                  <p className="text-destructive font-medium">Terjadi Kesalahan</p>
                  <p className="text-muted-foreground">{error}</p>
                  <Button variant="outline" size="sm" onClick={fetchInstances} className="mt-4">
                    Coba Lagi
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : instances.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-20">
              <div className="relative mb-8">
                <div className="w-24 h-24 rounded-3xl bg-primary/10 dark:bg-primary/20 flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-12 w-12 text-primary">
                    <path d="m22 2-7 20-4-9-9-4Z"/>
                    <path d="M22 2 11 13"/>
                  </svg>
                </div>
                <div className="absolute -top-2 -right-2 w-8 h-8 rounded-full bg-primary flex items-center justify-center">
                  <Plus className="h-4 w-4 text-primary-foreground" />
                </div>
              </div>
              <h3 className="text-2xl font-bold mb-3">Belum Ada Bot Telegram</h3>
              <p className="text-muted-foreground text-center max-w-md mb-8">
                Mulai dengan membuat bot Telegram pertama Anda. Buat bot melalui BotFather dan hubungkan dengan asisten AI!
              </p>
              <Button asChild size="lg" className="bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg">
                <Link href="/dashboard/telegram/new">
                  <Plus className="mr-2 h-5 w-5" />
                  Tambahkan Bot Telegram Pertama
                </Link>
              </Button>
            </div>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>Daftar Bot Telegram</CardTitle>
                <CardDescription>
                  Kelola dan pantau semua bot Telegram yang telah Anda buat.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nama Bot</TableHead>
                      <TableHead>Username</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Asisten</TableHead>
                      <TableHead>Terakhir Terhubung</TableHead>
                      <TableHead className="text-right">Aksi</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {instances.map((instance) => (
                      <TableRow key={instance.id}>
                        <TableCell className="font-medium">{instance.name}</TableCell>
                        <TableCell>@{instance.username}</TableCell>
                        <TableCell>
                          <Badge variant={getStatusBadgeVariant(instance.status) as any}>
                            {instance.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {instance.assistant ? instance.assistant.name : '-'}
                        </TableCell>
                        <TableCell>
                          {instance.lastConnected ? new Date(instance.lastConnected).toLocaleString() : '-'}
                        </TableCell>
                        <TableCell className="text-right space-x-2">
                          <Link href={`/dashboard/telegram/${instance.id}`} passHref>
                            <Button variant="outline" size="sm">
                              <Settings className="mr-1 h-3 w-3" /> Kelola
                            </Button>
                          </Link>
                          <Button variant="destructive" size="sm" onClick={() => handleDeleteInstance(instance.id)}>
                            <Trash2 className="mr-1 h-3 w-3" /> Hapus
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )}
        </main>
      </SidebarInset>
    </SidebarProvider>
  );
}
