"use client"

import * as React from "react"
import Image from "next/image"
import { usePathname } from "next/navigation"
import { useTheme } from "next-themes"
import {
  Activity,
  Bot,
  LayoutDashboard,
  FileText,
  MessageSquare,
  Globe,
  Send,
  Instagram,
  ChevronDown,
  HelpCircle,
  BookOpen,
  Brain,
  Sparkles,
  Radio,
} from "lucide-react"

import { NavMain } from "@/components/nav-main"
import { NavSecondary } from "@/components/nav-secondary"
import { NavUser } from "@/components/nav-user"
import { ThemeSwitcher } from "@/components/theme-switcher"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"

const data = {
  user: {
    name: "User",
    email: "<EMAIL>",
    avatar: "/placeholder.svg",
  },
  navMainItems: [
    {
      title: "Dashboard",
      url: "/dashboard",
      icon: LayoutDashboard,
    },
    {
      title: "Asisten AI",
      url: "/dashboard/assistants",
      icon: Brain,
    },
    {
      title: "Knowledge Base",
      url: "/dashboard/knowledge",
      icon: FileText,
    },
    {
      title: "Playground AI",
      url: "/dashboard/playground",
      icon: Sparkles,
    },
    {
      title: "Channel",
      url: "#",
      icon: Radio,
      items: [
        {
          title: "WhatsApp",
          url: "/dashboard/whatsapp",
          icon: MessageSquare,
        },
        {
          title: "Webchat",
          url: "/dashboard/webchat",
          icon: Globe,
        },
        {
          title: "Telegram",
          url: "/dashboard/telegram",
          icon: Send,
        },
        {
          title: "Instagram (Coming Soon)",
          url: "#",
          icon: Instagram,
          disabled: true
        }
      ]
    },
    {
      title: "Guidelines",
      url: "/dashboard/guidelines",
      icon: BookOpen,
    },

  ],
  navSecondaryItems: [
    {
      title: "Status Layanan",
      url: "https://status.heylo.co.id",
      icon: Activity,
    },
    {
      title: "Bantuan CS",
      url: "https://wa.me/************",
      icon: HelpCircle,
      external: true,
    },
  ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { theme } = useTheme();
  const pathname = usePathname();
  const [mounted, setMounted] = React.useState(false);
  
  // After mounting, we have access to the theme
  React.useEffect(() => {
    setMounted(true);
  }, []);

  // Create a copy of the navigation items with dynamic active state
  const navMainItemsWithActive = React.useMemo(() => {
    return data.navMainItems.map(item => {
      // For items with subitems (like Channel)
      if (item.items) {
        const updatedSubItems = item.items.map(subItem => {
          if (subItem.url === "/dashboard/whatsapp") {
            return {
              ...subItem,
              isActive: pathname.startsWith("/dashboard/whatsapp"),
            };
          }
          if (subItem.url === "/dashboard/webchat") {
            return {
              ...subItem,
              isActive: pathname.startsWith("/dashboard/webchat"),
            };
          }
          if (subItem.url === "/dashboard/telegram") {
            return {
              ...subItem,
              isActive: pathname.startsWith("/dashboard/telegram"),
            };
          }
          return subItem;
        });

        // Channel is active if WhatsApp, Webchat, or Telegram page is active
        const isChannelActive = pathname.includes("/dashboard/whatsapp") || pathname.includes("/dashboard/webchat") || pathname.includes("/dashboard/telegram");
        
        return {
          ...item,
          items: updatedSubItems,
          isActive: isChannelActive,
        };
      }

      // Default: exact match for others
      return {
        ...item,
        isActive: pathname === item.url ||
                 (item.url === "/dashboard" && pathname === "/dashboard") ||
                 (item.url === "/dashboard/guidelines" && pathname.startsWith("/dashboard/guidelines")),
      };
    });
  }, [pathname]);
  
  return (
    <Sidebar variant="inset" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <a href="/dashboard" className="flex justify-start w-full pl-0 -ml-1">
                <div className="flex items-center h-10 w-32 overflow-hidden">
                  {mounted ? (
                    <Image 
                      src={theme === "light" ? "/logoblack.svg" : "/logowhite.svg"}
                      alt="Heylo Logo"
                      width={128}
                      height={40}
                      className="w-full h-full object-contain"
                    />
                  ) : (
                    // Placeholder with same dimensions to prevent layout shift
                    <div className="w-32 h-10" />
                  )}
                </div>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={navMainItemsWithActive} />
        <NavSecondary items={data.navSecondaryItems} className="mt-auto" />
      </SidebarContent>
      <SidebarFooter>
        <div className="flex items-center justify-between px-4 py-2">
          <NavUser />
          <ThemeSwitcher />
        </div>
      </SidebarFooter>
    </Sidebar>
  )
}
