import React, { useState, useCallback } from 'react';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Lightbulb, AlertCircle, Check, Loader2, Wand2, Info, Zap, ThumbsUp, Building2, Users, FileText, Sparkles, HelpCircle } from 'lucide-react';
import { toast } from 'sonner';
import { AIPromptGenerator } from '@/components/ai-prompt-generator';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

interface PromptAnalyzerProps {
  value: string;
  onChange: (value: string) => void;
  label?: string;
  placeholder?: string;
  className?: string;
  id?: string;
  required?: boolean;
}

export function PromptAnalyzer({
  value,
  onChange,
  label = 'Instruksi / Prompt',
  placeholder = 'Berikan instruksi detail untuk asisten Anda...',
  className = '',
  id = 'instructions',
  required = false,
}: PromptAnalyzerProps) {
  const [analyzing, setAnalyzing] = useState(false);
  const [score, setScore] = useState<number | null>(null);
  const [feedback, setFeedback] = useState<string[]>([]);
  const [enhancedPrompt, setEnhancedPrompt] = useState<string | null>(null);
  const [showEnhancedPrompt, setShowEnhancedPrompt] = useState(false);

  // Function to analyze the prompt with button click
  const analyzePrompt = useCallback(async () => {
    const text = value;
    if (!text || text.trim().length < 20) {
      toast.error('Prompt terlalu pendek', {
        description: 'Prompt harus memiliki minimal 20 karakter untuk dianalisis'
      });
      return;
    }
    
    setAnalyzing(true);
    
    try {
      const response = await fetch('/api/analyze-prompt', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ prompt: text }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to analyze prompt');
      }
      
      const data = await response.json();
      
      setScore(data.score);
      setFeedback(data.feedback);
      setEnhancedPrompt(data.enhancedPrompt);
      toast.success('Analisis prompt selesai');
    } catch (error) {
      console.error('Error analyzing prompt:', error);
      toast.error('Gagal menganalisis prompt', {
        description: 'Terjadi kesalahan saat menganalisis prompt. Silakan coba lagi.'
      });
    } finally {
      setAnalyzing(false);
    }
  }, [value]);
  
  // Function to apply the enhanced prompt
  const applyEnhancedPrompt = () => {
    if (enhancedPrompt) {
      onChange(enhancedPrompt);
      toast.success('Prompt yang ditingkatkan telah diterapkan');
      setShowEnhancedPrompt(false);
    }
  };
  
  // Get score variant
  const getScoreVariant = () => {
    if (!score) return 'secondary';
    if (score < 40) return 'destructive';
    if (score < 70) return 'outline';
    return 'default';
  };

  return (
    <div className="space-y-3">
      <div className="flex justify-between items-center gap-2">
        <div className="flex items-center gap-1.5">
          <Label htmlFor={id} className="text-base">{label}</Label>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Info className="h-3.5 w-3.5 text-muted-foreground cursor-help" />
              </TooltipTrigger>
              <TooltipContent side="right" className="max-w-80">
                <p>Berikan instruksi detail untuk asisten AI Anda. Prompt yang baik mencakup tujuan, batasan, dan contoh.</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        {score !== null && (
          <div className="flex items-center gap-2">
            <div className="text-sm text-muted-foreground hidden sm:block">Kualitas Prompt:</div>
            <Badge variant={getScoreVariant() as any} className="gap-1">
              {score < 40 ? <AlertCircle className="h-3 w-3" /> : 
               score < 70 ? <Zap className="h-3 w-3" /> : 
               <ThumbsUp className="h-3 w-3" />}
              {score}/100
            </Badge>
          </div>
        )}
      </div>
      
      <div className="flex flex-col gap-2 w-full">
        <Textarea
          id={id}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          className={`min-h-[180px] ${className}`}
          required={required}
        />
        <div className="flex flex-col sm:flex-row justify-between items-stretch sm:items-center gap-3 mt-5 mb-4">
          <AIPromptGenerator
            onPromptGenerated={onChange}
            disabled={analyzing}
          />

          <Button
            type="button"
            onClick={analyzePrompt}
            disabled={analyzing || !value || value.trim().length < 20}
            className="gap-1.5"
            size="sm"
          >
            {analyzing ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Menganalisis...
              </>
            ) : (
              <>
                <Wand2 className="h-4 w-4" />
                Analisis & Tingkatkan Prompt
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Guidance Card */}
      <Card className="mt-4 border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-950/20">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <Info className="h-4 w-4 text-amber-600 dark:text-amber-400" />
            Penting: AI Hanya Tau Info Yang Anda Berikan!
          </CardTitle>
          <CardDescription>
            AI tidak bisa menebak atau tau informasi yang tidak Anda masukkan. Semua info yang AI perlu tau WAJIB dimasukkan di instruksi ini atau knowledge base:
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="grid gap-3 text-sm">
            <div className="flex items-start gap-3">
              <Building2 className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
              <div>
                <div className="font-medium mb-1">Data & Fakta</div>
                <div className="text-muted-foreground text-xs">
                  Nama, alamat, kontak, jadwal, angka, tanggal, atau data apapun
                </div>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <FileText className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
              <div>
                <div className="font-medium mb-1">Link & Referensi</div>
                <div className="text-muted-foreground text-xs">
                  URL, email, nomor telepon, atau referensi eksternal lainnya
                </div>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <Users className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
              <div>
                <div className="font-medium mb-1">Aturan & Prosedur</div>
                <div className="text-muted-foreground text-xs">
                  Langkah-langkah, batasan, atau cara kerja yang harus diikuti
                </div>
              </div>
            </div>
          </div>

          <div className="mt-4 p-3 bg-muted/50 rounded-md border">
            <div className="text-sm space-y-2">
              <div>
                <strong className="font-semibold">Prinsip utama:</strong> Apapun use case Anda, semua informasi yang Anda anggap AI perlu ketahui untuk menjawab dengan benar - <strong className="font-semibold">WAJIB dimasukkan</strong> di instruksi ini atau knowledge base. <strong className="font-semibold">AI tidak bisa menebak</strong> informasi yang tidak ada!
              </div>
              <div>
                <strong className="font-semibold">Tips file upload:</strong> Jika Anda akan mengupload file ke knowledge base, <strong className="font-semibold">sebaiknya sebutkan juga di instruksi ini</strong> file apa saja yang tersedia, contoh: <em>"Anda memiliki akses ke dokumen company policy, product catalog, dan FAQ. Gunakan informasi dari dokumen tersebut untuk menjawab."</em>
              </div>
            </div>

            <div className="mt-4 flex justify-start">
              <Dialog>
                <DialogTrigger asChild>
                  <Button variant="outline" size="sm" className="gap-2">
                    <HelpCircle className="h-4 w-4" />
                    Lihat Contoh Prompt yang Bagus
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                      <Sparkles className="h-5 w-5" />
                      Tips Menulis Instruksi yang Efektif
                    </DialogTitle>
                    <DialogDescription>
                      Konteks peran sangat penting agar AI paham betul dia harus berperan sebagai apa
                    </DialogDescription>
                  </DialogHeader>

                  <div className="space-y-6">
                    {/* Tips Section */}
                    <div className="grid gap-4 text-sm">
                      <div className="flex items-start gap-3">
                        <Users className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                        <div>
                          <div className="font-medium mb-1">Definisikan Peran dengan Jelas</div>
                          <div className="text-muted-foreground text-xs">
                            Mulai dengan "Anda adalah..." untuk memberikan konteks peran yang spesifik
                          </div>
                        </div>
                      </div>

                      <div className="flex items-start gap-3">
                        <Building2 className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                        <div>
                          <div className="font-medium mb-1">Berikan Konteks & Informasi Lengkap</div>
                          <div className="text-muted-foreground text-xs">
                            Semua info yang AI perlu tau WAJIB disebutkan - alamat, kontak, jam operasional, dll
                          </div>
                        </div>
                      </div>

                      <div className="flex items-start gap-3">
                        <FileText className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                        <div>
                          <div className="font-medium mb-1">Sebutkan File yang Diupload</div>
                          <div className="text-muted-foreground text-xs">
                            Jika upload file, wajib mention di instruksi file apa saja yang tersedia
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Example Section */}
                    <div className="space-y-4">
                      <h4 className="font-semibold">Contoh Prompt yang Bagus:</h4>
                      <div className="bg-muted p-4 rounded-md border text-sm font-mono leading-relaxed">
                        <div className="space-y-3">
                          <p>Anda adalah customer service assistant untuk PT Maju Bersama, perusahaan distributor alat tulis kantor yang melayani B2B dan retail sejak 2010.</p>

                          <p><strong>INFORMASI PERUSAHAAN:</strong><br />
                          • Alamat: Jl. Gatot Subroto No.45, Jakarta Selatan<br />
                          • Jam operasional: Senin-Jumat 08:00-17:00, Sabtu 08:00-15:00<br />
                          • WhatsApp: 0812-3456-7890<br />
                          • Email: <EMAIL><br />
                          • Website: www.majubersama.co.id</p>

                          <p><strong>KNOWLEDGE BASE:</strong><br />
                          Anda memiliki akses ke dokumen berikut yang sudah diupload:<br />
                          • Katalog produk lengkap dengan harga<br />
                          • Kebijakan pengiriman dan return<br />
                          • FAQ customer service<br />
                          • Daftar area pengiriman<br />
                          Gunakan informasi dari dokumen tersebut untuk menjawab pertanyaan customer.</p>

                          <p><strong>TYPICAL QUESTIONS:</strong><br />
                          Customer biasanya akan bertanya tentang:<br />
                          • Harga dan ketersediaan produk<br />
                          • Cara pemesanan dan minimum order<br />
                          • Ongkos kirim dan estimasi pengiriman<br />
                          • Kebijakan return dan garansi<br />
                          • Status pesanan yang sudah dibuat</p>

                          <p><strong>ATURAN:</strong><br />
                          • Selalu ramah dan profesional dalam menjawab<br />
                          • Berikan informasi lengkap berdasarkan dokumen yang tersedia<br />
                          • Untuk pemesanan, arahkan customer ke WhatsApp atau website<br />
                          • Jika ada komplain, minta detail dan arahkan ke tim yang tepat<br />
                          • Jika tidak tau jawaban, katakan "Saya akan cek dengan tim dan segera menghubungi Anda"</p>
                        </div>
                      </div>

                      <div className="p-3 bg-primary/5 rounded-md border border-primary/20">
                        <p className="text-sm"><strong>Mengapa contoh ini bagus?</strong></p>
                        <ul className="text-sm text-muted-foreground mt-2 space-y-1">
                          <li>✅ Peran jelas: customer service untuk perusahaan spesifik</li>
                          <li>✅ Konteks bisnis lengkap: jenis usaha, target market, tahun berdiri</li>
                          <li>✅ Informasi faktual lengkap: alamat, jam, kontak</li>
                          <li>✅ Mention file yang diupload dan instruksi penggunaan</li>
                          <li>✅ Typical questions: AI jadi prepared untuk pertanyaan umum</li>
                          <li>✅ Aturan realistic: fokus tanya jawab, bukan proses order</li>
                          <li>✅ Fallback action jika AI tidak tau jawaban</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </CardContent>
      </Card>

      {analyzing ? (
        <div className="flex flex-col gap-1.5">
          <div className="flex items-center text-sm text-muted-foreground">
            <Loader2 className="h-3.5 w-3.5 mr-1.5 animate-spin" />
            <span>Menganalisis prompt...</span>
          </div>
          <Progress value={45} className="h-1" />
        </div>
      ) : score !== null && (
        <div className="flex items-center gap-1.5 text-xs text-muted-foreground">
          <span className="inline-flex items-center">
            {score < 40 ? (
              <><AlertCircle className="h-3 w-3 text-destructive mr-1" /> Perlu perbaikan</>
            ) : score < 70 ? (
              <><Zap className="h-3 w-3 text-yellow-500 mr-1" /> Cukup baik</>
            ) : (
              <><ThumbsUp className="h-3 w-3 text-primary mr-1" /> Sangat baik</>
            )}
          </span>
          <span className="mx-1.5">•</span>
          <span>{value.length} karakter</span>
          <span className="mx-1.5">•</span>
          <span>{value.split(/\s+/).length} kata</span>
        </div>
      )}
      
      {feedback.length > 0 && (
        <Card className="mt-3 shadow-sm">
          <CardHeader className="py-3 px-4">
            <CardTitle className="text-sm font-medium flex items-center gap-1.5">
              <Lightbulb className="h-4 w-4 text-primary" />
              Umpan Balik Prompt
            </CardTitle>
          </CardHeader>
          <CardContent className="py-0 px-4">
            <ul className="text-sm space-y-2 text-muted-foreground">
              {feedback.map((item, index) => (
                <li key={index} className="flex items-start gap-2 pb-2">
                  <span className="flex-shrink-0 text-primary">•</span>
                  <span>{item}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}
      
      {enhancedPrompt && (
        <Card className="mt-3 shadow-sm">
          <CardHeader className="py-3 px-4">
            <CardTitle className="text-sm font-medium flex items-center gap-1.5">
              <Wand2 className="h-4 w-4 text-primary" />
              Prompt yang Ditingkatkan
            </CardTitle>
            <CardDescription>
              Versi yang ditingkatkan dari prompt Anda
            </CardDescription>
          </CardHeader>
          <CardContent className="py-0 px-4">
            <div className="grid gap-4">
              {/* Enhanced version of user's prompt */}
              <div>
                <div className="flex items-center gap-1.5 mb-2">
                  <Badge variant="outline" className="bg-primary/5 text-primary text-xs font-normal">
                    Versi Ditingkatkan
                  </Badge>
                  <span className="text-xs text-muted-foreground">Berdasarkan prompt Anda</span>
                </div>
                <div className="bg-muted/50 rounded-md p-3 mb-2 text-sm border border-border/50 max-h-40 overflow-y-auto">
                  {enhancedPrompt.split('\n').map((line, i) => (
                    <React.Fragment key={i}>
                      {line}
                      <br />
                    </React.Fragment>
                  ))}
                </div>
                <div className="flex justify-end mt-6">
                  <Button 
                    type="button"
                    size="sm" 
                    variant="default" 
                    className="h-7 px-3 text-xs gap-1 shadow-sm"
                    onClick={applyEnhancedPrompt}
                  >
                    <Check className="h-3 w-3" />
                    Terapkan Prompt Ini
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

// Helper component for the plus icon
function Plus({ className }: { className?: string }) {
  return (
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      viewBox="0 0 24 24" 
      fill="none" 
      stroke="currentColor" 
      strokeWidth="2" 
      strokeLinecap="round" 
      strokeLinejoin="round" 
      className={className}
    >
      <path d="M12 5v14M5 12h14" />
    </svg>
  );
}
