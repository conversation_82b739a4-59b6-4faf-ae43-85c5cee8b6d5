"use client"

import { useState, useCallback } from "react"
import { AppSidebar } from "@/components/app-sidebar"
import {
  <PERSON><PERSON>crumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Separator } from "@/components/ui/separator"
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { PromptAnalyzer } from "@/components/prompt-analyzer"
import { Save, AlertCircle, AlertTriangle, ArrowRight, FileText, Loader2, Globe, Link2, Check, Calendar, DollarSign, Search, Image, Mail, BarChart3, Video, Zap, Info } from "lucide-react"
import { useAuth } from "@/contexts/AuthContext"
import { useRouter } from 'next/navigation'
import { toast } from "sonner"
import { Toaster } from "@/components/ui/sonner"

export default function NewAssistantPage() {
  const [name, setName] = useState("")
  const [description, setDescription] = useState("")
  const [instructions, setInstructions] = useState("")
  const [files, setFiles] = useState<File[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [uploadMethod, setUploadMethod] = useState<'file' | 'url'>('file')
  const [url, setUrl] = useState('')
  const [isCrawling, setIsCrawling] = useState(false)
  const [crawlError, setCrawlError] = useState<string | null>(null)
  const [crawlPreview, setCrawlPreview] = useState<{content: string, filename: string} | null>(null)
  const [showPreview, setShowPreview] = useState(false)

  const { accessToken } = useAuth()
  const router = useRouter()

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFiles(Array.from(e.target.files))
      toast.success(`${e.target.files.length} file dipilih`)
    }
  }

  const handleCrawlUrl = useCallback(async () => {
    if (!url) return;
    
    // Validasi format URL
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      setCrawlError('URL harus dimulai dengan http:// atau https://');
      toast.error('Format URL tidak valid', {
        description: 'URL harus dimulai dengan http:// atau https://'
      });
      return;
    }
    
    setIsCrawling(true);
    setCrawlError(null);
    setCrawlPreview(null);
    
    try {
      const response = await fetch('/api/crawl-url', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ url })
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Gagal mengambil konten dari URL');
      }
      
      const data = await response.json();
      
      // Simpan hasil crawling untuk preview
      setCrawlPreview({
        content: data.content,
        filename: data.filename
      });
      
      // Tampilkan preview
      setShowPreview(true);
      
      toast.success('Konten berhasil diambil dari URL', {
        description: `Preview konten dari ${url} tersedia`
      });
    } catch (err: any) {
      setCrawlError(err.message);
      toast.error('Gagal mengambil konten', {
        description: err.message
      });
    } finally {
      setIsCrawling(false);
    }
  }, [url]);
  
  // Fungsi untuk menambahkan hasil crawling ke daftar file
  const addCrawlResultToFiles = useCallback(() => {
    if (!crawlPreview) return;
    
    // Buat file dari konten yang di-crawl
    const file = new File(
      [crawlPreview.content],
      crawlPreview.filename,
      { type: 'text/plain' }
    );
    
    // Tambahkan ke daftar file yang sudah ada
    setFiles(prevFiles => [...prevFiles, file]);
    
    toast.success('File berhasil ditambahkan', {
      description: `File ${crawlPreview.filename} telah ditambahkan ke daftar upload`
    });
    
    // Reset preview dan URL
    setCrawlPreview(null);
    setShowPreview(false);
    setUrl('');
  }, [crawlPreview]);
  
  // Fungsi untuk membatalkan hasil crawling
  const cancelCrawlResult = useCallback(() => {
    setCrawlPreview(null);
    setShowPreview(false);
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!accessToken) {
      setError("Authentication token not found. Please log in.")
      return
    }

    setIsLoading(true)
    setError(null)
    setIsUploading(false)

    const assistantData = {
      name,
      description,
      instructions,
    }

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/assistants`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`,
        },
        body: JSON.stringify(assistantData),
      })

      if (!response.ok) {
        const errorData = await response.json()
        
        // Check if this is a limit exceeded error
        if (errorData.message && errorData.message.includes('exceeded the maximum number of assistants')) {
          setError('limit_exceeded')
          return
        }
        
        // Check if this is a subscription not found error
        if (errorData.message && (errorData.message.includes('Subscription not found') || errorData.message.includes('subscription'))) {
          setError('subscription_required')
          return
        }
        
        throw new Error(errorData.message || `Failed to create assistant: ${response.status}`)
      }

      const newAssistant = await response.json()

      if (files.length > 0) {
        setIsUploading(true)
        const formData = new FormData()
        files.forEach(file => {
          formData.append('files', file)
        })

        const fileUploadResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/assistants/${newAssistant.id}/files`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${accessToken}`,
          },
          body: formData,
        })
        setIsUploading(false)

        if (!fileUploadResponse.ok) {
          const fileErrorData = await fileUploadResponse.json()
          setError(`Assistant created (ID: ${newAssistant.id}), but file upload failed: ${fileErrorData.message || fileUploadResponse.status}. You can try uploading files again via the edit page.`)
          setIsLoading(false)
          return
        }
      }

      // Store success message in sessionStorage to show after redirect
      const successMessage = "Asisten berhasil dibuat" + (files.length > 0 ? " dan file berhasil diupload!" : "!")
      sessionStorage.setItem('assistantToast', successMessage)
      
      // Redirect to the assistants page
      router.push("/dashboard/assistants")

    } catch (err: unknown) {
      setError((err as Error).message || "An unexpected error occurred.")
    } finally {
      setIsLoading(false)
      setIsUploading(false)
    }
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <Toaster />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator
              orientation="vertical"
              className="mr-2 data-[orientation=vertical]:h-4"
            />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="/dashboard">
                    Dashboard
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbLink href="/dashboard/assistants">
                    Asisten AI
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>Buat Asisten Baru</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>
        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
          <Card>
            <CardHeader>
              <CardTitle>Buat Asisten AI Baru</CardTitle>
              <CardDescription>
                Buat asisten AI dengan nama, deskripsi, dan instruksi sesuai kebutuhan Anda
              </CardDescription>
            </CardHeader>
            <form onSubmit={handleSubmit}>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="name">Nama Asisten</Label>
                  <Input 
                    id="name" 
                    placeholder="Masukkan nama asisten" 
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    required 
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="description">Deskripsi</Label>
                  <Textarea 
                    id="description" 
                    placeholder="Deskripsi singkat tentang asisten ini" 
                    className="min-h-[100px]"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <PromptAnalyzer
                    value={instructions}
                    onChange={setInstructions}
                    placeholder="Instruksi detail untuk asisten Anda..."
                    required
                  />
                </div>

                {/* Tools Section */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Label className="text-base">Tools & Integrations</Label>
                    <div className="flex items-center gap-1 text-xs font-medium text-orange-600 bg-orange-100 px-3 py-1 rounded-full border border-orange-200">
                      <Zap className="h-3 w-3" />
                      Coming Soon
                    </div>
                  </div>

                  <div className="rounded-lg border-2 border-dashed border-muted bg-muted/20 p-6">
                    <div className="flex items-start gap-3 mb-4">
                      <Info className="h-5 w-5 text-primary mt-0.5 flex-shrink-0" />
                      <div className="space-y-2">
                        <h3 className="font-medium text-sm">Mengapa Tools Penting?</h3>
                        <p className="text-sm text-muted-foreground">
                          Banyak user menulis instruksi seperti "buatkan jadwal di Google Calendar" atau "kirim email ke customer",
                          padahal assistant perlu di-connect secara eksplisit ke tools ini, bukan hanya instruksi teks.
                        </p>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                      {/* Google Calendar */}
                      <div className="bg-background/30 border border-muted rounded-lg p-4 opacity-50 cursor-not-allowed">
                        <div className="flex items-center gap-3 mb-2">
                          <Calendar className="h-6 w-6 text-blue-500/60" />
                          <h4 className="font-medium text-sm text-muted-foreground">Google Calendar</h4>
                        </div>
                        <p className="text-xs text-muted-foreground/80">
                          Scheduling appointments, managing events, checking availability
                        </p>
                      </div>

                      {/* Xero Accounting */}
                      <div className="bg-background/30 border border-muted rounded-lg p-4 opacity-50 cursor-not-allowed">
                        <div className="flex items-center gap-3 mb-2">
                          <DollarSign className="h-6 w-6 text-green-500/60" />
                          <h4 className="font-medium text-sm text-muted-foreground">Xero</h4>
                        </div>
                        <p className="text-xs text-muted-foreground/80">
                          Invoice management, financial reports, payment tracking
                        </p>
                      </div>

                      {/* Web Search */}
                      <div className="bg-background/30 border border-muted rounded-lg p-4 opacity-50 cursor-not-allowed">
                        <div className="flex items-center gap-3 mb-2">
                          <Search className="h-6 w-6 text-purple-500/60" />
                          <h4 className="font-medium text-sm text-muted-foreground">Web Search</h4>
                        </div>
                        <p className="text-xs text-muted-foreground/80">
                          Real-time information, current news, product research
                        </p>
                      </div>

                      {/* Image Generator */}
                      <div className="bg-background/30 border border-muted rounded-lg p-4 opacity-50 cursor-not-allowed">
                        <div className="flex items-center gap-3 mb-2">
                          <Image className="h-6 w-6 text-pink-500/60" />
                          <h4 className="font-medium text-sm text-muted-foreground">Image Generator</h4>
                        </div>
                        <p className="text-xs text-muted-foreground/80">
                          AI-powered image creation, logos, marketing materials
                        </p>
                      </div>

                      {/* Video Generator */}
                      <div className="bg-background/30 border border-muted rounded-lg p-4 opacity-50 cursor-not-allowed">
                        <div className="flex items-center gap-3 mb-2">
                          <Video className="h-6 w-6 text-purple-600/60" />
                          <h4 className="font-medium text-sm text-muted-foreground">Video Generator</h4>
                        </div>
                        <p className="text-xs text-muted-foreground/80">
                          AI-powered video creation, animations, promotional content
                        </p>
                      </div>

                      {/* Email */}
                      <div className="bg-background/30 border border-muted rounded-lg p-4 opacity-50 cursor-not-allowed">
                        <div className="flex items-center gap-3 mb-2">
                          <Mail className="h-6 w-6 text-red-500/60" />
                          <h4 className="font-medium text-sm text-muted-foreground">Email</h4>
                        </div>
                        <p className="text-xs text-muted-foreground/80">
                          Send notifications, follow-ups, automated responses
                        </p>
                      </div>

                      {/* Google Sheets */}
                      <div className="bg-background/30 border border-muted rounded-lg p-4 opacity-50 cursor-not-allowed">
                        <div className="flex items-center gap-3 mb-2">
                          <BarChart3 className="h-6 w-6 text-green-600/60" />
                          <h4 className="font-medium text-sm text-muted-foreground">Google Sheets</h4>
                        </div>
                        <p className="text-xs text-muted-foreground/80">
                          Data management, lead tracking, report generation
                        </p>
                      </div>

                      {/* More Tools Placeholder */}
                      <div className="bg-background/30 border border-muted rounded-lg p-4 opacity-50 cursor-not-allowed">
                        <div className="flex items-center gap-3 mb-2">
                          <Zap className="h-6 w-6 text-orange-500/60" />
                          <h4 className="font-medium text-sm text-muted-foreground">More Tools</h4>
                        </div>
                        <p className="text-xs text-muted-foreground/80">
                          CRM, SMS, WhatsApp, and many more integrations
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="knowledge">Knowledge Base</Label>
                  
                  {/* Tab selector for upload method */}
                  <div className="flex border-b mb-4">
                    <button 
                      type="button"
                      className={`px-4 py-2 flex items-center gap-1 ${uploadMethod === 'file' ? 'border-b-2 border-primary text-primary font-medium' : 'text-muted-foreground'}`}
                      onClick={() => setUploadMethod('file')}
                    >
                      <FileText className="h-4 w-4" />
                      Upload File
                    </button>
                    <button 
                      type="button"
                      className={`px-4 py-2 flex items-center gap-1 ${uploadMethod === 'url' ? 'border-b-2 border-primary text-primary font-medium' : 'text-muted-foreground'}`}
                      onClick={() => setUploadMethod('url')}
                    >
                      <Globe className="h-4 w-4" />
                      Crawl URL
                    </button>
                  </div>
                  
                  <div className="mt-2 mb-6 space-y-4 rounded-lg border-2 border-dashed border-primary/30 bg-primary/5 p-6 shadow-sm">
                    {uploadMethod === 'file' ? (
                      <>
                        <div className="flex items-center gap-2">
                          <FileText className="h-6 w-6 text-primary" />
                          <h3 className="text-lg font-semibold text-primary/90">Upload File</h3>
                        </div>
                        <p className="text-sm text-muted-foreground">Tambahkan file ke knowledge base asisten untuk meningkatkan kemampuannya.</p>
                      </>
                    ) : (
                      <>
                        <div className="flex items-center gap-2">
                          <Globe className="h-6 w-6 text-primary" />
                          <h3 className="text-lg font-semibold text-primary/90">Crawl URL</h3>
                        </div>
                        <p className="text-sm text-muted-foreground">Tambahkan konten dari URL ke knowledge base asisten. Konten akan dikonversi menjadi file teks.</p>
                      </>
                    )}
                    <div className="mt-2">
                      {uploadMethod === 'file' ? (
                        <>
                          <Label htmlFor="knowledge-upload" className="mb-2 block text-sm font-medium">Pilih File:</Label>
                          <div className="relative">
                            <Button 
                              type="button"
                              variant="outline"
                              className="bg-primary/10 text-primary hover:bg-primary/20 hover:text-primary mr-2"
                              onClick={() => document.getElementById('knowledge-upload')?.click()}
                              disabled={isLoading || isUploading}
                            >
                              <FileText className="mr-2 h-4 w-4" />
                              Pilih File
                            </Button>
                            <span className="text-sm text-muted-foreground">
                              {files.length > 0 
                                ? `${files.length} file dipilih` 
                                : "Belum ada file dipilih"}
                            </span>
                            <Input 
                              id="knowledge-upload" 
                              type="file" 
                              className="sr-only"
                              onChange={handleFileChange}
                              multiple
                              accept=".txt,.md,.pdf,.doc,.docx,.rtf,.odt,.csv,.xls,.xlsx,.ods,.ppt,.pptx,.odp,.json,.xml,.yaml,.yml"
                              disabled={isLoading || isUploading}
                            />
                          </div>
                          <p className="mt-1 text-xs text-muted-foreground">Format yang didukung: TXT, PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, dll.</p>
                        </>
                      ) : (
                        <>
                          <Label htmlFor="url-input" className="mb-2 block text-sm font-medium">Masukkan URL: <span className="text-muted-foreground font-normal">(format: https://example.com)</span></Label>
                          <div className="flex gap-2">
                            <div className="relative flex-1">
                              <Input
                                id="url-input"
                                type="url"
                                placeholder="https://example.com/article"
                                value={url}
                                onChange={(e) => setUrl(e.target.value)}
                                disabled={isLoading || isCrawling}
                                className={`pr-8 ${!url.startsWith('http://') && !url.startsWith('https://') && url.length > 0 ? 'border-destructive focus-visible:ring-destructive' : ''}`}
                              />
                              {url && (
                                <button
                                  type="button"
                                  className="absolute right-2 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                                  onClick={() => setUrl('')}
                                  disabled={isLoading || isCrawling}
                                >
                                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                                </button>
                              )}
                            </div>
                            <Button
                              type="button"
                              onClick={handleCrawlUrl}
                              disabled={!url || isLoading || isCrawling || (!url.startsWith('http://') && !url.startsWith('https://') && url.length > 0)}
                              className="bg-primary/10 text-primary hover:bg-primary/20 hover:text-primary"
                            >
                              {isCrawling ? (
                                <>
                                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                  Crawling...
                                </>
                              ) : (
                                <>
                                  <Link2 className="mr-2 h-4 w-4" />
                                  Crawl
                                </>
                              )}
                            </Button>
                          </div>
                          {crawlError && (
                            <div className="mt-2 text-sm text-destructive">
                              <AlertCircle className="inline-block mr-1 h-4 w-4" />
                              {crawlError}
                            </div>
                          )}
                          <p className="mt-1 text-xs text-muted-foreground">
                            Konten dari URL akan dikonversi menjadi file teks. Anda dapat melihat preview sebelum menambahkannya.
                          </p>
                          {url.length > 0 && !url.startsWith('http://') && !url.startsWith('https://') && (
                            <p className="mt-1 text-xs text-destructive flex items-center">
                              <AlertCircle className="h-3 w-3 mr-1" />
                              URL harus dimulai dengan http:// atau https://
                            </p>
                          )}
                          
                          {/* Preview hasil crawling */}
                          {showPreview && crawlPreview && (
                            <div className="mt-4 border rounded-md p-3 sm:p-4 bg-muted/30">
                              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 mb-3">
                                <h4 className="font-medium text-sm sm:text-base break-all">Preview: {crawlPreview.filename}</h4>
                                <div className="flex gap-2 w-full sm:w-auto">
                                  <Button 
                                    type="button" 
                                    variant="outline" 
                                    size="sm"
                                    onClick={cancelCrawlResult}
                                    className="text-destructive border-destructive hover:bg-destructive/10 flex-1 sm:flex-none"
                                  >
                                    Batalkan
                                  </Button>
                                  <Button 
                                    type="button" 
                                    variant="default" 
                                    size="sm"
                                    onClick={addCrawlResultToFiles}
                                    className="flex-1 sm:flex-none"
                                  >
                                    Tambahkan ke File
                                  </Button>
                                </div>
                              </div>
                              <div className="max-h-72 sm:max-h-96 overflow-y-auto border rounded p-2 sm:p-3 bg-card text-sm">
                                <pre className="whitespace-pre-wrap break-words font-mono text-xs sm:text-sm">
                                  {crawlPreview.content}
                                </pre>
                              </div>
                              <p className="text-xs text-muted-foreground mt-2 text-center sm:text-left">
                                {Math.round(crawlPreview.content.length / 1024 * 100) / 100} KB • {crawlPreview.content.split(/\s+/).length} kata
                              </p>
                            </div>
                          )}
                        </>
                      )}
                    </div>
                    {files.length > 0 && (
                      <div className="mt-2 space-y-1">
                        <p className="text-sm font-medium">File akan diupload:</p>
                        <ul className="list-disc pl-5 text-sm text-muted-foreground">
                          {files.map((file, index) => (
                            <li key={index} className="truncate">
                              <span className="break-all">{file.name}</span> ({(file.size / 1024).toFixed(2)} KB)
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
                {error && error !== 'limit_exceeded' && error !== 'subscription_required' && (
                  <div className="flex items-center gap-2 rounded-md border border-red-500 bg-red-50 p-3 text-sm text-red-700 mb-6">
                    <AlertCircle className="h-5 w-5 flex-shrink-0" />
                    <p>{error}</p>
                  </div>
                )}
                
                {error === 'subscription_required' && (
                  <div className="rounded-md border border-primary bg-primary/5 p-4 text-sm mb-6">
                    <div className="flex items-center gap-2 mb-2">
                      <AlertTriangle className="h-5 w-5 text-primary flex-shrink-0" />
                      <p className="font-medium text-primary">Langganan Diperlukan</p>
                    </div>
                    <p className="text-muted-foreground mb-3">
                      Untuk membuat asisten AI, Anda memerlukan paket langganan aktif. Silakan pilih paket langganan yang sesuai dengan kebutuhan Anda untuk mulai membuat asisten AI.
                    </p>
                    <Button 
                      variant="default"
                      onClick={() => router.push('/dashboard/plans')}
                    >
                      <ArrowRight className="mr-2 h-4 w-4" />
                      Lihat Paket Langganan
                    </Button>
                  </div>
                )}
                
                {error === 'limit_exceeded' && (
                  <div className="rounded-md border border-amber-500 bg-amber-50 p-4 text-sm mb-6">
                    <div className="flex items-center gap-2 mb-2">
                      <AlertTriangle className="h-5 w-5 text-amber-600 flex-shrink-0" />
                      <p className="font-medium text-amber-800">Batas Asisten Tercapai</p>
                    </div>
                    <p className="text-amber-700 mb-3">
                      Anda telah mencapai batas maksimum jumlah asisten yang dapat dibuat dengan paket langganan Anda saat ini.
                      Untuk membuat asisten baru, Anda perlu meningkatkan paket langganan Anda.
                    </p>
                    <Button 
                      variant="default" 
                      className="bg-amber-600 hover:bg-amber-700 text-white"
                      onClick={() => router.push('/dashboard/plans')}
                    >
                      <ArrowRight className="mr-2 h-4 w-4" />
                      Upgrade Paket Langganan
                    </Button>
                  </div>
                )}
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" type="button" onClick={() => router.back()} disabled={isLoading || isUploading}>
                  Batal
                </Button>
                <Button type="submit" disabled={isLoading || isUploading}>
                  {isLoading || isUploading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {isUploading ? "Mengupload file..." : "Menyimpan asisten..."}
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Simpan Asisten
                    </>
                  )}
                </Button>
              </CardFooter>
            </form>
          </Card>
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}
