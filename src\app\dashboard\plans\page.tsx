"use client";

import React, { useEffect, useState, useCallback } from "react";

// Declare gtag function for TypeScript
declare global {
  interface Window {
    gtag: (command: string, action: string, params?: any) => void;
    dataLayer: any[];
  }
}
import { AppSidebar } from "@/components/app-sidebar";
import { AddonPurchaseDialog } from "@/components/addon-purchase-dialog";
import { Addon, fetchAddons } from "@/lib/api/addons";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { <PERSON>R<PERSON>, Alert<PERSON>riangle, Check, CheckCircle2, <PERSON>ader2, Plus } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";
import { Toaster } from "@/components/ui/sonner";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Skeleton } from "@/components/ui/skeleton";

// Define types based on the API response
interface PlanFeatures {
  prioritySupport: boolean;
  advancedAnalytics: boolean;
  [key: string]: boolean;
}

interface Plan {
  id: string;
  code: string;
  name: string;
  description: string;
  price: number;
  interval: string;
  maxAssistants: number;
  maxWhatsappInstances: number;
  maxTelegramInstances: number;
  maxWebchatInstances: number;
  maxMessages: number;
  maxStorageInMB: number;
  features: PlanFeatures;
  status: string;
  sortOrder: number;
  isPopular: boolean;
  isDefault: boolean;
  isHidden: boolean;
}

interface SubscriptionUsage {
  assistantsCount: number;
  whatsappInstancesCount: number;
  telegramInstancesCount: number;
  webchatInstancesCount: number;
  messagesCount: number;
  storageUsedInMB: number;
}

interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  maxAssistants: number;
  maxWhatsappInstances: number;
  maxTelegramInstances: number;
  maxWebchatInstances: number;
  maxMessages: number;
  maxStorageInMB: number;
}

interface AddOnUsage {
  messagesCount: number;
}

interface AddOnResources {
  maxMessages: number;
}

interface Subscription {
  id: string;
  userId: string;
  plan: SubscriptionPlan;
  startDate: string;
  endDate: string;
  status: string;
  autoRenew: boolean;
  usage: SubscriptionUsage;
  addOnUsage?: AddOnUsage;
  addOnResources?: AddOnResources;
  resources?: SubscriptionPlan;
}

export default function PlansPage() {
  const { accessToken } = useAuth();
  const [plans, setPlans] = useState<Plan[]>([]);
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [isLoadingPlans, setIsLoadingPlans] = useState<boolean>(true);
  const [isLoadingSubscription, setIsLoadingSubscription] = useState<boolean>(true);
  const [isLoadingAddons, setIsLoadingAddons] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [addons, setAddons] = useState<Addon[]>([]);
  const [addonDialogOpen, setAddonDialogOpen] = useState<boolean>(false);
  const [processingPlanId, setProcessingPlanId] = useState<string | null>(null);

  // Fetch all available plans
  const fetchPlans = async () => {
    setIsLoadingPlans(true);
    setError(null);
    
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/plans`);
      
      if (!response.ok) {
        throw new Error("Gagal mengambil data paket langganan.");
      }
      
      const data: Plan[] = await response.json();
      // Sort plans by sortOrder
      const sortedPlans = data.sort((a, b) => a.sortOrder - b.sortOrder);
      setPlans(sortedPlans);
    } catch (err: any) {
      console.error("Error fetching plans:", err);
      setError(err.message);
    } finally {
      setIsLoadingPlans(false);
    }
  };

  // Fetch user's current subscription
  const fetchSubscription = async () => {
    if (!accessToken) {
      setIsLoadingSubscription(false);
      return;
    }
    
    setIsLoadingSubscription(true);
    
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/plans/subscription`,
        {
          headers: { Authorization: `Bearer ${accessToken}` },
        }
      );
      
      if (response.status === 404) {
        // User doesn't have a subscription yet
        setSubscription(null);
        return;
      }
      
      if (!response.ok) {
        throw new Error("Gagal mengambil data langganan.");
      }
      
      const data: Subscription = await response.json();
      setSubscription(data);
    } catch (err: any) {
      console.error("Error fetching subscription:", err);
      // Don't set error state here to avoid showing error UI when just subscription fails
    } finally {
      setIsLoadingSubscription(false);
    }
  };

  // Enroll in a subscription plan
  const enrollPlan = async (planId: string, isTrial: boolean = false) => {
    if (!accessToken) {
      toast.error("Silakan login terlebih dahulu untuk berlangganan.");
      return;
    }
    
    setProcessingPlanId(planId);
    
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/plans/subscription`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${accessToken}`,
          },
          body: JSON.stringify({ planId, isTrial }),
        }
      );
      
      if (!response.ok) {
        throw new Error("Gagal mendaftar paket langganan.");
      }
      
      const data = await response.json();
    
    // Get the selected plan details for the event tracking
    const selectedPlan = plans.find(plan => plan.id === planId);
    
    // Track subscription event in Google Analytics
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'purchase_subscription', {
        'subscription_type': isTrial ? 'trial' : 'paid',
        'plan_name': selectedPlan?.name || 'unknown',
        'plan_id': planId,
        'value': selectedPlan?.price || 0,
        'currency': 'IDR',
        'term': selectedPlan?.interval || 'monthly'
      });
    }
    
    // Show success message based on whether it's a trial or paid subscription
    if (isTrial) {
      toast.success("Berhasil mengaktifkan paket uji coba!", {
        description: "Anda sekarang dapat menikmati fitur premium secara gratis"
      });
    } else {
      toast.success("Berhasil membuat pesanan langganan!", {
        description: "Silakan lanjutkan ke halaman pembayaran"
      });
    }
      
      // Redirect to invoices page
      window.location.href = "/dashboard/plans/invoices";
      
    } catch (err: any) {
      console.error("Error enrolling in plan:", err);
      toast.error(err.message || "Gagal mendaftar paket langganan.");
    } finally {
      setProcessingPlanId(null);
    }
  };

  // Format price to IDR
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
      minimumFractionDigits: 0,
    }).format(price);
  };

  // Format date to readable format
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("id-ID", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  // Calculate usage percentage
  const calculateUsagePercentage = (used: number, max: number) => {
    return Math.min(Math.round((used / max) * 100), 100);
  };

  useEffect(() => {
    fetchPlans();
    fetchSubscription();
    if (accessToken) {
      fetchAvailableAddons();
    }
  }, [accessToken]);
  
  // Fetch available add-ons
  const fetchAvailableAddons = async () => {
    if (!accessToken) return;
    
    setIsLoadingAddons(true);
    try {
      const addonData = await fetchAddons(accessToken);
      // Filter for message add-ons only
      const messageAddons = addonData.filter(addon => 
        addon.code.toLowerCase().includes('message') || 
        addon.name.toLowerCase().includes('pesan')
      );
      setAddons(messageAddons);
    } catch (err) {
      console.error("Error fetching add-ons:", err);
      // Don't show error to user for add-ons, just log it
    } finally {
      setIsLoadingAddons(false);
    }
  };
  
  // Refresh data after add-on purchase
  const handleAddonPurchaseComplete = useCallback(() => {
    fetchSubscription();
  }, []);

  return (
    <SidebarProvider>
      <Toaster />
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b">
          <div className="flex items-center gap-2 px-4 w-full overflow-x-auto scrollbar-hide">
            <SidebarTrigger className="-ml-1 flex-shrink-0" />
            <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4 flex-shrink-0" />
            <Breadcrumb className="overflow-x-auto scrollbar-hide">
              <BreadcrumbList>
                <BreadcrumbItem className="flex-shrink-0">
                  <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="flex-shrink-0" />
                <BreadcrumbItem className="flex-shrink-0">
                  <BreadcrumbPage>Paket Layanan</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>

        <main className="flex-1 p-4 md:p-6 space-y-6">
          <div className="flex flex-col gap-2">
            <h1 className="text-3xl font-bold tracking-tight">Paket Layanan</h1>
            <p className="text-muted-foreground">
              Pilih paket layanan yang sesuai dengan kebutuhan Anda
            </p>
          </div>

          {/* Current Subscription */}
          {isLoadingSubscription ? (
            <Card>
              <CardHeader>
                <Skeleton className="h-8 w-1/3 mb-2" />
                <Skeleton className="h-4 w-1/2" />
              </CardHeader>
              <CardContent className="space-y-4">
                <Skeleton className="h-20 w-full" />
              </CardContent>
            </Card>
          ) : subscription ? (
            <Card>
              <CardHeader>
                <div className="flex items-center gap-2">
                  <CardTitle>
                    {subscription.status === 'active' ? 'Langganan Aktif' :
                     subscription.status === 'cancelled' ? 'Langganan Dibatalkan' :
                     subscription.status === 'expired' ? 'Langganan Berakhir' :
                     subscription.status === 'trial' ? 'Uji Coba Aktif' : 'Langganan'}
                  </CardTitle>
                  <Badge
                    variant={
                      subscription.status === 'active' ? 'default' :
                      subscription.status === 'trial' ? 'secondary' :
                      subscription.status === 'cancelled' ? 'destructive' :
                      subscription.status === 'expired' ? 'destructive' : 'outline'
                    }
                    className={
                      subscription.status === 'trial' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' : ''
                    }
                  >
                    {subscription.status === 'active' ? 'Aktif' :
                     subscription.status === 'cancelled' ? 'Dibatalkan' :
                     subscription.status === 'expired' ? 'Berakhir' :
                     subscription.status === 'trial' ? 'Uji Coba' : subscription.status}
                  </Badge>
                </div>
                <CardDescription>
                  Paket {subscription.plan.name} {
                    subscription.status === 'active' ? `aktif hingga ${formatDate(subscription.endDate)}` :
                    subscription.status === 'trial' ? `uji coba hingga ${formatDate(subscription.endDate)}` :
                    subscription.status === 'cancelled' ? `dibatalkan, berakhir pada ${formatDate(subscription.endDate)}` :
                    subscription.status === 'expired' ? `berakhir pada ${formatDate(subscription.endDate)}` :
                    `status: ${subscription.status}`
                  }
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                  <Card className="border shadow-sm">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Asisten AI</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">
                        {subscription.usage.assistantsCount} / {subscription.plan.maxAssistants}
                      </div>
                      <Progress 
                        value={calculateUsagePercentage(
                          subscription.usage.assistantsCount, 
                          subscription.plan.maxAssistants
                        )} 
                        className="mt-2"
                      />
                    </CardContent>
                  </Card>
                  
                  <Card className="border shadow-sm">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Nomor WhatsApp</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">
                        {subscription.usage.whatsappInstancesCount} / {subscription.plan.maxWhatsappInstances}
                      </div>
                      <Progress 
                        value={calculateUsagePercentage(
                          subscription.usage.whatsappInstancesCount, 
                          subscription.plan.maxWhatsappInstances
                        )} 
                        className="mt-2"
                      />
                    </CardContent>
                  </Card>

                  <Card className="border shadow-sm">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Akun Telegram</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">
                        {subscription.usage.telegramInstancesCount || 0} / {subscription.plan.maxTelegramInstances}
                      </div>
                      <Progress 
                        value={calculateUsagePercentage(
                          subscription.usage.telegramInstancesCount || 0, 
                          subscription.plan.maxTelegramInstances
                        )} 
                        className="mt-2"
                      />
                    </CardContent>
                  </Card>

                  <Card className="border shadow-sm">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Widget Webchat</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">
                        {subscription.usage.webchatInstancesCount || 0} / {subscription.plan.maxWebchatInstances}
                      </div>
                      <Progress 
                        value={calculateUsagePercentage(
                          subscription.usage.webchatInstancesCount || 0, 
                          subscription.plan.maxWebchatInstances
                        )} 
                        className="mt-2"
                      />
                    </CardContent>
                  </Card>
                  
                  <Card className="border shadow-sm">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Pesan</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">
                        {subscription.usage.messagesCount} / {subscription.plan.maxMessages}
                        {subscription.addOnResources && subscription.addOnResources.maxMessages ? ` + ${subscription.addOnResources.maxMessages}` : ''}
                      </div>
                      <Progress 
                        value={calculateUsagePercentage(
                          subscription.usage.messagesCount, 
                          (subscription.plan.maxMessages + (subscription.addOnResources?.maxMessages || 0))
                        )} 
                        className="mt-2"
                      />
                      {subscription.addOnResources && subscription.addOnResources.maxMessages > 0 && (
                        <div className="text-xs text-muted-foreground mt-2">
                          Termasuk {subscription.addOnResources.maxMessages} pesan tambahan
                        </div>
                      )}
                    </CardContent>
                  </Card>
                  
                  <Card className="border shadow-sm">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Penyimpanan</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">
                        {subscription.usage.storageUsedInMB.toFixed(1)} MB / {subscription.plan.maxStorageInMB.toFixed(1)} MB
                      </div>
                      <Progress 
                        value={calculateUsagePercentage(
                          subscription.usage.storageUsedInMB, 
                          subscription.plan.maxStorageInMB
                        )} 
                        className="mt-2"
                      />
                    </CardContent>
                  </Card>
                  
                  {/* Add-on Messages Card - Only show if add-ons exist */}
                  {subscription.addOnResources && subscription.addOnResources.maxMessages > 0 && (
                    <Card className="border shadow-sm">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium flex items-center">
                          <span className="text-primary">Add-on Pesan</span>
                          <Badge variant="outline" className="ml-2 bg-primary/10">Tambahan</Badge>
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {subscription.addOnUsage?.messagesCount || 0} / {subscription.addOnResources?.maxMessages || 0}
                        </div>
                        <Progress 
                          value={calculateUsagePercentage(
                            subscription.addOnUsage?.messagesCount || 0, 
                            subscription.addOnResources?.maxMessages || 1
                          )} 
                          className="mt-2"
                        />
                        <div className="text-xs text-muted-foreground mt-2">
                          Penggunaan add-on pesan tambahan
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </CardContent>
              <CardFooter className="flex flex-col space-y-4">
                <div className="w-full">
                  <p className="text-sm text-muted-foreground">
                    {subscription.autoRenew 
                      ? "Langganan Anda akan diperpanjang secara otomatis pada tanggal " + formatDate(subscription.endDate)
                      : "Langganan Anda akan berakhir pada tanggal " + formatDate(subscription.endDate)}
                  </p>
                </div>
                
                {/* Add-on Purchase Section with Usage Info */}
                {subscription.status === 'active' && (
                  <div className="w-full border-t pt-4">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                      <div>
                        <h4 className="text-sm font-medium">Kapasitas Pesan</h4>
                        <p className="text-sm text-muted-foreground mt-1">
                          {calculateUsagePercentage(
                            (subscription.usage?.messagesCount || 0) + (subscription.addOnUsage?.messagesCount || 0),
                            (subscription.plan?.maxMessages || 1) + (subscription.addOnResources?.maxMessages || 0)
                          ) > 70 ? (
                            <>Kapasitas pesan Anda hampir habis. Tambahkan kapasitas untuk melanjutkan percakapan.</>  
                          ) : (
                            <>Butuh lebih banyak pesan? Beli add-on pesan untuk langganan Anda.</>  
                          )}
                        </p>
                      </div>
                      <Button 
                        onClick={() => {
                          if (!isLoadingAddons && addons.length === 0) {
                            fetchAvailableAddons();
                          }
                          setAddonDialogOpen(true);
                        }}
                        className="whitespace-nowrap"
                      >
                        <Plus className="w-4 h-4 mr-2" />
                        Tambah Kapasitas
                      </Button>
                    </div>
                  </div>
                )}
                
                {/* Add-on Purchase Dialog */}
                {addons.length > 0 && (
                  <AddonPurchaseDialog
                    addon={addons[0]}
                    accessToken={accessToken || ''}
                    open={addonDialogOpen}
                    onOpenChange={setAddonDialogOpen}
                    onPurchaseComplete={handleAddonPurchaseComplete}
                  />
                )}
              </CardFooter>
            </Card>
          ) : null}

          {/* Error State */}
          {error && (
            <Card className="border-destructive bg-destructive/10">
              <CardHeader className="flex flex-row items-center space-x-3 pb-2">
                <AlertTriangle className="h-6 w-6 text-destructive" />
                <CardTitle className="text-destructive">Gagal Memuat Data</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-destructive/80">Terjadi kesalahan: {error}</p>
                <Button variant="outline" size="sm" onClick={fetchPlans} className="mt-4">
                  Coba Lagi
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Plans List */}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {isLoadingPlans ? (
              // Skeleton loaders for plans
              Array.from({ length: 3 }).map((_, index) => (
                <Card key={index} className="flex flex-col">
                  <CardHeader>
                    <Skeleton className="h-6 w-1/2 mb-2" />
                    <Skeleton className="h-4 w-3/4" />
                  </CardHeader>
                  <CardContent className="flex-1 space-y-4">
                    <Skeleton className="h-5 w-1/3" />
                    <div className="space-y-2">
                      {Array.from({ length: 4 }).map((_, i) => (
                        <Skeleton key={i} className="h-4 w-full" />
                      ))}
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Skeleton className="h-10 w-full" />
                  </CardFooter>
                </Card>
              ))
            ) : (
              plans
                .filter(plan => !plan.isHidden)
                .map(plan => (
                  <Card 
                    key={plan.id} 
                    className={`flex flex-col ${plan.isPopular ? 'border-primary' : ''}`}
                  >
                    <CardHeader>
                      <div className="flex justify-between items-center">
                        <CardTitle>{plan.name}</CardTitle>
                        {plan.isPopular && (
                          <Badge className="bg-primary">Populer</Badge>
                        )}
                      </div>
                      <CardDescription>{plan.description}</CardDescription>
                    </CardHeader>
                    <CardContent className="flex-1">
                      <div className="text-3xl font-bold mb-4">
                        {formatPrice(plan.price)}
                        <span className="text-sm font-normal text-muted-foreground">
                          /{plan.interval === 'monthly' ? 'bulan' : 'tahun'}
                        </span>
                      </div>
                      <ul className="space-y-2 mb-6">
                        <li className="flex items-center">
                          <Check className="h-4 w-4 mr-2 text-primary" />
                          <span>{plan.maxAssistants} Asisten AI</span>
                        </li>
                        <li className="flex items-center">
                          <Check className="h-4 w-4 mr-2 text-primary" />
                          <span>{plan.maxWhatsappInstances} Nomor WhatsApp</span>
                        </li>
                        <li className="flex items-center">
                          <Check className="h-4 w-4 mr-2 text-primary" />
                          <span>{plan.maxTelegramInstances} Akun Telegram</span>
                        </li>
                        <li className="flex items-center">
                          <Check className="h-4 w-4 mr-2 text-primary" />
                          <span>{plan.maxWebchatInstances} Widget Webchat</span>
                        </li>
                        <li className="flex items-center">
                          <Check className="h-4 w-4 mr-2 text-primary" />
                          <span>{plan.maxMessages} Pesan per bulan</span>
                        </li>
                        <li className="flex items-center">
                          <Check className="h-4 w-4 mr-2 text-primary" />
                          <span>{plan.maxStorageInMB} MB Penyimpanan</span>
                        </li>
                        {plan.features.prioritySupport && (
                          <li className="flex items-center">
                            <Check className="h-4 w-4 mr-2 text-primary" />
                            <span>Dukungan Prioritas</span>
                          </li>
                        )}
                        {plan.features.advancedAnalytics && (
                          <li className="flex items-center">
                            <Check className="h-4 w-4 mr-2 text-primary" />
                            <span>Analitik Lanjutan</span>
                          </li>
                        )}
                      </ul>
                    </CardContent>
                    <CardFooter className="flex flex-col gap-3">
                      <Button
                        className="w-full"
                        variant={plan.isPopular ? "default" : "outline"}
                        onClick={() => enrollPlan(plan.id)}
                        disabled={
                          processingPlanId === plan.id ||
                          (subscription?.plan.id === plan.id && (subscription?.status === 'active' || subscription?.status === 'trial'))
                        }
                      >
                        {processingPlanId === plan.id ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Memproses...
                          </>
                        ) : subscription?.plan.id === plan.id && (subscription?.status === 'active' || subscription?.status === 'trial') ? (
                          subscription?.status === 'trial' ? "Paket Uji Coba" : "Paket Aktif"
                        ) : (
                          "Pilih Paket"
                        )}
                      </Button>

                      {/* Show trial button only if user has never had any subscription */}
                      {!subscription && (
                        <>
                          <p className="text-xs text-muted-foreground text-center">atau</p>
                          <Button
                            className="w-full font-semibold"
                            variant="outline"
                            onClick={() => enrollPlan(plan.id, true)}
                            disabled={processingPlanId === plan.id}
                          >
                            {processingPlanId === plan.id ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Memproses...
                              </>
                            ) : (
                              "Coba Gratis 7 Hari"
                            )}
                          </Button>
                        </>
                      )}
                    </CardFooter>
                  </Card>
                ))
            )}
          </div>
        </main>
      </SidebarInset>
    </SidebarProvider>
  );
}
