"use client";

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { AppSidebar } from '@/components/app-sidebar';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Separator } from '@/components/ui/separator';
import { SidebarInset, SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ArrowLeft, Loader2, AlertTriangle, Save, ExternalLink } from 'lucide-react';
import { toast } from 'sonner';

interface NewTelegramInstanceResponse {
  id: string;
  name: string;
  botToken: string;
  username: string;
  userId: string;
  status: string;
  assistantId: string | null;
  createdAt: string;
  updatedAt: string;
}

export default function NewTelegramInstancePage() {
  const { accessToken } = useAuth();
  const router = useRouter();
  const [name, setName] = useState('');
  const [botToken, setBotToken] = useState('');
  const [username, setUsername] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isLimitExceeded, setIsLimitExceeded] = useState(false);



  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!accessToken) {
      setError("Autentikasi diperlukan.");
      toast.error("Autentikasi diperlukan.");
      return;
    }
    if (!name.trim() || !botToken.trim() || !username.trim()) {
      setError("Nama bot, username, dan bot token tidak boleh kosong.");
      toast.error("Nama bot, username, dan bot token tidak boleh kosong.");
      return;
    }

    setIsLoading(true);
    setError(null);
    setIsLimitExceeded(false);

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/telegram`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify({
          name,
          botToken,
          username
        }),
      });

      const responseData = await response.json();

      if (!response.ok) {
        // Check if this is a limit exceeded error
        if (responseData.message && responseData.message.includes('exceeded the maximum number of telegram instances')) {
          setIsLimitExceeded(true);
          return;
        }
        
        // Check if this is a subscription not found error
        if (responseData.message && (responseData.message.includes('Subscription not found') || responseData.message.includes('subscription'))) {
          setError('subscription_required');
          return;
        }
        
        throw new Error(responseData.message || 'Gagal membuat bot Telegram baru.');
      }
      
      // Store success message in sessionStorage to show after redirect
      sessionStorage.setItem('telegramToast', `Bot Telegram "${responseData.name}" berhasil dibuat!`);
      
      // Redirect to the Telegram instances list page
      router.push('/dashboard/telegram');
    } catch (err: any) {
      setError(err.message);
      toast.error(`Gagal membuat bot Telegram: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="/dashboard/telegram">Pengaturan Telegram</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>Buat Bot Telegram Baru</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>

        <main className="flex-1 p-4 md:p-6 space-y-6">
          <div className="mb-6">
            <Link href="/dashboard/telegram" passHref>
              <Button variant="outline" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" /> Kembali ke Daftar Bot Telegram
              </Button>
            </Link>
          </div>

          <div className="max-w-2xl mx-auto">
            <div className="mb-6">
              <h1 className="text-2xl font-semibold">Buat Bot Telegram Baru</h1>
              <p className="text-muted-foreground mt-2">
                Tambahkan bot Telegram baru untuk menghubungkan asisten AI Anda dengan platform Telegram.
              </p>
            </div>

            {/* Tutorial Card */}
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="text-lg">Cara Mendapatkan Bot Token</CardTitle>
                <CardDescription>
                  Ikuti langkah-langkah berikut untuk membuat bot Telegram dan mendapatkan token:
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="space-y-2">
                  <p className="text-sm"><strong>1.</strong> Buka Telegram dan cari <code className="bg-muted px-1 rounded">@BotFather</code></p>
                  <p className="text-sm"><strong>2.</strong> Kirim perintah <code className="bg-muted px-1 rounded">/newbot</code></p>
                  <p className="text-sm"><strong>3.</strong> Berikan nama bot (contoh: "Customer Service Bot")</p>
                  <p className="text-sm"><strong>4.</strong> Berikan username bot (harus diakhiri dengan "bot", contoh: "customer_service_bot")</p>
                  <p className="text-sm"><strong>5.</strong> BotFather akan memberikan token bot yang dimulai dengan angka</p>
                  <p className="text-sm"><strong>6.</strong> Salin nama, username, dan token tersebut ke form di bawah</p>
                </div>
                <div className="pt-2">
                  <Button variant="outline" size="sm" asChild>
                    <a href="https://t.me/botfather" target="_blank" rel="noopener noreferrer">
                      <ExternalLink className="mr-2 h-4 w-4" />
                      Buka BotFather
                    </a>
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Error States */}
            {isLimitExceeded && (
              <div className="rounded-md border border-amber-500 bg-amber-50 p-4 text-sm mb-6">
                <div className="flex items-center gap-2 mb-2">
                  <AlertTriangle className="h-5 w-5 text-amber-600 flex-shrink-0" />
                  <p className="font-medium text-amber-800">Batas Bot Telegram Tercapai</p>
                </div>
                <p className="text-amber-700">
                  Anda telah mencapai batas maksimum bot Telegram untuk paket saat ini.
                  Silakan upgrade paket Anda atau hapus bot yang tidak digunakan.
                </p>
              </div>
            )}

            {error === 'subscription_required' && (
              <div className="rounded-md border border-primary bg-primary/5 p-4 text-sm mb-6">
                <div className="flex items-center gap-2 mb-2">
                  <AlertTriangle className="h-5 w-5 text-primary flex-shrink-0" />
                  <p className="font-medium text-primary">Langganan Diperlukan</p>
                </div>
                <p className="text-muted-foreground">
                  Anda perlu berlangganan untuk membuat bot Telegram.
                  Silakan pilih paket berlangganan yang sesuai dengan kebutuhan Anda.
                </p>
              </div>
            )}

            {error && error !== 'subscription_required' && (
              <div className="flex items-center gap-2 rounded-md border border-destructive bg-destructive/10 p-3 text-sm text-destructive mb-6">
                <AlertTriangle className="h-5 w-5 flex-shrink-0" />
                <p>{error}</p>
              </div>
            )}

            {/* Form */}
            <Card>
              <form onSubmit={handleSubmit}>
                <CardHeader>
                  <CardTitle>Informasi Bot Telegram</CardTitle>
                  <CardDescription>
                    Masukkan detail bot Telegram yang ingin Anda buat. Asisten AI dapat diatur setelah bot berhasil dibuat.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="bot-name">Nama Bot</Label>
                    <Input
                      id="bot-name"
                      placeholder="Contoh: Customer Service Bot"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      disabled={isLoading}
                    />
                    <p className="text-xs text-muted-foreground">
                      Nama ini hanya untuk identifikasi di dashboard Anda.
                    </p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="bot-username">Username Bot</Label>
                    <Input
                      id="bot-username"
                      placeholder="Contoh: customer_service_bot"
                      value={username}
                      onChange={(e) => setUsername(e.target.value)}
                      disabled={isLoading}
                    />
                    <p className="text-xs text-muted-foreground">
                      Username bot Telegram tanpa simbol @. Harus unik dan diakhiri dengan "bot".
                    </p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="bot-token">Bot Token</Label>
                    <Input
                      id="bot-token"
                      placeholder="1234567890:ABCdefGHIjklMNOpqrsTUVwxyz"
                      value={botToken}
                      onChange={(e) => setBotToken(e.target.value)}
                      disabled={isLoading}
                      type="password"
                    />
                    <p className="text-xs text-muted-foreground">
                      Token yang diberikan oleh BotFather saat membuat bot baru.
                    </p>
                  </div>

                </CardContent>
                <CardFooter className="flex justify-end pt-4 border-t">
                  <Button type="submit" disabled={isLoading} className="min-w-[150px]">
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Membuat Bot...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Buat Bot Telegram
                      </>
                    )}
                  </Button>
                </CardFooter>
              </form>
            </Card>
          </div>
        </main>
      </SidebarInset>
    </SidebarProvider>
  );
}
