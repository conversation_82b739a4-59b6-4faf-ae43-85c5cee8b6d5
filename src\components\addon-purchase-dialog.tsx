"use client";

import { useState, useEffect } from "react";
import { <PERSON>lider } from "@/components/ui/slider";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Addon, AddonPurchaseRequest, purchaseAddons } from "@/lib/api/addons";
import { formatCurrency } from "@/lib/utils";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

interface AddonPurchaseDialogProps {
  addon: Addon | null;
  accessToken: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onPurchaseComplete?: () => void;
}

export function AddonPurchaseDialog({ 
  addon, 
  accessToken, 
  open, 
  onOpenChange,
  onPurchaseComplete 
}: AddonPurchaseDialogProps) {
  const router = useRouter();
  const [quantity, setQuantity] = useState<number>(addon?.minQuantity || 1);
  const [price, setPrice] = useState<number>(0);
  const [isPurchasing, setIsPurchasing] = useState<boolean>(false);

  // Reset quantity when addon changes
  useEffect(() => {
    if (addon) {
      setQuantity(addon.minQuantity);
    }
  }, [addon]);

  // Calculate price based on quantity and price tiers
  useEffect(() => {
    if (!addon) return;
    
    // Find the appropriate price tier
    const tier = addon.prices.find(
      (p, index) => 
        quantity >= p.quantity && 
        (index === addon.prices.length - 1 || quantity < addon.prices[index + 1].quantity)
    );
    
    if (tier) {
      setPrice(tier.price * quantity);
    } else {
      // Fallback to base price
      setPrice(addon.price * quantity);
    }
  }, [quantity, addon]);

  // Handle slider change
  const handleSliderChange = (value: number[]) => {
    if (!addon) return;
    setQuantity(value[0]);
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!addon) return;
    const value = parseInt(e.target.value, 10);
    if (!isNaN(value)) {
      // Clamp value between min and max
      const clampedValue = Math.min(Math.max(value, addon.minQuantity), addon.maxQuantity);
      setQuantity(clampedValue);
    }
  };

  // Handle purchase
  const handlePurchase = async () => {
    if (!addon) return;
    
    setIsPurchasing(true);
    try {
      const purchaseRequest: AddonPurchaseRequest = {
        items: [{ id: addon.id, quantity }]
      };
      
      const response = await purchaseAddons(accessToken, purchaseRequest);
      
      if (response.paymentLink) {
        toast.success("Pembayaran Siap", {
          description: "Anda akan diarahkan ke halaman invoice.",
          duration: 3000,
        });
        
        // Close the dialog
        onOpenChange(false);
        
        // Redirect to invoices page after a short delay
        setTimeout(() => {
          router.push("/dashboard/plans/invoices");
        }, 1000);
      } else {
        toast.success("Pembelian Berhasil", {
          description: "Add-on telah ditambahkan ke langganan Anda.",
          duration: 3000,
        });
        
        // Close the dialog
        onOpenChange(false);
        
        if (onPurchaseComplete) {
          onPurchaseComplete();
        }
      }
    } catch (error) {
      toast.error("Gagal Membeli Add-on", {
        description: error instanceof Error ? error.message : "Terjadi kesalahan saat membeli add-on.",
        duration: 5000,
      });
    } finally {
      setIsPurchasing(false);
    }
  };

  if (!addon) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Tambah {addon.name}</DialogTitle>
          <DialogDescription>{addon.description}</DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6 py-4">
          <div className="space-y-2">
            <div className="flex justify-between">
              <Label htmlFor="quantity">Jumlah Pesan</Label>
              <span className="text-sm text-muted-foreground">
                {formatCurrency(price)}
              </span>
            </div>
            
            <div className="flex items-center gap-4">
              <Slider
                id="quantity-slider"
                min={addon.minQuantity}
                max={addon.maxQuantity}
                step={1}
                value={[quantity]}
                onValueChange={handleSliderChange}
                className="flex-1"
              />
              <Input
                id="quantity"
                type="number"
                value={quantity}
                onChange={handleInputChange}
                min={addon.minQuantity}
                max={addon.maxQuantity}
                className="w-20"
              />
            </div>
            
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>{addon.minQuantity}</span>
              <span>{addon.maxQuantity}</span>
            </div>
          </div>
          
          {addon.prices.length > 0 && (
            <div className="space-y-2">
              <Label>Harga Tier</Label>
              <div className="grid grid-cols-1 gap-2 sm:grid-cols-2 lg:grid-cols-3">
                {addon.prices.map((tier, index) => (
                  <div 
                    key={`tier-${index}`} 
                    className={`rounded-md border p-2 text-sm ${
                      quantity >= tier.quantity && 
                      (index === addon.prices.length - 1 || quantity < addon.prices[index + 1].quantity)
                        ? "border-primary bg-primary/5"
                        : "border-border"
                    }`}
                  >
                    <div className="font-medium">≥ {tier.quantity} pesan</div>
                    <div className="text-muted-foreground">
                      {formatCurrency(tier.price)} / pesan
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
        
        <DialogFooter className="flex items-center justify-between">
          <div>
            <div className="text-sm font-medium">Total</div>
            <div className="text-2xl font-bold">{formatCurrency(price)}</div>
          </div>
          
          <Button onClick={handlePurchase} disabled={isPurchasing}>
            {isPurchasing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Memproses...
              </>
            ) : (
              "Beli Add-on"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
