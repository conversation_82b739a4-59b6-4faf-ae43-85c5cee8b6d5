"use client";

import React, { useEffect, useState, useCallback } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { AppSidebar } from '@/components/app-sidebar';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Separator } from '@/components/ui/separator';
import { SidebarInset, SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar';
import { MessageCircle, AlertTriangle, Loader2, Eye, ToggleLeft, ToggleRight, Plus, History, Bo<PERSON>, Download } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { 
  Pagination, 
  PaginationContent, 
  PaginationItem, 
  PaginationLink, 
  PaginationNext, 
  PaginationPrevious 
} from '@/components/ui/pagination';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from "sonner";
import { Toaster } from "@/components/ui/sonner";
import * as XLSX from 'xlsx';

interface Chat {
  id: string;
  whatsappId: string;
  phoneNumber: string; // Added phoneNumber field
  autoReplyEnabled: boolean;
  createdAt: string;
  messageCount: number;
  latestMessage: {
    userId: string;
    role: 'user' | 'assistant';
    content: string;
    createdAt: string;
  };
}

interface PaginatedResponse<T> {
  data: T[];
  totalItems: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
}

interface WhatsAppInstance {
  id: string;
  name: string;
  phoneNumber: string;
}

export default function WhatsAppChatsPage() {
  const { accessToken } = useAuth();
  const [chats, setChats] = useState<Chat[]>([]);
  const [instances, setInstances] = useState<WhatsAppInstance[]>([]);
  const [selectedInstance, setSelectedInstance] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [isExporting, setIsExporting] = useState(false);

  // Fetch WhatsApp instances
  const fetchInstances = useCallback(async () => {
    if (!accessToken) return;
    
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/whatsapp`, {
        headers: { Authorization: `Bearer ${accessToken}` },
      });
      
      if (!response.ok) {
        throw new Error('Gagal mengambil data nomor WhatsApp.');
      }
      
      const data: WhatsAppInstance[] = await response.json();
      setInstances(data);
      
      if (data.length > 0 && !selectedInstance) {
        setSelectedInstance(data[0].id);
      }
    } catch (err: any) {
      console.error('Error fetching WhatsApp instances:', err);
    }
  }, [accessToken, selectedInstance]);

  // Fetch chats for selected instance
  const fetchChats = useCallback(async () => {
    if (!accessToken || !selectedInstance) {
      setIsLoading(false);
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/whatsapp/${selectedInstance}/chats?page=${currentPage}&limit=${itemsPerPage}`, 
        {
          headers: { Authorization: `Bearer ${accessToken}` },
        }
      );
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Gagal mengambil data percakapan.' }));
        throw new Error(errorData.message || 'Gagal mengambil data percakapan.');
      }
      
      const data: PaginatedResponse<Chat> = await response.json();
      setChats(data.data);
      setTotalPages(data.totalPages);
      setCurrentPage(data.currentPage);
    } catch (err: any) {
      setError(err.message);
      setChats([]);
    } finally {
      setIsLoading(false);
    }
  }, [accessToken, selectedInstance, currentPage, itemsPerPage]);

  // Export all chats to Excel
  const exportChatsToExcel = async () => {
    if (!accessToken || !selectedInstance) {
      toast.error("Tidak dapat mengekspor data", {
        description: "Silakan pilih nomor WhatsApp terlebih dahulu",
      });
      return;
    }

    setIsExporting(true);
    toast.info("Mempersiapkan data untuk ekspor", {
      description: "Mohon tunggu sebentar...",
    });

    try {
      // Fetch all chats for the selected instance
      const allChats: Chat[] = [];
      let currentFetchPage = 1;
      let hasMorePages = true;
      const fetchLimit = 100; // Fetch more data per request for efficiency

      while (hasMorePages) {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/whatsapp/${selectedInstance}/chats?page=${currentFetchPage}&limit=${fetchLimit}`,
          {
            headers: { Authorization: `Bearer ${accessToken}` },
          }
        );

        if (!response.ok) {
          throw new Error("Gagal mengambil data percakapan");
        }

        const data: PaginatedResponse<Chat> = await response.json();
        allChats.push(...data.data);

        if (currentFetchPage >= data.totalPages) {
          hasMorePages = false;
        } else {
          currentFetchPage++;
        }
      }

      // Fetch messages for each chat
      const chatsWithMessages = [];
      
      for (const chat of allChats) {
        try {
          const messagesResponse = await fetch(
            `${process.env.NEXT_PUBLIC_API_URL}/whatsapp/${selectedInstance}/chats/${chat.id}/messages?limit=1000`,
            {
              headers: { Authorization: `Bearer ${accessToken}` },
            }
          );

          if (messagesResponse.ok) {
            const messagesData = await messagesResponse.json();
            const messages = messagesData.data || [];
            
            // Sort messages by createdAt timestamp
            const sortedMessages = [...messages].sort((a, b) => {
              return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
            });
            
            // Add each message as a row in the export data
            for (const message of sortedMessages) {
              chatsWithMessages.push({
                "Nomor Telepon": chat.phoneNumber,
                "Waktu": formatDate(message.createdAt),
                "Pengirim": message.role === 'user' ? 'Pengguna' : 'Asisten',
                "Pesan": message.content
              });
            }
          }
        } catch (err) {
          console.error(`Error fetching messages for chat ${chat.id}:`, err);
        }
      }

      // Create Excel workbook and worksheet
      const worksheet = XLSX.utils.json_to_sheet(chatsWithMessages);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "WhatsApp Chats");

      // Generate Excel file
      const currentDate = new Date().toISOString().split('T')[0];
      XLSX.writeFile(workbook, `whatsapp-chats-export-${currentDate}.xlsx`);

      toast.success("Ekspor berhasil", {
        description: `Data percakapan WhatsApp telah diekspor ke Excel`,
      });
    } catch (error) {
      console.error("Error exporting chats:", error);
      toast.error("Gagal mengekspor data", {
        description: "Terjadi kesalahan saat mengekspor data",
      });
    } finally {
      setIsExporting(false);
    }
  };

  // Toggle auto-reply for a chat
  const toggleAutoReply = async (chatId: string, currentState: boolean) => {
    if (!accessToken || !selectedInstance) return;
    
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/whatsapp/${selectedInstance}/chats/${chatId}`, 
        {
          method: 'PUT',
          headers: { 
            'Content-Type': 'application/json',
            Authorization: `Bearer ${accessToken}` 
          },
          body: JSON.stringify({ autoReplyEnabled: !currentState })
        }
      );
      
      if (!response.ok) {
        throw new Error('Gagal mengubah pengaturan auto-reply.');
      }
      
      // Update local state
      setChats(prevChats => 
        prevChats.map(chat => 
          chat.id === chatId 
            ? { ...chat, autoReplyEnabled: !currentState } 
            : chat
        )
      );
    } catch (err: any) {
      console.error('Error toggling auto-reply:', err);
      alert('Gagal mengubah pengaturan auto-reply. Silakan coba lagi.');
    }
  };

  useEffect(() => {
    fetchInstances();
  }, [fetchInstances]);

  useEffect(() => {
    fetchChats();
  }, [fetchChats, selectedInstance, currentPage, itemsPerPage]);

  // Format date to local string
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('id-ID', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Truncate message content if too long
  const truncateMessage = (message: string, maxLength: number = 50) => {
    return message.length > maxLength 
      ? message.substring(0, maxLength) + '...' 
      : message;
  };

  return (
    <React.Fragment>
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbLink href="/dashboard/whatsapp">Pengaturan WhatsApp</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>Histori Percakapan</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>

        <main className="flex-1 p-4 md:p-6 space-y-6">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div>
              <h1 className="text-2xl font-bold tracking-tight">Histori Percakapan WhatsApp</h1>
              <p className="text-muted-foreground">
                Lihat dan kelola percakapan dari nomor WhatsApp Anda.
              </p>
            </div>
          </div>

          {/* Instance selector */}
          <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
            <div className="w-full md:w-72">
              <Select 
                value={selectedInstance} 
                onValueChange={setSelectedInstance}
                disabled={isLoading || instances.length === 0}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Pilih Nomor WhatsApp" />
                </SelectTrigger>
                <SelectContent>
                  {instances.map(instance => (
                    <SelectItem key={instance.id} value={instance.id}>
                      {instance.name} ({instance.phoneNumber})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="w-full md:w-48">
              <Select 
                value={itemsPerPage.toString()} 
                onValueChange={(value) => setItemsPerPage(parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Item per halaman" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5 item</SelectItem>
                  <SelectItem value="10">10 item</SelectItem>
                  <SelectItem value="20">20 item</SelectItem>
                  <SelectItem value="50">50 item</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {isLoading && (
            <div className="flex justify-center items-center py-10">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <p className="ml-2 text-muted-foreground">Memuat percakapan...</p>
            </div>
          )}

          {error && !isLoading && (
            <Card className="border-destructive bg-destructive/10">
              <CardHeader className="flex flex-row items-center space-x-3 pb-2">
                <AlertTriangle className="h-6 w-6 text-destructive" />
                <CardTitle className="text-destructive">Gagal Memuat Data</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-destructive/80">Terjadi kesalahan: {error}</p>
                <Button variant="outline" size="sm" onClick={fetchChats} className="mt-4">
                  Coba Lagi
                </Button>
              </CardContent>
            </Card>
          )}

          {!isLoading && !error && (!selectedInstance || instances.length === 0) && (
            <div className="flex flex-col items-center justify-center py-20">
              <div className="relative mb-8">
                <div className="w-24 h-24 rounded-3xl bg-primary/10 dark:bg-primary/20 flex items-center justify-center">
                  <MessageCircle className="h-12 w-12 text-primary" />
                </div>
                <div className="absolute -top-2 -right-2 w-8 h-8 rounded-full bg-primary flex items-center justify-center">
                  <Plus className="h-4 w-4 text-primary-foreground" />
                </div>
              </div>
              <h3 className="text-2xl font-bold mb-3">Tidak Ada Nomor WhatsApp</h3>
              <p className="text-muted-foreground text-center max-w-md mb-8">
                Anda belum memiliki nomor WhatsApp yang terhubung. Hubungkan nomor WhatsApp Anda untuk mulai berinteraksi dengan pengguna melalui asisten AI.
              </p>
              <Button asChild size="lg" className="bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg">
                <Link href="/dashboard/whatsapp/new">
                  <MessageCircle className="mr-2 h-5 w-5" />
                  Hubungkan WhatsApp
                </Link>
              </Button>
            </div>
          )}

          {!isLoading && !error && selectedInstance && chats.length === 0 && (
            <div className="flex flex-col items-center justify-center py-20">
              <div className="relative mb-8">
                <div className="w-24 h-24 rounded-3xl bg-primary/10 dark:bg-primary/20 flex items-center justify-center">
                  <MessageCircle className="h-12 w-12 text-primary" />
                </div>
                <div className="absolute -top-2 -right-2 w-8 h-8 rounded-full bg-secondary flex items-center justify-center">
                  <History className="h-4 w-4 text-secondary-foreground" />
                </div>
              </div>
              <h3 className="text-2xl font-bold mb-3">Belum Ada Percakapan</h3>
              <p className="text-muted-foreground text-center max-w-md mb-8">
                Nomor WhatsApp ini belum memiliki riwayat percakapan. Percakapan akan muncul di sini setelah pengguna mulai berinteraksi dengan asisten Anda melalui WhatsApp.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Button asChild variant="outline" className="shadow-sm">
                  <Link href="/dashboard/assistants">
                    <Bot className="mr-2 h-4 w-4" />
                    Lihat Asisten
                  </Link>
                </Button>
                <Button asChild className="shadow-sm">
                  <Link href="/dashboard/playground">
                    <MessageCircle className="mr-2 h-4 w-4" />
                    Coba Asisten di Playground
                  </Link>
                </Button>
              </div>
            </div>
          )}

          {!isLoading && !error && chats.length > 0 && (
            <Card>
              <CardHeader className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div>
                  <CardTitle>Daftar Percakapan</CardTitle>
                  <CardDescription>
                    Menampilkan {chats.length} percakapan dari total {totalPages * itemsPerPage} percakapan.
                  </CardDescription>
                </div>
                <Button
                  onClick={exportChatsToExcel}
                  variant="outline"
                  size="sm"
                  disabled={isExporting || isLoading || chats.length === 0}
                >
                  {isExporting ? (
                    <React.Fragment>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Mengekspor...
                    </React.Fragment>
                  ) : (
                    <React.Fragment>
                      <Download className="mr-2 h-4 w-4" />
                      Ekspor Excel
                    </React.Fragment>
                  )}
                </Button>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>ID Percakapan</TableHead>
                      <TableHead>Nomor WhatsApp</TableHead>
                      <TableHead>Pesan Terakhir</TableHead>
                      <TableHead>Waktu</TableHead>
                      <TableHead>Jumlah Pesan</TableHead>
                      <TableHead>Auto-Reply</TableHead>
                      <TableHead className="text-right">Aksi</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {chats.map((chat) => (
                      <TableRow key={chat.id}>
                        <TableCell className="font-medium">{chat.id.substring(0, 8)}...</TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <span className="font-medium">{chat.phoneNumber}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col">
                            <span className="text-xs text-muted-foreground">
                              {chat.latestMessage.role === 'user' ? 'Pengguna' : 'Asisten'}:
                            </span>
                            <span>{truncateMessage(chat.latestMessage.content)}</span>
                          </div>
                        </TableCell>
                        <TableCell>{formatDate(chat.latestMessage.createdAt)}</TableCell>
                        <TableCell>{chat.messageCount}</TableCell>
                        <TableCell>
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            onClick={() => toggleAutoReply(chat.id, chat.autoReplyEnabled)}
                          >
                            {chat.autoReplyEnabled ? (
                              <><ToggleRight className="h-4 w-4 mr-1 text-primary" /> Aktif</>
                            ) : (
                              <><ToggleLeft className="h-4 w-4 mr-1 text-muted-foreground" /> Nonaktif</>
                            )}
                          </Button>
                        </TableCell>
                        <TableCell className="text-right">
                          <Link 
                            href={`/dashboard/whatsapp/${selectedInstance}/chats/${chat.id}`} 
                            passHref
                          >
                            <Button variant="outline" size="sm">
                              <Eye className="mr-1 h-3 w-3" /> Lihat Detail
                            </Button>
                          </Link>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
              <CardFooter className="flex justify-center">
                <Pagination>
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious 
                        href="#" 
                        onClick={(e) => {
                          e.preventDefault();
                          if (currentPage > 1) setCurrentPage(currentPage - 1);
                        }}
                        className={currentPage <= 1 ? "pointer-events-none opacity-50" : ""}
                      />
                    </PaginationItem>
                    
                    {Array.from({ length: totalPages }, (_, i) => i + 1)
                      .filter(page => {
                        // Show current page, first page, last page, and pages around current page
                        return page === 1 || 
                               page === totalPages || 
                               (page >= currentPage - 1 && page <= currentPage + 1);
                      })
                      .map((page, index, array) => {
                        // Add ellipsis
                        const showEllipsisBefore = index > 0 && array[index - 1] !== page - 1;
                        const showEllipsisAfter = index < array.length - 1 && array[index + 1] !== page + 1;
                        
                        return (
                          <React.Fragment key={page}>
                            {showEllipsisBefore && (
                              <PaginationItem>
                                <span className="px-4 py-2">...</span>
                              </PaginationItem>
                            )}
                            
                            <PaginationItem>
                              <PaginationLink 
                                href="#" 
                                onClick={(e) => {
                                  e.preventDefault();
                                  setCurrentPage(page);
                                }}
                                isActive={page === currentPage}
                              >
                                {page}
                              </PaginationLink>
                            </PaginationItem>
                            
                            {showEllipsisAfter && (
                              <PaginationItem>
                                <span className="px-4 py-2">...</span>
                              </PaginationItem>
                            )}
                          </React.Fragment>
                        );
                      })}
                    
                    <PaginationItem>
                      <PaginationNext 
                        href="#" 
                        onClick={(e) => {
                          e.preventDefault();
                          if (currentPage < totalPages) setCurrentPage(currentPage + 1);
                        }}
                        className={currentPage >= totalPages ? "pointer-events-none opacity-50" : ""}
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </CardFooter>
            </Card>
          )}
        </main>
        </SidebarInset>
      </SidebarProvider>
      <Toaster />
    </React.Fragment>
  );
}
