import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, Shield, Eye, Lock, Database, Mail } from "lucide-react";
import Link from "next/link";

export default function PrivacyPage() {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-card/50 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" asChild>
              <Link href="/">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Kembali
              </Link>
            </Button>
            <div className="flex items-center gap-2">
              <Shield className="h-6 w-6 text-primary" />
              <h1 className="text-2xl font-bold"><PERSON><PERSON><PERSON><PERSON></h1>
            </div>
          </div>
        </div>
      </header>

      {/* Content */}
      <main className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="space-y-6">
          {/* Introduction */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5 text-primary" />
                Pendahuluan
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-muted-foreground">
                PT Sandyakala Group Asia ("kami", "kita", atau "Heylo") berkomitmen untuk melindungi 
                privasi dan keamanan informasi pribadi Anda. Kebijakan Privasi ini menjelaskan bagaimana 
                kami mengumpulkan, menggunakan, dan melindungi informasi Anda saat menggunakan platform Heylo.
              </p>
              <p className="text-muted-foreground">
                Dengan menggunakan layanan kami, Anda menyetujui praktik yang dijelaskan dalam 
                Kebijakan Privasi ini.
              </p>
              <div className="bg-primary/5 border border-primary/20 rounded-lg p-4">
                <p className="text-sm">
                  <strong>Terakhir diperbarui:</strong> {new Date().toLocaleDateString('id-ID', { 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                  })}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Data Collection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5 text-primary" />
                Informasi yang Kami Kumpulkan
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-semibold mb-2">1. Informasi Akun</h3>
                <ul className="list-disc list-inside space-y-1 text-muted-foreground ml-4">
                  <li>Nama lengkap dan alamat email</li>
                  <li>Informasi profil dan preferensi akun</li>
                  <li>Data autentikasi dan keamanan</li>
                </ul>
              </div>
              
              <div>
                <h3 className="font-semibold mb-2">2. Data Penggunaan</h3>
                <ul className="list-disc list-inside space-y-1 text-muted-foreground ml-4">
                  <li>Aktivitas penggunaan platform dan fitur</li>
                  <li>Log sistem dan data teknis</li>
                  <li>Statistik performa asisten AI</li>
                </ul>
              </div>

              <div>
                <h3 className="font-semibold mb-2">3. Data Komunikasi</h3>
                <ul className="list-disc list-inside space-y-1 text-muted-foreground ml-4">
                  <li>Pesan dan percakapan melalui WhatsApp</li>
                  <li>Dokumen dan file yang diunggah</li>
                  <li>Metadata komunikasi</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* Data Usage */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Lock className="h-5 w-5 text-primary" />
                Penggunaan Informasi
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-muted-foreground">
                Kami menggunakan informasi yang dikumpulkan untuk:
              </p>
              <ul className="list-disc list-inside space-y-2 text-muted-foreground ml-4">
                <li>Menyediakan dan mengoperasikan layanan Heylo</li>
                <li>Memproses dan merespons permintaan dukungan</li>
                <li>Meningkatkan kualitas dan performa platform</li>
                <li>Mengirim notifikasi penting terkait layanan</li>
                <li>Memastikan keamanan dan mencegah penyalahgunaan</li>
                <li>Mematuhi kewajiban hukum yang berlaku</li>
              </ul>
            </CardContent>
          </Card>

          {/* Data Protection */}
          <Card>
            <CardHeader>
              <CardTitle>Perlindungan Data</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-muted-foreground">
                Kami menerapkan langkah-langkah keamanan teknis dan organisasi yang sesuai untuk 
                melindungi informasi pribadi Anda dari akses, penggunaan, atau pengungkapan yang tidak sah.
              </p>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="p-4 border rounded-lg">
                  <h4 className="font-semibold mb-2">Enkripsi Data</h4>
                  <p className="text-sm text-muted-foreground">
                    Data sensitif dienkripsi saat transit dan saat disimpan
                  </p>
                </div>
                <div className="p-4 border rounded-lg">
                  <h4 className="font-semibold mb-2">Akses Terbatas</h4>
                  <p className="text-sm text-muted-foreground">
                    Hanya personel yang berwenang yang dapat mengakses data
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Data Sharing */}
          <Card>
            <CardHeader>
              <CardTitle>Pembagian Informasi</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-muted-foreground">
                Kami tidak menjual, menyewakan, atau membagikan informasi pribadi Anda kepada pihak ketiga, 
                kecuali dalam situasi berikut:
              </p>
              <ul className="list-disc list-inside space-y-2 text-muted-foreground ml-4">
                <li>Dengan persetujuan eksplisit dari Anda</li>
                <li>Untuk mematuhi kewajiban hukum atau perintah pengadilan</li>
                <li>Dengan penyedia layanan tepercaya yang membantu operasional kami</li>
                <li>Dalam situasi darurat untuk melindungi keselamatan</li>
              </ul>
            </CardContent>
          </Card>

          {/* User Rights */}
          <Card>
            <CardHeader>
              <CardTitle>Hak Pengguna</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-muted-foreground">Anda memiliki hak untuk:</p>
              <ul className="list-disc list-inside space-y-2 text-muted-foreground ml-4">
                <li>Mengakses dan memperbarui informasi pribadi Anda</li>
                <li>Meminta penghapusan data pribadi Anda</li>
                <li>Membatasi pemrosesan data tertentu</li>
                <li>Meminta portabilitas data Anda</li>
                <li>Menarik persetujuan yang telah diberikan</li>
              </ul>
            </CardContent>
          </Card>

          {/* Contact */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="h-5 w-5 text-primary" />
                Hubungi Kami
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-muted-foreground">
                Jika Anda memiliki pertanyaan tentang Kebijakan Privasi ini atau ingin menggunakan 
                hak-hak Anda, silakan hubungi kami:
              </p>
              <div className="bg-muted/50 rounded-lg p-4">
                <div className="space-y-2">
                  <p><strong>PT Sandyakala Group Asia</strong></p>
                  <p>Email: <a href="mailto:<EMAIL>" className="text-primary hover:underline"><EMAIL></a></p>
                  <p>Platform: Heylo - AI Assistant Platform</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Updates */}
          <Card>
            <CardHeader>
              <CardTitle>Perubahan Kebijakan</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-muted-foreground">
                Kami dapat memperbarui Kebijakan Privasi ini dari waktu ke waktu. Perubahan material 
                akan diberitahukan melalui email atau notifikasi di platform. Penggunaan berkelanjutan 
                atas layanan kami setelah perubahan menunjukkan penerimaan Anda terhadap kebijakan yang diperbarui.
              </p>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
