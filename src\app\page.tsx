"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import { ThemeSwitcher } from "@/components/theme-switcher";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import {
  Laptop,
  MessageCircle,
  FileText,
  Bot,
  Zap,
  Globe,
  Lock,
  ChevronRight,
  LogOut,
  User,
  Settings,
} from "lucide-react";
import { useTheme } from 'next-themes';

export default function Home() {
  const { accessToken, user, logout } = useAuth();
  const [mounted, setMounted] = useState(false);
  const { theme } = useTheme();

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null; // Prevent hydration errors
  }

  return (
    <div className="min-h-screen flex flex-col relative">
      {/* Global Background Image */}
      <div className="fixed inset-0 w-full h-full z-0">
        <Image
          src="/bg2.png"
          alt="Background"
          fill
          className="object-cover pointer-events-none"
          priority
        />
      </div>
      {/* Navigation */}
      <header className="z-50 absolute top-0 left-0 right-0">

        <div className="container mx-auto px-4 py-3 flex items-center justify-between relative z-10">
          <div className="flex items-center space-x-2">
            {mounted && (
              <Image
                src={theme === 'dark' ? '/logowhite.svg' : '/logoblack.svg'}
                alt="Heylo Logo"
                width={120}
                height={40}
                priority
              />
            )}
          </div>
          
          <div className="flex items-center space-x-4">
            <ThemeSwitcher />
            
            {accessToken ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="relative h-10 w-10 rounded-full">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src="/placeholder-avatar.jpg" alt={user?.name || "User"} />
                      <AvatarFallback>{user?.name?.charAt(0) || "U"}</AvatarFallback>
                    </Avatar>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>Akun Saya</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem asChild>
                    <Link href="/dashboard" className="cursor-pointer">
                    <User className="mr-2 h-4 w-4" />
                    Dashboard
                  </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={logout} className="cursor-pointer">
                    <LogOut className="mr-2 h-4 w-4" />
                    Logout
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <div className="flex items-center space-x-2">
                <Button variant="outline" asChild>
                  <Link href="/login">Login</Link>
                </Button>
                <Button asChild>
                  <Link href="/register">Register</Link>
                </Button>
              </div>
            )}
          </div>
        </div>
      </header>


      
      {/* Hero Section */}
      <section className="pt-32 pb-20 px-4 relative z-10">
        
        {/* Decorative Stars - Hidden on mobile */}
        <Image
          src="/star.svg"
          width={60}
          height={60}
          alt="Decorative star"
          className="absolute top-[20%] left-[15%] opacity-70 animate-pulse z-0 hidden md:block"
          style={{ transform: 'rotate(15deg)' }}
        />
        <Image
          src="/star.svg"
          width={45}
          height={45}
          alt="Decorative star"
          className="absolute top-[25%] right-[20%] opacity-80 animate-pulse z-0 hidden md:block"
          style={{ transform: 'rotate(-10deg)', animationDelay: '0.5s' }}
        />
        <Image
          src="/star.svg"
          width={70}
          height={70}
          alt="Decorative star"
          className="absolute bottom-[30%] left-[25%] opacity-60 animate-pulse z-0 hidden md:block"
          style={{ transform: 'rotate(45deg)', animationDelay: '1s' }}
        />
        <Image
          src="/star.svg"
          width={50}
          height={50}
          alt="Decorative star"
          className="absolute bottom-[25%] right-[15%] opacity-75 animate-pulse z-0 hidden md:block"
          style={{ transform: 'rotate(-25deg)', animationDelay: '1.5s' }}  
        />
        
        <div className="container mx-auto text-center max-w-4xl relative z-10">
          <Badge className="mb-4 px-3 py-1 text-sm">AI Agent Platform</Badge>
          <h1 className="text-4xl md:text-6xl font-bold mb-6 flex items-center justify-center flex-wrap gap-2">
            <span className="inline-flex items-center">
              Heylo! 
              <Image 
                src="/heyloicon.svg" 
                width={50} 
                height={50} 
                alt="Heylo Icon" 
                className="ml-2 inline-block" 
              />
            </span>
            <span>Saatnya Bikin AI Agent Multichannel Tanpa Koding!</span>
          </h1>
          <p className="text-lg md:text-xl text-foreground dark:text-white mb-10 max-w-3xl mx-auto">
            Heylo adalah platform SaaS yang memudahkan tim customer support, bisnis kecil, startup teknologi, dan agensi dalam menciptakan AI agent pintar tanpa perlu menulis satu baris kode pun.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" asChild className="px-8">
              <Link href={accessToken ? "/dashboard" : "/register"}>
                Coba Gratis Sekarang
                <ChevronRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
            <Button size="lg" variant="outline" asChild className="px-8">
              <Link href="/dashboard/playground">
                <MessageCircle className="mr-2 h-5 w-5" />
                Demo Playground
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 relative z-10">

        <div className="container mx-auto max-w-6xl relative z-10">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Build AI Agent with <span className="text-primary">Your Own Style</span></h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              Setiap bisnis itu unik — begitu juga cara komunikasinya. Di Heylo, kamu bisa bikin AI Agent yang nggak cuma smart, tapi juga punya karakter yang sesuai brand kamu.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <Card className="bg-white dark:bg-muted/70 border-0 hover:bg-gray-50 dark:hover:bg-muted/80 transition-colors">
              <CardContent className="pt-6">
                <div className="w-12 h-12 mb-4">
                  <Image src="/robot-icon.png" alt="Robot Icon" width={48} height={48} />
                </div>
                <h3 className="text-xl font-semibold mb-2">Atur tone of voice</h3>
                <p className="text-muted-foreground">Sopan, santai, formal, atau sesuaikan dengan brand personality kamu.</p>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-muted/70 border-0 hover:bg-gray-50 dark:hover:bg-muted/80 transition-colors">
              <CardContent className="pt-6">
                <div className="w-12 h-12 mb-4">
                  <Image src="/chat-icon.png" alt="Chat Icon" width={48} height={48} />
                </div>
                <h3 className="text-xl font-semibold mb-2">Ajari cara jawab</h3>
                <p className="text-muted-foreground">Berikan instruksi khusus untuk pertanyaan tertentu agar jawaban sesuai kebutuhan.</p>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-muted/70 border-0 hover:bg-gray-50 dark:hover:bg-muted/80 transition-colors">
              <CardContent className="pt-6">
                <div className="w-12 h-12 mb-4">
                  <Image src="/personality-icon.png" alt="Personality Icon" width={48} height={48} />
                </div>
                <h3 className="text-xl font-semibold mb-2">Kasih personality</h3>
                <p className="text-muted-foreground">Buat batasan dan karakter agar AI agent terasa lebih "manusia" dan sesuai brand.</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Multichannel Section */}
      <section className="py-20 px-4 relative z-10">

        <div className="container mx-auto max-w-6xl relative z-10">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-foreground dark:text-white"><span className="text-primary">Heylo </span>Multichannel Integrations</h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              Dimanapun pelanggan menghubungi Anda, Heylo memastikan respons cepat dan terorganisir lewat integrasi multichannel yang siap pakai.
            </p>
          </div>

          <div className="relative">
            {/* Center Icon */}
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-20 hidden md:block">
              <Image
                src="/heyloicon.svg"
                width={100}
                height={100}
                alt="Heylo Icon"
              />
            </div>
            
            {/* Mobile Icon - Only visible on small screens */}
            <div className="flex justify-center mb-8 md:hidden">
              <Image
                src="/heyloicon.svg"
                width={80}
                height={80}
                alt="Heylo Icon"
              />
            </div>

            {/* Integration Cards Grid - 2x2 layout with gap in the middle */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-16">
              {/* WhatsApp & Telegram */}
              <Card className="bg-white dark:bg-muted/70 border-0 hover:bg-gray-50 dark:hover:bg-muted/80 transition-colors">
                <CardContent className="pt-6">
                  <div className="flex space-x-2 mb-4">
                    <div className="w-10 h-10">
                      <Image src="/whatsapp-icon.png" alt="WhatsApp Icon" width={40} height={40} />
                    </div>
                    <div className="w-10 h-10">
                      <Image src="/Telegram-icon.png" alt="Telegram Icon" width={40} height={40} />
                    </div>
                  </div>
                  <h3 className="text-xl font-semibold mb-2">WhatsApp & Telegram</h3>
                  <p className="text-muted-foreground">Respons instan 24/7 untuk pelanggan di platform messaging populer.</p>
                </CardContent>
              </Card>

              {/* Web Widget */}
              <Card className="bg-white dark:bg-muted/70 border-0 hover:bg-gray-50 dark:hover:bg-muted/80 transition-colors">
                <CardContent className="pt-6">
                  <div className="w-12 h-12 mb-4">
                    <Image src="/web-icon.png" alt="Web Icon" width={48} height={48} />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">Web Widget</h3>
                  <p className="text-muted-foreground">Pasang live chat AI di website dalam hitungan menit.</p>
                </CardContent>
              </Card>

              {/* Instagram */}
              <Card className="bg-white dark:bg-muted/70 border-0 hover:bg-gray-50 dark:hover:bg-muted/80 transition-colors">
                <CardContent className="pt-6">
                  <div className="w-12 h-12 mb-4">
                    <Image src="/instagram-icon.png" alt="Instagram Icon" width={48} height={48} />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">Instagram</h3>
                  <p className="text-muted-foreground">Balas DM dan komentar otomatis untuk meningkatkan engagement.</p>
                </CardContent>
              </Card>

              {/* Coming Soon */}
              <Card className="bg-white dark:bg-muted/70 border-0 hover:bg-gray-50 dark:hover:bg-muted/80 transition-colors">
                <CardContent className="pt-6">
                  <div className="w-12 h-12 mb-4">
                    <Image src="/bolt.png" alt="Bolt Icon" width={48} height={48} />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">Coming Soon</h3>
                  <p className="text-muted-foreground">TikTok DM, Facebook Messenger, Line, dan lainnya.</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Knowledge Base Section */}
      <section className="py-20 px-4 relative z-10">

        <div className="container mx-auto max-w-6xl relative z-10">
          <div className="md:flex items-center gap-12">
            <div className="md:w-1/2 mb-10 md:mb-0">
              <Badge className="mb-4">Knowledge Base</Badge>
              <h2 className="text-3xl md:text-4xl font-bold mb-6"><span className="text-primary">Knowledgeable AI</span> — Bisa Belajar dari Dokumen Kamu</h2>
              <p className="text-lg text-muted-foreground mb-6">
                AI agent Heylo bisa belajar dari file PDF, link website, Notion, Google Docs, dan masih banyak lagi. Tinggal upload atau paste link, dan AI kamu langsung bisa kasih jawaban berdasarkan info itu.
              </p>
              
              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="mr-3 mt-1 bg-primary/10 p-1 rounded-full">
                    <FileText className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <h4 className="font-medium">Upload manual produk</h4>
                    <p className="text-muted-foreground">AI bantu jawab pertanyaan teknis</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="mr-3 mt-1 bg-primary/10 p-1 rounded-full">
                    <Globe className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <h4 className="font-medium">Share link FAQ</h4>
                    <p className="text-muted-foreground">AI handle pertanyaan umum otomatis</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="mr-3 mt-1 bg-primary/10 p-1 rounded-full">
                    <FileText className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <h4 className="font-medium">Tambahkan SOP internal</h4>
                    <p className="text-muted-foreground">AI bantu onboarding tim baru</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="md:w-1/2 p-6 md:p-8">
              <div className="bg-white dark:bg-muted/70 rounded-lg p-4 shadow-sm border-0">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 rounded-full bg-primary/20 flex items-center justify-center mr-3">
                    <Bot className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="font-medium">AI Assistant</p>
                    <p className="text-xs text-muted-foreground">Powered by Heylo</p>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div className="bg-secondary/20 rounded-lg p-3 max-w-[80%]">
                    <p className="text-sm">Bagaimana cara mengganti password akun saya?</p>
                  </div>
                  
                  <div className="bg-primary/10 rounded-lg p-3 ml-auto max-w-[80%]">
                    <p className="text-sm">Untuk mengganti password akun Anda, silakan ikuti langkah-langkah berikut:

1. Login ke akun Anda
2. Klik menu "Pengaturan" di pojok kanan atas
3. Pilih tab "Keamanan"
4. Klik tombol "Ubah Password"
5. Masukkan password lama dan password baru
6. Klik "Simpan"

Jika Anda lupa password lama, Anda bisa menggunakan fitur "Lupa Password" di halaman login.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Advanced Features Section */}
      <section className="py-20 px-4 relative z-10">

        <div className="container mx-auto max-w-7xl relative z-10">
          <div className="text-center mb-16">
            <div className="flex items-center justify-center gap-3 mb-4">
              <Badge className="">AI Agent Builder</Badge>
              <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-950 dark:text-yellow-300 dark:border-yellow-800">
                Coming Soon
              </Badge>
            </div>
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              <span className="text-primary">Automation & Advanced</span> Integration
            </h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              Heylo bukan cuma chatbot. Kita sedang develop fitur-fitur advance supaya AI kamu bisa benar-benar jadi co-worker digital yang lebih powerful.
            </p>
          </div>

          <div className="space-y-16">
            {/* Business Actions */}
            <div>
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold mb-3">
                  <span className="text-primary">Business Actions</span>
                </h3>
                <p className="text-muted-foreground">AI agent yang bisa execute tasks bisnis langsung via chat</p>
              </div>
              <div className="grid lg:grid-cols-3 gap-6">
                <Card className="bg-white dark:bg-muted/70 border-0 hover:bg-gray-50 dark:hover:bg-muted/80 transition-colors">
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="rounded-full w-12 h-12 bg-primary/10 flex items-center justify-center">
                        <FileText className="h-6 w-6 text-primary" />
                      </div>
                      <div className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">Google Sheets</div>
                    </div>
                    <h4 className="text-lg font-semibold mb-3">Smart Spreadsheet Manager</h4>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      "Tolong update stok produk A jadi 50 di Google Sheets" → AI langsung update spreadsheet real-time
                    </p>
                  </CardContent>
                </Card>

                <Card className="bg-white dark:bg-muted/70 border-0 hover:bg-gray-50 dark:hover:bg-muted/80 transition-colors">
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="rounded-full w-12 h-12 bg-primary/10 flex items-center justify-center">
                        <Zap className="h-6 w-6 text-primary" />
                      </div>
                      <div className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">API Integration</div>
                    </div>
                    <h4 className="text-lg font-semibold mb-3">Invoice Generator</h4>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      "Buatkan invoice untuk client XYZ" → AI hit API accounting system, generate invoice otomatis
                    </p>
                  </CardContent>
                </Card>

                <Card className="bg-white dark:bg-muted/70 border-0 hover:bg-gray-50 dark:hover:bg-muted/80 transition-colors">
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="rounded-full w-12 h-12 bg-primary/10 flex items-center justify-center">
                        <User className="h-6 w-6 text-primary" />
                      </div>
                      <div className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">CRM Integration</div>
                    </div>
                    <h4 className="text-lg font-semibold mb-3">Lead Management</h4>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      "Tambahkan lead baru ini ke CRM" → AI input data ke Salesforce/HubSpot dengan detail lengkap
                    </p>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Productivity Actions */}
            <div>
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold mb-3">
                  <span className="text-primary">Productivity Actions</span>
                </h3>
                <p className="text-muted-foreground">AI agent yang bisa handle task productivity langsung via chat</p>
              </div>
              <div className="grid lg:grid-cols-3 gap-6">
                <Card className="bg-white dark:bg-muted/70 border-0 hover:bg-gray-50 dark:hover:bg-muted/80 transition-colors">
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="rounded-full w-12 h-12 bg-primary/10 flex items-center justify-center">
                        <Globe className="h-6 w-6 text-primary" />
                      </div>
                      <div className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">Google Calendar</div>
                    </div>
                    <h4 className="text-lg font-semibold mb-3">Smart Scheduler</h4>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      "Schedule meeting dengan tim marketing besok jam 2" → AI akses Google Calendar, cari slot kosong, bikin meeting
                    </p>
                  </CardContent>
                </Card>

                <Card className="bg-white dark:bg-muted/70 border-0 hover:bg-gray-50 dark:hover:bg-muted/80 transition-colors">
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="rounded-full w-12 h-12 bg-primary/10 flex items-center justify-center">
                        <MessageCircle className="h-6 w-6 text-primary" />
                      </div>
                      <div className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">Slack Integration</div>
                    </div>
                    <h4 className="text-lg font-semibold mb-3">Team Reminder</h4>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      "Kirim reminder ke tim tentang deadline project" → AI kirim notifikasi ke Slack/email dengan detail lengkap
                    </p>
                  </CardContent>
                </Card>

                <Card className="bg-white dark:bg-muted/70 border-0 hover:bg-gray-50 dark:hover:bg-muted/80 transition-colors">
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="rounded-full w-12 h-12 bg-primary/10 flex items-center justify-center">
                        <FileText className="h-6 w-6 text-primary" />
                      </div>
                      <div className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">Google Drive</div>
                    </div>
                    <h4 className="text-lg font-semibold mb-3">File Manager</h4>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      "Backup file penting ke Google Drive" → AI upload files otomatis ke folder yang tepat dengan naming convention
                    </p>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Data & Integration Actions */}
            <div>
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold mb-3">
                  <span className="text-primary">Data & Integration Actions</span>
                </h3>
                <p className="text-muted-foreground">AI agent yang bisa akses dan update data dari berbagai sistem</p>
              </div>
              <div className="grid lg:grid-cols-3 gap-6">
                <Card className="bg-white dark:bg-muted/70 border-0 hover:bg-gray-50 dark:hover:bg-muted/80 transition-colors">
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="rounded-full w-12 h-12 bg-primary/10 flex items-center justify-center">
                        <Laptop className="h-6 w-6 text-primary" />
                      </div>
                      <div className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">Database Query</div>
                    </div>
                    <h4 className="text-lg font-semibold mb-3">Real-time Data Checker</h4>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      "Cek status order #12345" → AI query database, kasih update real-time dengan detail lengkap
                    </p>
                  </CardContent>
                </Card>

                <Card className="bg-white dark:bg-muted/70 border-0 hover:bg-gray-50 dark:hover:bg-muted/80 transition-colors">
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="rounded-full w-12 h-12 bg-primary/10 flex items-center justify-center">
                        <Globe className="h-6 w-6 text-primary" />
                      </div>
                      <div className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">CMS API</div>
                    </div>
                    <h4 className="text-lg font-semibold mb-3">Website Updater</h4>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      "Update harga produk di website" → AI hit CMS API, update content langsung tanpa login dashboard
                    </p>
                  </CardContent>
                </Card>

                <Card className="bg-white dark:bg-muted/70 border-0 hover:bg-gray-50 dark:hover:bg-muted/80 transition-colors">
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="rounded-full w-12 h-12 bg-primary/10 flex items-center justify-center">
                        <FileText className="h-6 w-6 text-primary" />
                      </div>
                      <div className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">Report Generator</div>
                    </div>
                    <h4 className="text-lg font-semibold mb-3">Smart Report Builder</h4>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      "Generate report penjualan bulan ini" → AI pull data dari multiple sources, compile report otomatis
                    </p>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="py-20 px-4 relative z-10">

        <div className="container mx-auto max-w-6xl relative z-10">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Real Use Cases — <span className="text-primary">Buat Apa Aja!</span>
            </h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              Dari automasi bisnis sampai asisten personal, Heylo bisa disesuaikan dengan kebutuhan kamu.
              Ini beberapa contoh real yang udah dipake user kami.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {/* Business Automation */}
            <Card className="bg-white dark:bg-muted/70 border-0 hover:bg-gray-50 dark:hover:bg-muted/80 transition-colors">
              <CardContent className="pt-6">
                <div className="rounded-full w-12 h-12 bg-primary/10 flex items-center justify-center mb-4">
                  <MessageCircle className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-3">Automasi Q&A Bisnis</h3>
                <p className="text-muted-foreground mb-4">
                  <strong>Contoh:</strong> Toko online yang upload katalog produk, price list, dan FAQ ke Heylo.
                  Customer bisa tanya "Ada sepatu Nike ukuran 42?" langsung via WhatsApp, AI jawab real-time
                  dengan info stok dan harga.
                </p>
                <div className="text-sm text-primary bg-primary/5 rounded-lg p-3">
                  💡 Perfect untuk: E-commerce, restoran, klinik, jasa service
                </div>
              </CardContent>
            </Card>

            {/* Personal AI Assistant */}
            <Card className="bg-white dark:bg-muted/70 border-0 hover:bg-gray-50 dark:hover:bg-muted/80 transition-colors">
              <CardContent className="pt-6">
                <div className="rounded-full w-12 h-12 bg-primary/10 flex items-center justify-center mb-4">
                  <User className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-3">Asisten Personal Custom</h3>
                <p className="text-muted-foreground mb-4">
                  <strong>Contoh:</strong> Upload notes kuliah, dokumen kerja, atau personal knowledge base.
                  Tanya via WhatsApp: "Gimana cara bikin proposal yang kemarin?" AI jawab berdasarkan
                  dokumen kamu sendiri. ChatGPT kan gabisa connect langsung ke WhatsApp!
                </p>
                <div className="text-sm text-primary bg-primary/5 rounded-lg p-3">
                  💡 Perfect untuk: Mahasiswa, profesional, content creator
                </div>
              </CardContent>
            </Card>

            {/* Agency/Freelancer */}
            <Card className="bg-white dark:bg-muted/70 border-0 hover:bg-gray-50 dark:hover:bg-muted/80 transition-colors">
              <CardContent className="pt-6">
                <div className="rounded-full w-12 h-12 bg-primary/10 flex items-center justify-center mb-4">
                  <Laptop className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-3">Jasa Pembuatan Chatbot</h3>
                <p className="text-muted-foreground mb-4">
                  <strong>Contoh:</strong> Freelancer/agency bisa nawarin jasa "Bikin AI Chatbot untuk Bisnis Anda"
                  dengan Heylo. Setup cepat, white-label ready, client tinggal terima jadi.
                  Satu project bisa charge 2-5 juta!
                </p>
                <div className="text-sm text-primary bg-primary/5 rounded-lg p-3">
                  💡 Perfect untuk: Digital agency, freelancer, konsultan IT
                </div>
              </CardContent>
            </Card>

            {/* Community & Education */}
            <Card className="bg-white dark:bg-muted/70 border-0 hover:bg-gray-50 dark:hover:bg-muted/80 transition-colors">
              <CardContent className="pt-6">
                <div className="rounded-full w-12 h-12 bg-primary/10 flex items-center justify-center mb-4">
                  <Globe className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-3">Bot Komunitas & Edukasi</h3>
                <p className="text-muted-foreground mb-4">
                  <strong>Contoh:</strong> Grup WhatsApp kelas/komunitas yang upload materi pembelajaran.
                  Member bisa tanya "Rumus integral parsial gimana?" di grup, AI jawab berdasarkan
                  materi yang udah di-upload. Grup jadi lebih produktif!
                </p>
                <div className="text-sm text-primary bg-primary/5 rounded-lg p-3">
                  💡 Perfect untuk: Komunitas belajar, grup kelas, organisasi
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Additional Use Cases */}
          <div className="mt-12 text-center">
            <h3 className="text-xl font-semibold mb-6">Use Cases Lainnya yang Sering Dipake:</h3>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="p-4 rounded-lg bg-primary/5 border border-primary/10">
                <h4 className="font-medium mb-2">📋 Internal Knowledge Base</h4>
                <p className="text-sm text-muted-foreground">Upload SOP, manual, prosedur internal. Tim bisa tanya via WhatsApp grup.</p>
              </div>
              <div className="p-4 rounded-lg bg-primary/5 border border-primary/10">
                <h4 className="font-medium mb-2">🏥 Appointment Booking</h4>
                <p className="text-sm text-muted-foreground">Klinik/salon upload jadwal dokter, customer bisa cek slot kosong via chat.</p>
              </div>
              <div className="p-4 rounded-lg bg-primary/5 border border-primary/10">
                <h4 className="font-medium mb-2">📚 Course Assistant</h4>
                <p className="text-sm text-muted-foreground">Upload materi kursus, student bisa tanya tugas/materi kapan aja.</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Security Section */}
      <section className="py-20 px-4 relative z-10">

        <div className="container mx-auto max-w-6xl relative z-10">
          <div className="md:flex items-center gap-12">
            <div className="md:w-1/2 mb-10 md:mb-0">
              <Badge className="mb-4">Enterprise Ready</Badge>
              <h2 className="text-3xl md:text-4xl font-bold mb-6">Scalable & Secure by Design</h2>
              <p className="text-lg text-muted-foreground mb-6">
                Heylo dibangun dengan teknologi modern dan standar keamanan enterprise. Kamu punya kontrol penuh atas knowledge base, channel yang aktif, dan prompt yang digunakan.
              </p>
              
              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="mr-3 mt-1 bg-primary/10 p-1 rounded-full">
                    <Lock className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <h4 className="font-medium">Data Security</h4>
                    <p className="text-muted-foreground">Enkripsi end-to-end dan penyimpanan data yang aman</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="mr-3 mt-1 bg-primary/10 p-1 rounded-full">
                    <Zap className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <h4 className="font-medium">High Performance</h4>
                    <p className="text-muted-foreground">Arsitektur yang dioptimalkan untuk respons cepat</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="mr-3 mt-1 bg-primary/10 p-1 rounded-full">
                    <Globe className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <h4 className="font-medium">Scalable Infrastructure</h4>
                    <p className="text-muted-foreground">Siap menangani 1 atau 100 AI agent dengan performa optimal</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="md:w-1/2">
              <Image 
                src="/securitynew.png" 
                alt="Security Illustration" 
                width={500} 
                height={400}
                className="mx-auto"
              />
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 relative z-10">

        <div className="container mx-auto max-w-4xl text-center relative z-10">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">Mulai Sekarang — Try It for Free!</h2>
          <p className="text-lg text-muted-foreground mb-10 max-w-3xl mx-auto">
            Kamu bisa langsung coba bikin AI Agent pertama tanpa biaya. No credit card needed. No hidden fees.
          </p>
          <Button size="lg" asChild className="px-8">
            <Link href={accessToken ? "/dashboard" : "/register"}>
              🚀 Coba Gratis Sekarang
            </Link>
          </Button>
          <p className="mt-6 text-muted-foreground">
            Masih ada pertanyaan? Tim support kami ready bantu kamu.
          </p>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t py-12 px-4 relative z-10">

        <div className="container mx-auto relative z-10">
          <div className="flex flex-col md:flex-row justify-between md:items-center">
            {/* Logo and Description */}
            <div className="mb-6 md:mb-0 text-center md:text-left">
              {mounted && (
                <Image
                  src={theme === 'dark' ? '/logowhite.svg' : '/logoblack.svg'}
                  alt="Heylo Logo"
                  width={100}
                  height={30}
                  className="mx-auto md:mx-0"
                />
              )}
              <p className="text-muted-foreground mt-2 text-sm md:text-base">
                Cara baru build AI Agent. Lebih simple, lebih powerful, lebih cepat.
              </p>

              {/* Social Media & Links */}
              <div className="mt-4">
                {/* Mobile: Stacked layout */}
                <div className="md:hidden space-y-2">
                  <p className="text-sm text-muted-foreground">Ikuti kami:</p>
                  <div className="flex items-center justify-center gap-4">
                    <Link
                      href="https://www.instagram.com/heylo.network"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-muted-foreground hover:text-primary transition-colors flex items-center gap-2"
                    >
                      <Image
                        src="/instagram-icon.png"
                        alt="Instagram"
                        width={16}
                        height={16}
                        className="opacity-70 hover:opacity-100 transition-opacity"
                      />
                      <span className="text-sm">Instagram</span>
                    </Link>
                    <Link
                      href="https://artikel.heylo.co.id"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-muted-foreground hover:text-primary transition-colors flex items-center gap-2"
                    >
                      <Image
                        src="/web-icon.png"
                        alt="Artikel"
                        width={16}
                        height={16}
                        className="opacity-70 hover:opacity-100 transition-opacity"
                      />
                      <span className="text-sm">Artikel</span>
                    </Link>
                  </div>
                </div>

                {/* Desktop: Inline layout */}
                <div className="hidden md:flex items-center gap-4 flex-wrap">
                  <span className="text-sm text-muted-foreground">Ikuti kami:</span>
                  <Link
                    href="https://www.instagram.com/heylo.network"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-muted-foreground hover:text-primary transition-colors flex items-center gap-2"
                  >
                    <Image
                      src="/instagram-icon.png"
                      alt="Instagram"
                      width={16}
                      height={16}
                      className="opacity-70 hover:opacity-100 transition-opacity"
                    />
                    <span className="text-sm">Instagram</span>
                  </Link>
                  <Link
                    href="https://artikel.heylo.co.id"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-muted-foreground hover:text-primary transition-colors flex items-center gap-2"
                  >
                    <Image
                      src="/web-icon.png"
                      alt="Artikel"
                      width={16}
                      height={16}
                      className="opacity-70 hover:opacity-100 transition-opacity"
                    />
                    <span className="text-sm">Artikel</span>
                  </Link>
                </div>
              </div>
            </div>

            {/* Navigation Links */}
            <div className="mt-6 md:mt-0">
              {/* Mobile: Vertical layout */}
              <div className="flex flex-col space-y-2 text-center md:hidden">
                <Link href="/privacy" className="text-muted-foreground hover:text-foreground transition-colors text-sm">
                  Kebijakan Privasi
                </Link>
                <Link href="/terms" className="text-muted-foreground hover:text-foreground transition-colors text-sm">
                  Ketentuan Layanan
                </Link>
                <Link href="https://wa.me/628998736040" className="text-muted-foreground hover:text-foreground transition-colors text-sm">
                  Hubungi Kami
                </Link>
              </div>

              {/* Desktop: Horizontal layout */}
              <div className="hidden md:flex md:space-x-6">
                <Link href="/privacy" className="text-muted-foreground hover:text-foreground transition-colors">
                  Kebijakan Privasi
                </Link>
                <Link href="/terms" className="text-muted-foreground hover:text-foreground transition-colors">
                  Ketentuan Layanan
                </Link>
                <Link href="https://api.whatsapp.com/send?phone=6285647185683&text=Hi%20Heylo%20Support!%20Saya%20butuh%20bantuan%20nih%20%3A)" className="text-muted-foreground hover:text-foreground transition-colors">
                  Hubungi Kami
                </Link>
              </div>
            </div>
          </div>
          
          <div className="mt-8 pt-8 border-t text-center text-muted-foreground text-sm">
            © {new Date().getFullYear()} Heylo Network by PT Sandyakala Group Asia. All rights reserved.
          </div>
        </div>
      </footer>
    </div>
  );
}
