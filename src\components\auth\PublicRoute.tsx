"use client";

import { useAuth } from "@/contexts/AuthContext";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

interface PublicRouteProps {
  children: React.ReactNode;
}

/**
 * PublicRoute component that redirects authenticated users away from public pages
 * Use this for pages like login, register that should not be accessible when logged in
 */
export default function PublicRoute({ children }: PublicRouteProps) {
  const { accessToken, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // Only redirect after auth state is determined (not loading)
    // and if user is authenticated (has token)
    if (!isLoading && accessToken) {
      router.replace("/dashboard");
    }
  }, [accessToken, isLoading, router]);

  // Show nothing while checking authentication or during redirect
  if (isLoading || accessToken) {
    return (
      <div className="flex min-h-svh items-center justify-center">
        <div className="animate-pulse text-center">
          <div className="text-lg font-medium">Loading...</div>
          <p className="text-sm text-muted-foreground">Redirecting to dashboard</p>
        </div>
      </div>
    );
  }

  // If user is not authenticated, show the children (login/register page)
  return <>{children}</>;
}
