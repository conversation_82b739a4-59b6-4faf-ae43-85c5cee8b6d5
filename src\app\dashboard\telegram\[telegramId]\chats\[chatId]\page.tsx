"use client";

import React, { useState, useEffect, useCallback, useRef } from 'react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { AppSidebar } from '@/components/app-sidebar';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Separator } from '@/components/ui/separator';
import { SidebarInset, SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious
} from '@/components/ui/pagination';
import { ArrowLeft, MessageSquare, AlertTriangle, Loader2, User, Bot, Calendar, RefreshCw, Copy, ChevronDown } from 'lucide-react';
import { toast } from "sonner";
import { Toaster } from "@/components/ui/sonner";
import { cn } from '@/lib/utils';

interface Message {
  id: string;
  chatId: string;
  role: 'user' | 'assistant';
  content: string;
  createdAt: string;
  updatedAt: string;
}

interface Chat {
  id: string;
  telegramId: string;
  chatId: string;
  threadId: string;
  assistantId: string;
  autoReplyEnabled: boolean;
  createdAt: string;
  updatedAt: string;
}

interface PaginatedResponse<T> {
  data: T[];
  totalItems: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
}

export default function TelegramChatDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { accessToken } = useAuth();
  const telegramId = params.telegramId as string;
  const chatId = params.chatId as string;
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const [messages, setMessages] = useState<Message[]>([]);
  const [chat, setChat] = useState<Chat | null>(null);
  const [telegramName, setTelegramName] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [showScrollButton, setShowScrollButton] = useState(false);

  // Fetch messages with pagination
  const fetchMessages = useCallback(async (isRefresh = false) => {
    if (!accessToken || !telegramId || !chatId) {
      setIsLoading(false);
      return;
    }

    if (isRefresh) {
      setIsRefreshing(true);
    } else {
      setIsLoading(true);
    }
    setError(null);

    try {
      // Fetch messages using database ID with pagination
      const messagesResponse = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/telegram/${telegramId}/chats/${chatId}/messages?page=${currentPage}&limit=${itemsPerPage}`,
        {
          headers: { Authorization: `Bearer ${accessToken}` },
          cache: 'no-store'
        }
      );

      if (!messagesResponse.ok) {
        const errorData = await messagesResponse.json().catch(() => ({ message: 'Gagal mengambil pesan.' }));
        throw new Error(errorData.message || 'Gagal mengambil pesan.');
      }

      const messagesData: PaginatedResponse<Message> = await messagesResponse.json();
      // Sort messages by createdAt timestamp to ensure correct chronological order
      const sortedMessages = [...messagesData.data].sort((a, b) => {
        return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
      });
      setMessages(sortedMessages);
      setTotalPages(messagesData.totalPages);
      setCurrentPage(messagesData.currentPage);

      if (isRefresh) {
        toast.success("Data percakapan telah diperbarui", {
          description: "Berhasil memperbarui pesan",
          duration: 3000
        });
      }
    } catch (err: any) {
      setError(err.message);
      setMessages([]);

      if (isRefresh) {
        toast.error("Gagal memperbarui", {
          description: err.message,
          duration: 3000
        });
      }
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  }, [accessToken, telegramId, chatId, currentPage, itemsPerPage]);

  // Fetch telegram name and chat details
  const fetchChatDetails = useCallback(async () => {
    if (!accessToken || !telegramId || !chatId) return;

    try {
      // Fetch telegram instance name
      const telegramResponse = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/telegram/${telegramId}`,
        {
          headers: { Authorization: `Bearer ${accessToken}` },
        }
      );

      if (telegramResponse.ok) {
        const telegramData = await telegramResponse.json();
        setTelegramName(telegramData.name || 'Bot Telegram');
      }

      // Fetch chat details from the chats list endpoint to get chat info
      const chatsResponse = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/telegram/${telegramId}/chats`,
        {
          headers: { Authorization: `Bearer ${accessToken}` },
        }
      );

      if (chatsResponse.ok) {
        const chatsData: PaginatedResponse<Chat> = await chatsResponse.json();
        const foundChat = chatsData.data.find(chat => chat.id === chatId);
        if (foundChat) {
          setChat(foundChat);
        }
      }
    } catch (err: any) {
      console.error('Error fetching chat details:', err);
    }
  }, [accessToken, telegramId, chatId]);



  // Handle scroll to bottom button visibility
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;
    setShowScrollButton(!isNearBottom);
  }, []);

  // Scroll to bottom of messages
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  // Copy message content to clipboard
  const copyMessageToClipboard = useCallback((content: string) => {
    navigator.clipboard.writeText(content).then(() => {
      toast.success("Disalin", {
        description: "Pesan telah disalin ke clipboard",
        duration: 2000
      });
    });
  }, []);

  // Refresh messages
  const refreshMessages = useCallback(() => {
    fetchMessages(true);
  }, [fetchMessages]);

  // Format date to local string
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const isToday = date.toDateString() === now.toDateString();

    if (isToday) {
      return date.toLocaleTimeString('id-ID', {
        hour: '2-digit',
        minute: '2-digit'
      });
    }

    return date.toLocaleString('id-ID', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Group messages by date
  const groupMessagesByDate = useCallback((messages: Message[]) => {
    const groups: { [key: string]: Message[] } = {};

    messages.forEach(message => {
      const date = new Date(message.createdAt).toLocaleDateString('id-ID');
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(message);
    });

    return Object.entries(groups).map(([date, messages]) => ({
      date,
      messages
    }));
  }, []);

  useEffect(() => {
    fetchChatDetails();
  }, [fetchChatDetails]);

  useEffect(() => {
    fetchMessages();
  }, [fetchMessages, currentPage, itemsPerPage]);



  return (
    <SidebarProvider>
      <Toaster />
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b">
          <div className="flex items-center gap-2 px-4 w-full overflow-x-auto scrollbar-hide">
            <SidebarTrigger className="-ml-1 flex-shrink-0" />
            <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4 flex-shrink-0" />
            <Breadcrumb className="overflow-x-auto scrollbar-hide">
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block flex-shrink-0">
                  <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block flex-shrink-0" />
                <BreadcrumbItem className="flex-shrink-0">
                  <BreadcrumbLink href="/dashboard/telegram">Telegram</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden sm:block flex-shrink-0" />
                <BreadcrumbItem className="hidden sm:block flex-shrink-0">
                  <BreadcrumbLink href="/dashboard/telegram/chats">Percakapan</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden sm:block flex-shrink-0" />
                <BreadcrumbItem className="flex-shrink-0">
                  <BreadcrumbPage>Detail</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>

        <main className="flex-1 p-4 md:p-6 space-y-6 flex flex-col h-[calc(100vh-4rem)]">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex items-center gap-2 w-full sm:w-auto">
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.back()}
                className="flex-shrink-0"
              >
                <ArrowLeft className="h-4 w-4 mr-1" /> Kembali
              </Button>
              <h1 className="text-xl sm:text-2xl font-bold tracking-tight truncate">Detail Percakapan</h1>
            </div>

            <div className="flex items-center gap-2 w-full sm:w-auto justify-end">
              <Button
                variant="outline"
                size="sm"
                onClick={() => refreshMessages()}
                disabled={isRefreshing}
                className="flex-shrink-0"
              >
                <RefreshCw className={cn("h-4 w-4 mr-1", isRefreshing && "animate-spin")} />
                {isRefreshing ? "Memperbarui..." : "Perbarui"}
              </Button>
            </div>
          </div>

          {/* Bot info */}
          <div className="text-sm text-muted-foreground">
            Bot: {telegramName} • {messages.length} pesan
          </div>

          {/* Pagination and items per page selector */}
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="w-full sm:w-48">
              <Select
                value={itemsPerPage.toString()}
                onValueChange={(value) => setItemsPerPage(parseInt(value))}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Item per halaman" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5 pesan</SelectItem>
                  <SelectItem value="10">10 pesan</SelectItem>
                  <SelectItem value="20">20 pesan</SelectItem>
                  <SelectItem value="50">50 pesan</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {totalPages > 1 && (
              <div className="flex justify-center w-full sm:w-auto">
                <Pagination>
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious
                        href="#"
                        onClick={(e) => {
                          e.preventDefault();
                          if (currentPage > 1) setCurrentPage(currentPage - 1);
                        }}
                        className={currentPage <= 1 ? "pointer-events-none opacity-50" : ""}
                      />
                    </PaginationItem>

                    {Array.from({ length: totalPages }, (_, i) => i + 1)
                      .filter(page => {
                        // Show fewer pages on mobile
                        const isMobile = typeof window !== 'undefined' && window.innerWidth < 640;
                        if (isMobile) {
                          return page === 1 ||
                                page === totalPages ||
                                page === currentPage;
                        }
                        // Show more pages on desktop
                        return page === 1 ||
                              page === totalPages ||
                              (page >= currentPage - 1 && page <= currentPage + 1);
                      })
                      .map((page, index, array) => {
                        // Add ellipsis
                        const showEllipsisBefore = index > 0 && array[index - 1] !== page - 1;
                        const showEllipsisAfter = index < array.length - 1 && array[index + 1] !== page + 1;

                        return (
                          <React.Fragment key={page}>
                            {showEllipsisBefore && (
                              <PaginationItem className="hidden sm:flex">
                                <span className="px-4 py-2">...</span>
                              </PaginationItem>
                            )}

                            <PaginationItem>
                              <PaginationLink
                                href="#"
                                onClick={(e) => {
                                  e.preventDefault();
                                  setCurrentPage(page);
                                }}
                                isActive={page === currentPage}
                              >
                                {page}
                              </PaginationLink>
                            </PaginationItem>

                            {showEllipsisAfter && (
                              <PaginationItem className="hidden sm:flex">
                                <span className="px-4 py-2">...</span>
                              </PaginationItem>
                            )}
                          </React.Fragment>
                        );
                      })}

                    <PaginationItem>
                      <PaginationNext
                        href="#"
                        onClick={(e) => {
                          e.preventDefault();
                          if (currentPage < totalPages) setCurrentPage(currentPage + 1);
                        }}
                        className={currentPage >= totalPages ? "pointer-events-none opacity-50" : ""}
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </div>
            )}
          </div>



          {isLoading && (
            <div className="flex-1 overflow-hidden">
              <div className="space-y-4 p-1">
                {Array.from({ length: 5 }).map((_, index) => (
                  <div
                    key={index}
                    className={`flex ${index % 2 === 0 ? 'justify-start' : 'justify-end'}`}
                  >
                    <div
                      className={`max-w-[55%] sm:max-w-[50%] md:max-w-[45%] p-2 rounded-lg ${index % 2 === 0 ? 'bg-muted' : 'bg-primary/20'} shadow-sm`}
                    >
                      <Skeleton className="h-3 w-16 mb-1.5" />
                      <Skeleton className="h-12 w-full mb-1.5" />
                      <Skeleton className="h-2.5 w-20 mt-1.5" />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {error && !isLoading && (
            <Card className="border-destructive bg-destructive/10">
              <CardHeader className="flex flex-row items-center space-x-3 pb-2">
                <AlertTriangle className="h-6 w-6 text-destructive" />
                <CardTitle className="text-destructive">Gagal Memuat Data</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-destructive/80">Terjadi kesalahan: {error}</p>
                <Button variant="outline" size="sm" onClick={() => fetchMessages()} className="mt-4">
                  Coba Lagi
                </Button>
              </CardContent>
            </Card>
          )}

          {!isLoading && !error && messages.length === 0 && (
            <Card className="text-center">
              <CardHeader>
                <CardTitle>Tidak Ada Pesan</CardTitle>
                <CardDescription>
                  Percakapan ini belum memiliki pesan.
                </CardDescription>
              </CardHeader>
            </Card>
          )}

          {!isLoading && !error && messages.length > 0 && (
            <div className="flex-1 overflow-y-auto pr-1" onScroll={handleScroll}>
              <div className="space-y-4 pb-4 px-2">
                {groupMessagesByDate(messages).map(group => (
                  <div key={group.date} className="space-y-3">
                    <div className="sticky top-0 z-10 flex justify-center my-2">
                      <div className="bg-background/80 backdrop-blur-sm px-2.5 py-0.5 rounded-full text-xs text-muted-foreground border">
                        {group.date}
                      </div>
                    </div>

                    {group.messages.map((message, index) => {
                      const isConsecutive = index > 0 &&
                        group.messages[index - 1].role === message.role;

                      return (
                        <div
                          key={message.id}
                          className={`flex ${message.role === 'user' ? 'justify-start' : 'justify-end'}`}
                        >
                          <div
                            className={`max-w-[55%] sm:max-w-[50%] md:max-w-[45%] p-2 rounded-lg ${
                              message.role === 'user'
                                ? 'bg-muted text-foreground hover:bg-muted/80'
                                : 'bg-primary text-primary-foreground hover:bg-primary/90'
                            } relative group transition-colors shadow-sm`}
                          >
                            {!isConsecutive && (
                              <div className="text-xs font-medium mb-1">
                                {message.role === 'user' ? 'Pengguna' : 'Asisten'}
                              </div>
                            )}
                            <div className="text-sm whitespace-pre-wrap break-words">{message.content}</div>
                            <div className="text-xs mt-1.5 opacity-70 flex justify-between items-center">
                              <span>{formatDate(message.createdAt)}</span>
                              <button
                                onClick={() => copyMessageToClipboard(message.content)}
                                className="opacity-0 group-hover:opacity-100 transition-opacity p-1 hover:bg-background/20 rounded"
                                aria-label="Salin pesan"
                              >
                                <Copy className="h-3 w-3" />
                              </button>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </div>

              {showScrollButton && (
                <button
                  onClick={scrollToBottom}
                  className="fixed bottom-20 right-6 bg-primary text-primary-foreground p-2 rounded-full shadow-lg hover:bg-primary/90 transition-all z-10"
                  aria-label="Scroll to bottom"
                >
                  <ChevronDown className="h-5 w-5" />
                </button>
              )}
            </div>
          )}
        </main>
      </SidebarInset>
    </SidebarProvider>
  );
}
