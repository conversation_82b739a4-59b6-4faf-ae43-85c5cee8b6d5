import { NextRequest, NextResponse } from 'next/server';

// Firecrawl API wrapper
async function crawlUrl(url: string, apiKey: string) {
  try {
    // Pastikan URL memiliki protokol (http:// atau https://)
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://' + url;
    }
    
    console.log(`Crawling URL: ${url}`);
    
    // Menggunakan endpoint v1 yang benar berdasarkan dokumentasi terbaru
    // Pastikan format request sesuai dengan contoh di dokumentasi
    const requestBody = {
      url: url,
      formats: ['markdown']
    };
    
    console.log('Request body:', JSON.stringify(requestBody));
    
    const response = await fetch('https://api.firecrawl.dev/v1/scrape', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify(requestBody)
    });

    // Handle non-JSON responses (like HTML error pages)
    const contentType = response.headers.get('content-type');
    
    if (!response.ok) {
      console.error(`Error response status: ${response.status}`);
      
      // Check if response is JSON before trying to parse it
      if (contentType && contentType.includes('application/json')) {
        try {
          const errorData = await response.json();
          console.error('Error response body:', JSON.stringify(errorData));
          throw new Error(errorData.error || errorData.message || `Gagal melakukan crawling: ${response.status}`);
        } catch (parseError) {
          console.error('Failed to parse error JSON:', parseError);
          throw new Error(`Gagal melakukan crawling: ${response.status}`);
        }
      } else {
        // For non-JSON errors (like HTML responses)
        try {
          const textError = await response.text();
          console.error('Non-JSON error response:', textError.substring(0, 200) + '...');
          throw new Error(`Gagal melakukan crawling: ${response.status} - Server tidak mengembalikan respons JSON yang valid`);
        } catch (textError) {
          console.error('Failed to get error text:', textError);
          throw new Error(`Gagal melakukan crawling: ${response.status}`);
        }
      }
    }

    // Verify the response is actually JSON before parsing
    if (!contentType || !contentType.includes('application/json')) {
      const textResponse = await response.text();
      console.error('Unexpected non-JSON response:', textResponse.substring(0, 200) + '...');
      throw new Error('Server mengembalikan respons non-JSON yang tidak diharapkan');
    }

    const responseData = await response.json();
    console.log('FireCrawl API response:', JSON.stringify(responseData).substring(0, 200) + '...');
    return responseData;
  } catch (error: any) {
    console.error('Error crawling URL:', error);
    throw error;
  }
}

export async function POST(request: NextRequest) {
  try {
    const { url } = await request.json();
    
    if (!url) {
      return NextResponse.json({ error: 'URL diperlukan' }, { status: 400 });
    }

    const apiKey = process.env.FIRECRAWL_API_KEY;
    
    if (!apiKey) {
      return NextResponse.json({ error: 'API key tidak ditemukan' }, { status: 500 });
    }
    
    // Log API key length and first/last few characters for debugging (jangan tampilkan seluruh key)
    if (apiKey) {
      const keyLength = apiKey.length;
      const firstChars = apiKey.substring(0, 4);
      const lastChars = apiKey.substring(keyLength - 4);
      console.log(`API Key length: ${keyLength}, format: ${firstChars}...${lastChars}`);
    }
    
    // Validate URL format - lebih permisif, perbaiki jika perlu
    let validatedUrl = url;
    try {
      // Tambahkan protokol jika tidak ada
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        validatedUrl = 'https://' + url;
      }
      new URL(validatedUrl);
    } catch (e) {
      return NextResponse.json({ error: 'URL tidak valid' }, { status: 400 });
    }

    const crawlResult = await crawlUrl(validatedUrl, apiKey);
    
    // Berdasarkan dokumentasi terbaru, struktur respons FireCrawl adalah:
    // { success: boolean, data: { markdown: string, metadata: {...} } }
    
    console.log('FireCrawl response structure:', Object.keys(crawlResult).join(', '));
    
    let markdownContent = '';
    
    // Cek apakah respons memiliki format yang diharapkan berdasarkan dokumentasi terbaru
    if (crawlResult.success && crawlResult.data && crawlResult.data.markdown) {
      // Format respons untuk v1/scrape endpoint dengan format markdown
      markdownContent = crawlResult.data.markdown;
    } else if (crawlResult.data && Array.isArray(crawlResult.data) && crawlResult.data.length > 0 && crawlResult.data[0].markdown) {
      // Format respons alternatif (untuk kompatibilitas)
      markdownContent = crawlResult.data[0].markdown;
    } else if (crawlResult.markdown) {
      // Format respons lama (untuk kompatibilitas)
      markdownContent = crawlResult.markdown;
    } else {
      console.error('Unexpected FireCrawl response structure:', JSON.stringify(crawlResult).substring(0, 200));
      return NextResponse.json({ error: 'Format respons dari FireCrawl tidak sesuai yang diharapkan' }, { status: 500 });
    }
    
    if (!markdownContent) {
      return NextResponse.json({ error: 'Konten markdown kosong dari hasil crawling' }, { status: 500 });
    }

    // Generate a filename based on the URL
    const urlObj = new URL(url);
    const hostname = urlObj.hostname.replace(/\./g, '_');
    const filename = `${hostname}_${Date.now()}.txt`;

    return NextResponse.json({
      content: markdownContent,
      filename,
      url
    });
  } catch (error: any) {
    console.error('Error in crawl-url API route:', error);
    return NextResponse.json({ error: error.message || 'Terjadi kesalahan saat crawling URL' }, { status: 500 });
  }
}
