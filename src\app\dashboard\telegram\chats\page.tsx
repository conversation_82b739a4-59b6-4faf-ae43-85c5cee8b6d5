"use client";

import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { AppSidebar } from '@/components/app-sidebar';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Separator } from '@/components/ui/separator';
import { SidebarInset, SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>2, <PERSON><PERSON><PERSON><PERSON>gle, MessageSquare, Eye, Download } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Toaster } from "@/components/ui/sonner";

interface TelegramInstance {
  id: string;
  name: string;
  username: string;
}

interface Chat {
  id: string;
  telegramId: string;
  chatId: string;
  threadId: string;
  assistantId: string;
  autoReplyEnabled: boolean;
  createdAt: string;
  updatedAt: string;
  messageCount: number;
  latestMessage?: {
    userId: string;
    role: 'user' | 'assistant';
    content: string;
    createdAt: string;
  };
}

interface PaginatedResponse<T> {
  data: T[];
  totalItems: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
}

export default function TelegramChatsPage() {
  const { accessToken } = useAuth();
  const [instances, setInstances] = useState<TelegramInstance[]>([]);
  const [selectedInstance, setSelectedInstance] = useState<string>('');
  const [chats, setChats] = useState<Chat[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingChats, setIsLoadingChats] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const itemsPerPage = 20;

  const fetchInstances = useCallback(async () => {
    if (!accessToken) {
      setIsLoading(false);
      setError("Autentikasi diperlukan.");
      return;
    }
    
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/telegram`, {
        headers: { Authorization: `Bearer ${accessToken}` },
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Gagal mengambil data bot Telegram.' }));
        throw new Error(errorData.message || 'Gagal mengambil data bot Telegram.');
      }
      
      const data: TelegramInstance[] = await response.json();
      setInstances(data);
      
      // Auto-select first instance if available
      if (data.length > 0 && !selectedInstance) {
        setSelectedInstance(data[0].id);
      }
    } catch (err: any) {
      setError(err.message);
      setInstances([]);
    } finally {
      setIsLoading(false);
    }
  }, [accessToken, selectedInstance]);

  const fetchChats = useCallback(async () => {
    if (!accessToken || !selectedInstance) {
      setChats([]);
      return;
    }
    
    setIsLoadingChats(true);
    setError(null);
    
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/telegram/${selectedInstance}/chats?page=${currentPage}&limit=${itemsPerPage}`, 
        {
          headers: { Authorization: `Bearer ${accessToken}` },
        }
      );
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Gagal mengambil data percakapan.' }));
        throw new Error(errorData.message || 'Gagal mengambil data percakapan.');
      }
      
      const data: PaginatedResponse<Chat> = await response.json();
      
      // Validate and sanitize the data
      const sanitizedChats = data.data.map(chat => ({
        ...chat,
        // Ensure all required fields have fallback values
        messageCount: chat.messageCount || 0,
        autoReplyEnabled: !!chat.autoReplyEnabled,
        latestMessage: chat.latestMessage ? {
          ...chat.latestMessage,
          content: chat.latestMessage.content || '',
          role: chat.latestMessage.role || 'user',
          createdAt: chat.latestMessage.createdAt || chat.createdAt || new Date().toISOString()
        } : undefined
      }));
      
      setChats(sanitizedChats);
      setTotalPages(data.totalPages || 1);
      setCurrentPage(data.currentPage || 1);
    } catch (err: any) {
      console.error('Error fetching chats:', err);
      setError(err.message || 'Terjadi kesalahan saat mengambil data percakapan');
      setChats([]);
    } finally {
      setIsLoadingChats(false);
    }
  }, [accessToken, selectedInstance, currentPage, itemsPerPage]);

  useEffect(() => {
    fetchInstances();
  }, [fetchInstances]);

  useEffect(() => {
    if (selectedInstance) {
      setCurrentPage(1); // Reset to first page when changing instance
    }
  }, [selectedInstance]);

  useEffect(() => {
    fetchChats();
  }, [fetchChats]);

  const handleInstanceChange = (instanceId: string) => {
    setSelectedInstance(instanceId);
    setChats([]);
    setCurrentPage(1);
  };

  // Helper function to truncate message content
  const truncateMessage = (content: string | null | undefined, maxLength: number = 50) => {
    if (!content) return 'No content';
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  // Helper function to format date
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'Unknown date';
    
    try {
      const date = new Date(dateString);
      
      // Check if date is valid
      if (isNaN(date.getTime())) {
        return 'Invalid date';
      }
      
      const now = new Date();
      const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

      if (diffInHours < 24) {
        return date.toLocaleTimeString('id-ID', {
          hour: '2-digit',
          minute: '2-digit'
        });
      } else if (diffInHours < 24 * 7) {
        return date.toLocaleDateString('id-ID', {
          weekday: 'short',
          hour: '2-digit',
          minute: '2-digit'
        });
      } else {
        return date.toLocaleDateString('id-ID', {
          day: '2-digit',
          month: 'short',
          year: 'numeric'
        });
      }
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Date error';
    }
  };



  return (
    <React.Fragment>
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbLink href="/dashboard/telegram">Pengaturan Telegram</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>Histori Percakapan</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>

        <main className="flex-1 p-4 md:p-6 space-y-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-2xl font-semibold">Histori Percakapan Telegram</h1>
              <p className="text-muted-foreground">
                Lihat dan kelola semua percakapan dari bot Telegram Anda.
              </p>
            </div>
            <Link href="/dashboard/telegram" passHref>
              <Button variant="outline">
                <ArrowLeft className="mr-2 h-4 w-4" /> Kembali ke Pengaturan
              </Button>
            </Link>
          </div>

          {/* Instance Selector */}
          <Card>
            <CardHeader>
              <CardTitle>Pilih Bot Telegram</CardTitle>
              <CardDescription>
                Pilih bot Telegram untuk melihat histori percakapannya.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center space-x-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Memuat bot Telegram...</span>
                </div>
              ) : instances.length === 0 ? (
                <div className="text-center py-4">
                  <p className="text-muted-foreground">Belum ada bot Telegram yang tersedia.</p>
                  <Link href="/dashboard/telegram/new" passHref>
                    <Button variant="outline" className="mt-2">
                      Buat Bot Telegram Pertama
                    </Button>
                  </Link>
                </div>
              ) : (
                <Select value={selectedInstance} onValueChange={handleInstanceChange}>
                  <SelectTrigger className="w-full max-w-md">
                    <SelectValue placeholder="Pilih bot Telegram" />
                  </SelectTrigger>
                  <SelectContent>
                    {instances.map((instance) => (
                      <SelectItem key={instance.id} value={instance.id}>
                        {instance.name} (@{instance.username})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </CardContent>
          </Card>

          {/* Chats Table */}
          {selectedInstance && (
            <Card>
              <CardHeader>
                <CardTitle>Daftar Percakapan</CardTitle>
                <CardDescription>
                  Percakapan dari bot Telegram yang dipilih.
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoadingChats ? (
                  <div className="flex items-center justify-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin mr-2" />
                    <span>Memuat percakapan...</span>
                  </div>
                ) : error ? (
                  <div className="flex items-center justify-center py-8 text-center">
                    <div>
                      <AlertTriangle className="h-8 w-8 text-destructive mx-auto mb-2" />
                      <p className="text-destructive font-medium">Terjadi Kesalahan</p>
                      <p className="text-muted-foreground">{error}</p>
                      <Button variant="outline" size="sm" onClick={fetchChats} className="mt-4">
                        Coba Lagi
                      </Button>
                    </div>
                  </div>
                ) : chats.length === 0 ? (
                  <div className="flex items-center justify-center py-8 text-center">
                    <div>
                      <MessageSquare className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                      <p className="font-medium">Belum Ada Percakapan</p>
                      <p className="text-muted-foreground">
                        Percakapan akan muncul di sini setelah ada pengguna yang mengirim pesan ke bot.
                      </p>
                    </div>
                  </div>
                ) : (
                  <>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>ID Chat</TableHead>
                          <TableHead>Pesan Terakhir</TableHead>
                          <TableHead>Waktu</TableHead>
                          <TableHead>Jumlah Pesan</TableHead>
                          <TableHead>Auto-Reply</TableHead>
                          <TableHead className="text-right">Aksi</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {chats.slice(0, 100).map((chat) => (
                          <TableRow key={chat.id}>
                            <TableCell className="font-medium">
                              {chat.id.substring(0, 8)}...
                            </TableCell>
                            <TableCell>
                              {chat.latestMessage ? (
                                <div>
                                  <div className="text-sm">
                                    {truncateMessage(chat.latestMessage.content)}
                                  </div>
                                  <div className="text-xs text-muted-foreground">
                                    {chat.latestMessage.role === 'user' ? 'Pengguna' : 'Asisten'}
                                  </div>
                                </div>
                              ) : (
                                <span className="text-muted-foreground">Belum ada pesan</span>
                              )}
                            </TableCell>
                            <TableCell>
                              {formatDate(chat.latestMessage?.createdAt || chat.createdAt)}
                            </TableCell>
                            <TableCell>{chat.messageCount || 0}</TableCell>
                            <TableCell>
                              <Badge variant={chat.autoReplyEnabled ? "default" : "secondary"}>
                                {chat.autoReplyEnabled ? "Aktif" : "Nonaktif"}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-right">
                              <Link href={`/dashboard/telegram/${selectedInstance}/chats/${chat.id}`} passHref>
                                <Button variant="outline" size="sm">
                                  <Eye className="mr-1 h-3 w-3" /> Lihat
                                </Button>
                              </Link>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>

                    {/* Pagination */}
                    {totalPages > 1 && (
                      <div className="flex items-center justify-between mt-4">
                        <p className="text-sm text-muted-foreground">
                          Halaman {currentPage} dari {totalPages}
                        </p>
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                            disabled={currentPage === 1}
                          >
                            Sebelumnya
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                            disabled={currentPage === totalPages}
                          >
                            Selanjutnya
                          </Button>
                        </div>
                      </div>
                    )}
                  </>
                )}
              </CardContent>
            </Card>
          )}
        </main>
        </SidebarInset>
      </SidebarProvider>
      <Toaster />
    </React.Fragment>
  );
}
