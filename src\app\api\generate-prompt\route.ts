import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { message } = await request.json();

    if (!message || typeof message !== 'string') {
      return NextResponse.json(
        { error: 'Message is required and must be a string' },
        { status: 400 }
      );
    }

    const webhookUrl = process.env.AI_PROMPTER_N8N_WEBHOOK_URL;
    
    if (!webhookUrl) {
      console.error('AI Prompter webhook URL not configured');
      return NextResponse.json(
        { error: 'AI Prompter service not available' },
        { status: 500 }
      );
    }

    // Call the N8N webhook
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ message }),
    });

    if (!response.ok) {
      console.error('AI Prompter webhook failed:', response.status, response.statusText);
      return NextResponse.json(
        { error: 'Failed to generate prompt' },
        { status: 500 }
      );
    }

    const data = await response.json();
    
    if (!data.output) {
      console.error('Invalid response from AI Prompter:', data);
      return NextResponse.json(
        { error: 'Invalid response from AI service' },
        { status: 500 }
      );
    }

    return NextResponse.json({ output: data.output });

  } catch (error) {
    console.error('Error in generate-prompt API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
